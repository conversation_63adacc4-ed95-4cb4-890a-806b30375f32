export const Typography = {
  // Font sizes
  fontSize: {
    xs: 12,
    sm: 14,
    base: 16,
    lg: 18,
    xl: 20,
    '2xl': 24,
    '3xl': 28,
    '4xl': 32,
    '5xl': 36,
  },

  // Line heights
  lineHeight: {
    xs: 16,
    sm: 20,
    base: 24,
    lg: 28,
    xl: 28,
    '2xl': 32,
    '3xl': 36,
    '4xl': 40,
    '5xl': 44,
  },

  // Font weights
  fontWeight: {
    normal: '400' as const,
    medium: '500' as const,
    semibold: '600' as const,
    bold: '700' as const,
  },

  // Text styles
  styles: {
    // Headings
    h1: {
      fontSize: 32,
      lineHeight: 40,
      fontWeight: 'bold' as const,
    },
    h2: {
      fontSize: 28,
      lineHeight: 36,
      fontWeight: 'bold' as const,
    },
    h3: {
      fontSize: 24,
      lineHeight: 32,
      fontWeight: 'bold' as const,
    },
    h4: {
      fontSize: 20,
      lineHeight: 28,
      fontWeight: 'bold' as const,
    },
    h5: {
      fontSize: 18,
      lineHeight: 28,
      fontWeight: 'semibold' as const,
    },
    h6: {
      fontSize: 16,
      lineHeight: 24,
      fontWeight: 'semibold' as const,
    },

    // Body text
    body: {
      fontSize: 16,
      lineHeight: 24,
      fontWeight: 'normal' as const,
    },
    bodySmall: {
      fontSize: 14,
      lineHeight: 20,
      fontWeight: 'normal' as const,
    },
    bodyLarge: {
      fontSize: 18,
      lineHeight: 28,
      fontWeight: 'normal' as const,
    },

    // UI elements
    button: {
      fontSize: 16,
      lineHeight: 24,
      fontWeight: 'semibold' as const,
    },
    buttonSmall: {
      fontSize: 14,
      lineHeight: 20,
      fontWeight: 'medium' as const,
    },
    buttonLarge: {
      fontSize: 18,
      lineHeight: 28,
      fontWeight: 'semibold' as const,
    },

    // Supporting text
    caption: {
      fontSize: 12,
      lineHeight: 16,
      fontWeight: 'normal' as const,
    },
    overline: {
      fontSize: 12,
      lineHeight: 16,
      fontWeight: 'medium' as const,
      textTransform: 'uppercase' as const,
      letterSpacing: 1,
    },

    // Special
    link: {
      fontSize: 16,
      lineHeight: 24,
      fontWeight: 'medium' as const,
      textDecorationLine: 'underline' as const,
    },
    label: {
      fontSize: 14,
      lineHeight: 20,
      fontWeight: 'medium' as const,
    },
  },
};