import React from 'react';
import { render, screen } from '@testing-library/react-native';
import { ThemedText } from '../../components/ThemedText';

// Mock the useThemeColor hook
jest.mock('@/hooks/useThemeColor', () => ({
  useThemeColor: jest.fn(() => '#000000'),
}));

describe('ThemedText Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render text content correctly', () => {
    render(<ThemedText>Hello World</ThemedText>);
    
    expect(screen.getByText('Hello World')).toBeTruthy();
  });

  it('should apply default type styles by default', () => {
    render(<ThemedText testID="themed-text">Default Text</ThemedText>);
    
    const textElement = screen.getByTestId('themed-text');
    expect(textElement.props.style).toContainEqual(
      expect.objectContaining({
        fontSize: 16,
        lineHeight: 24,
      })
    );
  });

  it('should apply title type styles correctly', () => {
    render(<ThemedText type="title" testID="title-text">Title Text</ThemedText>);
    
    const textElement = screen.getByTestId('title-text');
    expect(textElement.props.style).toContainEqual(
      expect.objectContaining({
        fontSize: 32,
        fontWeight: 'bold',
        lineHeight: 40,
      })
    );
  });

  it('should apply subtitle type styles correctly', () => {
    render(<ThemedText type="subtitle" testID="subtitle-text">Subtitle Text</ThemedText>);
    
    const textElement = screen.getByTestId('subtitle-text');
    expect(textElement.props.style).toContainEqual(
      expect.objectContaining({
        fontSize: 20,
        fontWeight: 'bold',
        lineHeight: 28,
      })
    );
  });

  it('should apply defaultSemiBold type styles correctly', () => {
    render(<ThemedText type="defaultSemiBold" testID="semibold-text">Semi Bold Text</ThemedText>);
    
    const textElement = screen.getByTestId('semibold-text');
    expect(textElement.props.style).toContainEqual(
      expect.objectContaining({
        fontSize: 16,
        lineHeight: 24,
        fontWeight: '600',
      })
    );
  });

  it('should apply link type styles correctly', () => {
    render(<ThemedText type="link" testID="link-text">Link Text</ThemedText>);
    
    const textElement = screen.getByTestId('link-text');
    expect(textElement.props.style).toContainEqual(
      expect.objectContaining({
        lineHeight: 30,
        fontSize: 16,
        color: '#0a7ea4',
      })
    );
  });

  it('should merge custom styles with type styles', () => {
    const customStyle = { marginTop: 10, fontSize: 18 };
    render(
      <ThemedText style={customStyle} testID="custom-styled-text">
        Custom Styled Text
      </ThemedText>
    );
    
    const textElement = screen.getByTestId('custom-styled-text');
    expect(textElement.props.style).toContainEqual(
      expect.objectContaining(customStyle)
    );
  });

  it('should forward all text props correctly', () => {
    const testProps = {
      numberOfLines: 2,
      ellipsizeMode: 'tail' as const,
      accessible: true,
      accessibilityLabel: 'Test accessibility label',
    };
    
    render(
      <ThemedText {...testProps} testID="forwarded-props-text">
        Text with forwarded props
      </ThemedText>
    );
    
    const textElement = screen.getByTestId('forwarded-props-text');
    expect(textElement.props.numberOfLines).toBe(2);
    expect(textElement.props.ellipsizeMode).toBe('tail');
    expect(textElement.props.accessible).toBe(true);
    expect(textElement.props.accessibilityLabel).toBe('Test accessibility label');
  });

  it('should use theme color from useThemeColor hook', () => {
    const mockUseThemeColor = require('@/hooks/useThemeColor').useThemeColor;
    mockUseThemeColor.mockReturnValue('#ff0000');
    
    render(<ThemedText testID="themed-color-text">Themed Color Text</ThemedText>);
    
    const textElement = screen.getByTestId('themed-color-text');
    expect(textElement.props.style).toContainEqual(
      expect.objectContaining({
        color: '#ff0000',
      })
    );
  });

  it('should handle custom light and dark colors', () => {
    const mockUseThemeColor = require('@/hooks/useThemeColor').useThemeColor;
    
    render(
      <ThemedText 
        lightColor="#ffffff" 
        darkColor="#000000" 
        testID="custom-color-text"
      >
        Custom Color Text
      </ThemedText>
    );
    
    expect(mockUseThemeColor).toHaveBeenCalledWith(
      { light: '#ffffff', dark: '#000000' },
      'text'
    );
  });
});