import {
  getWeekStartDate,
  isNewWeek,
  getNextMealPlanGenerationTime,
} from '../../lib/weekCalculation';

// Mock Date to control current time in tests
const mockDate = (dateString: string) => {
  const mockNow = new Date(dateString);
  global.Date = jest.fn(() => mockNow) as any;
  global.Date.UTC = Date.UTC;
  global.Date.prototype = Date.prototype;
  Object.setPrototypeOf(mockNow, Date.prototype);
};

describe('weekCalculation utilities', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('getWeekStartDate', () => {
    it('should return correct week start date for Friday cycle (default)', () => {
      // Mock current time as Friday, Jan 5, 2024 10:00 AM UTC
      mockDate('2024-01-05T10:00:00.000Z');
      
      const result = getWeekStartDate();
      
      // Should return the Sunday that started this week (Dec 31, 2023)
      expect(result).toBe('2023-12-31');
    });

    it('should return correct week start date for different cycle start days', () => {
      // Mock current time as Wednesday, Jan 3, 2024 10:00 AM UTC
      mockDate('2024-01-03T10:00:00.000Z');
      
      // Test Monday cycle start (day 1)
      const mondayResult = getWeekStartDate(1);
      expect(mondayResult).toBe('2024-01-01'); // Monday Jan 1, 2024
      
      // Test Sunday cycle start (day 0)
      const sundayResult = getWeekStartDate(0);
      expect(sundayResult).toBe('2023-12-31'); // Sunday Dec 31, 2023
    });

    it('should handle edge case when current day is cycle start day', () => {
      // Mock current time as Friday, Jan 5, 2024 (which is day 5)
      mockDate('2024-01-05T10:00:00.000Z');
      
      // Test Friday cycle start (day 5)
      const result = getWeekStartDate(5);
      
      // Should return the Sunday that started this week
      expect(result).toBe('2023-12-31');
    });

    it('should handle UTC calculations correctly', () => {
      // Mock current time just after midnight UTC on Saturday
      mockDate('2024-01-06T00:30:00.000Z');
      
      const result = getWeekStartDate(5); // Friday cycle
      
      // Should return the Sunday that started this week
      expect(result).toBe('2023-12-31');
    });
  });

  describe('isNewWeek', () => {
    it('should return true when in a new week', () => {
      // Mock current time as a new week
      mockDate('2024-01-12T10:00:00.000Z');
      
      const previousWeekStart = '2024-01-07';
      const result = isNewWeek(previousWeekStart, 5);
      
      expect(result).toBe(true);
    });

    it('should return false when in the same week', () => {
      // Mock current time within the same week
      mockDate('2024-01-10T10:00:00.000Z');
      
      const previousWeekStart = '2024-01-07';
      const result = isNewWeek(previousWeekStart, 5);
      
      expect(result).toBe(false);
    });
  });

  describe('getNextMealPlanGenerationTime', () => {
    it('should return correct message for same day generation', () => {
      // Mock current time as Friday before midnight
      mockDate('2024-01-05T23:30:00.000Z');
      
      const result = getNextMealPlanGenerationTime(5); // Friday cycle
      
      expect(result).toBe('Tonight at midnight UTC (Friday)');
    });

    it('should return correct message for next day generation', () => {
      // Mock current time as Thursday
      mockDate('2024-01-04T10:00:00.000Z');
      
      const result = getNextMealPlanGenerationTime(5); // Friday cycle
      
      expect(result).toBe('Tomorrow at midnight UTC (Friday)');
    });

    it('should return correct message for multiple days away', () => {
      // Mock current time as Monday
      mockDate('2024-01-01T10:00:00.000Z');
      
      const result = getNextMealPlanGenerationTime(5); // Friday cycle
      
      expect(result).toBe('In 4 days at midnight UTC (Friday)');
    });

    it('should handle different cycle start days correctly', () => {
      // Mock current time as Wednesday
      mockDate('2024-01-03T10:00:00.000Z');
      
      const result = getNextMealPlanGenerationTime(1); // Monday cycle
      
      expect(result).toBe('In 5 days at midnight UTC (Monday)');
    });
  });
});

describe('weekCalculation edge cases', () => {
  it('should handle year boundary correctly', () => {
    // Mock current time as January 1st (Sunday)
    mockDate('2024-01-01T10:00:00.000Z');
    
    const result = getWeekStartDate(5); // Friday cycle
    
    // Should return the previous Sunday (Dec 31, 2023)
    expect(result).toBe('2023-12-31');
  });

  it('should handle leap year correctly', () => {
    // Mock current time during a leap year
    mockDate('2024-02-29T10:00:00.000Z'); // Leap day Thursday
    
    const result = getWeekStartDate(5); // Friday cycle
    
    // Should return the Sunday that started this week
    expect(result).toBe('2024-02-25');
  });
});