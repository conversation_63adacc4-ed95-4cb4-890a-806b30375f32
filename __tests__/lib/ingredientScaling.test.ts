import { parseIngredientAmount, scaleIngredients } from '../../lib/ingredientScaling';

describe('ingredientScaling utilities', () => {
  describe('parseIngredientAmount', () => {
    it('should parse simple ingredient with amount and unit', () => {
      const result = parseIngredientAmount('2 cups flour');
      
      expect(result).toEqual({
        amount: 2,
        unit: 'cups',
        name: 'flour',
        isRange: false,
      });
    });

    it('should parse fractional amounts', () => {
      const result = parseIngredientAmount('1/2 cup sugar');
      
      expect(result).toEqual({
        amount: 0.5,
        unit: 'cup',
        name: 'sugar',
        isRange: false,
      });
    });

    it('should parse mixed numbers', () => {
      const result = parseIngredientAmount('1 1/2 cups milk');
      
      expect(result).toEqual({
        amount: 1.5,
        unit: 'cups',
        name: 'milk',
        isRange: false,
      });
    });

    it('should parse ingredient without unit', () => {
      const result = parseIngredientAmount('3 eggs');
      
      expect(result).toEqual({
        amount: 3,
        unit: 'eggs',
        name: 'eggs',
        isRange: false,
      });
    });

    it('should parse range amounts', () => {
      const result = parseIngredientAmount('2-3 cloves garlic');
      
      expect(result).toEqual({
        amount: 2,
        unit: 'cloves',
        name: 'garlic',
        isRange: true,
        rangeMax: 3,
      });
    });

    it('should handle ingredient with no amount', () => {
      const result = parseIngredientAmount('salt to taste');
      
      expect(result).toEqual({
        amount: 1,
        unit: '',
        name: 'salt to taste',
        isRange: false,
      });
    });

    it('should standardize unit variations', () => {
      const result = parseIngredientAmount('2 tablespoons olive oil');
      
      expect(result?.unit).toBe('tbsp');
    });

    it('should return null for invalid input', () => {
      const result = parseIngredientAmount('');
      
      expect(result).toBeNull();
    });
  });

  describe('scaleIngredients', () => {
    const mockIngredients = [
      '2 cups flour',
      '1/2 cup sugar',
      '3 eggs',
      '1 tsp vanilla extract',
      'Salt to taste',
    ];

    it('should scale ingredients for larger serving size', () => {
      const result = scaleIngredients(mockIngredients, 4, 8);
      
      expect(result).toHaveLength(5);
      expect(result[0].amount).toBe('4 cups'); // 2 * 2
      expect(result[1].amount).toBe('1 cup'); // 0.5 * 2
      expect(result[2].amount).toBe('6'); // 3 * 2
      expect(result[3].amount).toBe('2 tsp'); // 1 * 2
      expect(result[4].amount).toBe(''); // No amount ingredients stay empty
    });

    it('should scale ingredients for smaller serving size', () => {
      const result = scaleIngredients(mockIngredients, 4, 2);
      
      expect(result).toHaveLength(5);
      expect(result[0].amount).toBe('1 cup'); // 2 * 0.5
      expect(result[1].amount).toBe('1/4 cup'); // 0.5 * 0.5
      expect(result[2].amount).toBe('2'); // 3 * 0.5 rounded to whole
      expect(result[3].amount).toBe('1/2 tsp'); // 1 * 0.5
    });

    it('should handle same serving size (no scaling)', () => {
      const result = scaleIngredients(mockIngredients, 4, 4);
      
      expect(result).toHaveLength(5);
      expect(result[0].amount).toBe('2 cups');
      expect(result[1].amount).toBe('1/2 cup');
      expect(result[2].amount).toBe('3');
      expect(result[3].amount).toBe('1 tsp');
    });

    it('should preserve ingredient names correctly', () => {
      const result = scaleIngredients(mockIngredients, 4, 8);
      
      expect(result[0].name).toBe('flour');
      expect(result[1].name).toBe('sugar');
      expect(result[2].name).toBe('eggs');
      expect(result[3].name).toBe('vanilla extract');
      expect(result[4].name).toBe('Salt to taste');
    });

    it('should handle zero or invalid serving sizes', () => {
      const result = scaleIngredients(mockIngredients, 0, 4);
      
      // Should return original ingredients without scaling
      expect(result).toHaveLength(5);
      expect(result[0].amount).toBe('2 cups');
    });

    it('should handle range ingredients correctly', () => {
      const rangeIngredients = ['2-3 cloves garlic'];
      const result = scaleIngredients(rangeIngredients, 4, 8);
      
      expect(result[0].amount).toBe('4-6 cloves'); // Both min and max scaled
    });

    it('should handle fractional scaling with proper rounding', () => {
      const ingredients = ['3 eggs'];
      const result = scaleIngredients(ingredients, 6, 4); // Scale down by 2/3
      
      expect(result[0].amount).toBe('2'); // 3 * (4/6) = 2, rounded to whole for eggs
    });

    it('should preserve emojis from getIngredientEmoji', () => {
      const ingredients = ['2 cups flour'];
      const result = scaleIngredients(ingredients, 4, 8);
      
      expect(result[0]).toHaveProperty('emoji');
      expect(typeof result[0].emoji).toBe('string');
    });
  });

  describe('edge cases', () => {
    it('should handle very small scaled amounts', () => {
      const ingredients = ['1/4 tsp salt'];
      const result = scaleIngredients(ingredients, 8, 1); // Scale down dramatically
      
      expect(result[0].amount).toBe('pinch'); // Should convert very small amounts
    });

    it('should handle complex ingredient descriptions', () => {
      const ingredients = ['2 cups all-purpose flour, sifted'];
      const result = scaleIngredients(ingredients, 4, 8);
      
      expect(result[0].name).toBe('all-purpose flour, sifted');
    });

    it('should handle ingredients with parenthetical information', () => {
      const ingredients = ['1 cup butter (room temperature)'];
      const result = scaleIngredients(ingredients, 4, 8);
      
      expect(result[0].name).toBe('butter (room temperature)');
    });
  });
});