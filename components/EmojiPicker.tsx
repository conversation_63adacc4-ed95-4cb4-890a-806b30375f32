import React, { useState, useMemo } from 'react';
import {
  View,
  TouchableOpacity,
  Modal,
  Text,
  TextInput,
  ScrollView,
  StyleSheet,
  Pressable,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { FOOD_EMOJIS, getEmojisByCategory } from '@/lib/food-emojis';
import { Colors } from '@/constants/Colors';
import { useTheme } from '@/contexts/ThemeContext';

interface EmojiPickerProps {
  value?: string;
  onChange: (emoji: string) => void;
  placeholder?: string;
}

export function EmojiPicker({
  value,
  onChange,
  placeholder = 'Select emoji',
}: EmojiPickerProps) {
  const { activeTheme } = useTheme();
  const colors = Colors[activeTheme];
  const [isOpen, setIsOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>('All');
  const [searchTerm, setSearchTerm] = useState('');

  const categorizedEmojis = getEmojisByCategory();
  const categories = ['All', ...Object.keys(categorizedEmojis)];

  // Filter emojis based on search term and category
  const filteredEmojis = useMemo(() => {
    let emojis =
      selectedCategory === 'All'
        ? FOOD_EMOJIS
        : categorizedEmojis[selectedCategory] || [];

    if (searchTerm.trim()) {
      emojis = emojis.filter(
        (emoji) =>
          emoji.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          emoji.category.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    return emojis;
  }, [selectedCategory, searchTerm, categorizedEmojis]);

  const handleEmojiSelect = (emoji: string) => {
    onChange(emoji);
    setIsOpen(false);
    setSearchTerm('');
  };

  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);
    setSearchTerm('');
  };

  return (
    <>
      {/* Trigger Button */}
      <TouchableOpacity
        style={[
          styles.triggerButton,
          {
            borderColor: colors.border,
            backgroundColor: colors.cardBackground,
          },
        ]}
        onPress={() => setIsOpen(true)}
      >
        <Text style={[styles.emojiText, { color: colors.text }]}>
          {value || '🍽️'}
        </Text>
      </TouchableOpacity>

      {/* Modal */}
      <Modal
        visible={isOpen}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setIsOpen(false)}
      >
        <View style={styles.modalOverlay}>
          <Pressable
            style={styles.modalBackdrop}
            onPress={() => setIsOpen(false)}
          />
          <View
            style={[
              styles.modalContent,
              { backgroundColor: colors.cardBackground },
            ]}
          >
            <View
              style={[styles.modalHeader, { borderBottomColor: colors.border }]}
            >
              <Text style={[styles.modalTitle, { color: colors.text }]}>
                Select Emoji
              </Text>
              <TouchableOpacity onPress={() => setIsOpen(false)}>
                <Ionicons name="close" size={24} color={colors.text} />
              </TouchableOpacity>
            </View>

            {/* Search Input */}
            <View style={styles.searchContainer}>
              <Ionicons
                name="search"
                size={20}
                color={colors.text}
                style={{ opacity: 0.5 }}
              />
              <TextInput
                style={[styles.searchInput, { color: colors.text }]}
                placeholder="Search emojis..."
                placeholderTextColor={colors.text + '80'}
                value={searchTerm}
                onChangeText={setSearchTerm}
              />
            </View>

            {/* Category Tabs */}
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              style={[
                styles.categoryContainer,
                { borderBottomColor: colors.border },
              ]}
              contentContainerStyle={styles.categoryContent}
            >
              {categories.map((category) => (
                <TouchableOpacity
                  key={category}
                  onPress={() => handleCategoryChange(category)}
                  style={[
                    styles.categoryTab,
                    selectedCategory === category && [
                      styles.categoryTabActive,
                      { backgroundColor: colors.primary },
                    ],
                  ]}
                >
                  <Text
                    style={[
                      styles.categoryText,
                      {
                        color:
                          selectedCategory === category
                            ? colors.background
                            : colors.text,
                      },
                    ]}
                  >
                    {category}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>

            {/* Results Count */}
            {searchTerm.trim() && (
              <View style={styles.resultsCount}>
                <Text
                  style={[
                    styles.resultsText,
                    { color: colors.text, opacity: 0.6 },
                  ]}
                >
                  {filteredEmojis.length} emoji
                  {filteredEmojis.length !== 1 ? 's' : ''} found
                </Text>
              </View>
            )}

            {/* Emoji Grid */}
            <ScrollView
              style={styles.emojiScrollView}
              contentContainerStyle={styles.emojiGrid}
              showsVerticalScrollIndicator={false}
            >
              {filteredEmojis.map((item) => (
                <TouchableOpacity
                  key={item.emoji}
                  onPress={() => handleEmojiSelect(item.emoji)}
                  style={[
                    styles.emojiButton,
                    value === item.emoji && [
                      styles.emojiButtonActive,
                      {
                        backgroundColor: colors.primary + '20',
                        borderColor: colors.primary,
                      },
                    ],
                  ]}
                >
                  <Text style={styles.emoji}>{item.emoji}</Text>
                  <Text
                    style={[styles.emojiName, { color: colors.text }]}
                    numberOfLines={1}
                  >
                    {item.name}
                  </Text>
                </TouchableOpacity>
              ))}

              {filteredEmojis.length === 0 && (
                <View style={styles.emptyState}>
                  <Text
                    style={[
                      styles.emptyText,
                      { color: colors.text, opacity: 0.5 },
                    ]}
                  >
                    {searchTerm.trim()
                      ? `No emojis found matching "${searchTerm}"`
                      : 'No emojis found in this category'}
                  </Text>
                </View>
              )}
            </ScrollView>
          </View>
        </View>
      </Modal>
    </>
  );
}

const styles = StyleSheet.create({
  triggerButton: {
    width: 40,
    height: 40,
    borderWidth: 1,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emojiText: {
    fontSize: 20,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalBackdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '100%',
    height: '90%',
    borderRadius: 20,
    paddingBottom: 20,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    margin: 16,
    marginBottom: 0,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    gap: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    paddingVertical: 4,
  },
  categoryContainer: {
    borderBottomWidth: 1,
    paddingVertical: 8,
    maxHeight: 50,
  },
  categoryContent: {
    paddingHorizontal: 16,
    gap: 8,
  },
  categoryTab: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
  },
  categoryTabActive: {
    backgroundColor: '#FFE598',
  },
  categoryText: {
    fontSize: 13,
    fontWeight: '500',
  },
  resultsCount: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  resultsText: {
    fontSize: 12,
  },
  emojiScrollView: {
    flex: 1,
  },
  emojiGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    padding: 16,
    gap: 8,
  },
  emojiButton: {
    width: '22%',
    aspectRatio: 1,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
    padding: 4,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  emojiButtonActive: {
    borderWidth: 1,
  },
  emoji: {
    fontSize: 24,
    marginBottom: 2,
  },
  emojiName: {
    fontSize: 10,
    textAlign: 'center',
  },
  emptyState: {
    flex: 1,
    width: '100%',
    paddingVertical: 40,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 14,
    textAlign: 'center',
  },
});
