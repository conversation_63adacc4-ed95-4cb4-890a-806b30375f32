import { Text, type TextProps, StyleSheet } from 'react-native';
import { useThemeColor } from '@/hooks/useThemeColor';
import { Typography } from '@/constants/Typography';

export type ThemedTextProps = TextProps & {
  lightColor?: string;
  darkColor?: string;
  type?: 'default' | 'title' | 'defaultSemiBold' | 'subtitle' | 'link' | 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'body' | 'bodySmall' | 'bodyLarge' | 'caption' | 'label';
};

export function ThemedText({
  style,
  lightColor,
  darkColor,
  type = 'default',
  ...rest
}: ThemedTextProps) {
  const color = useThemeColor({ light: lightColor, dark: darkColor }, 'text');

  const getTextStyle = () => {
    switch (type) {
      case 'default':
      case 'body':
        return Typography.styles.body;
      case 'title':
      case 'h1':
        return Typography.styles.h1;
      case 'defaultSemiBold':
        return { ...Typography.styles.body, fontWeight: Typography.fontWeight.semibold };
      case 'subtitle':
      case 'h4':
        return Typography.styles.h4;
      case 'link':
        return { ...Typography.styles.link, color: '#0a7ea4' };
      case 'h2':
        return Typography.styles.h2;
      case 'h3':
        return Typography.styles.h3;
      case 'h5':
        return Typography.styles.h5;
      case 'h6':
        return Typography.styles.h6;
      case 'bodySmall':
        return Typography.styles.bodySmall;
      case 'bodyLarge':
        return Typography.styles.bodyLarge;
      case 'caption':
        return Typography.styles.caption;
      case 'label':
        return Typography.styles.label;
      default:
        return Typography.styles.body;
    }
  };

  return (
    <Text
      style={[
        { color },
        getTextStyle(),
        style,
      ]}
      {...rest}
    />
  );
}
