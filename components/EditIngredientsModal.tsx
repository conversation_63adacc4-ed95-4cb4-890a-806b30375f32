import React, { useState, useEffect } from 'react';
import {
  Modal,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Pressable,
  KeyboardAvoidingView,
  Platform,
  ActionSheetIOS,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { ThemedText } from './ThemedText';
import { Colors } from '@/constants/Colors';
import { useTheme } from '@/contexts/ThemeContext';
import { getIngredientEmoji } from '@/lib/ingredientEmoji';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

interface Ingredient {
  name: string;
  amount: string;
  unit: string;
  emoji: string;
  isEdited: boolean; // Track if user has edited this ingredient
  originalAmount: string; // Keep the original combined amount
}

// Common cooking units
const COOKING_UNITS = [
  '', // No unit
  'tsp',
  'tbsp',
  'cup',
  'cups',
  'oz',
  'fl oz',
  'lb',
  'lbs',
  'g',
  'kg',
  'ml',
  'l',
  'clove',
  'cloves',
  'piece',
  'pieces',
  'slice',
  'slices',
  'whole',
  'half',
  'pinch',
  'dash',
  'can',
  'jar',
  'bunch',
  'head',
  'stalk',
  'stalks',
];

interface EditIngredientsModalProps {
  visible: boolean;
  ingredients: { name: string; amount: string; emoji: string }[];
  onClose: () => void;
  onSave: (
    ingredients: { name: string; amount: string; emoji: string }[]
  ) => void;
  mealName: string;
}

// Helper function to parse amount and unit from combined string
const parseAmountAndUnit = (
  combinedAmount: string
): { amount: string; unit: string } => {
  if (!combinedAmount) {
    return { amount: '', unit: '' };
  }

  const trimmed = combinedAmount.trim();

  // Try to match various number patterns: "2", "1.5", "0.5", "1/2", "1 1/2", etc.
  // This regex captures numbers (including fractions and decimals) at the start, followed by optional unit
  const match = trimmed.match(
    /^([0-9]+(?:\s*[0-9]*\/[0-9]+)?(?:\.[0-9]+)?|\.[0-9]+|[0-9]*\/[0-9]+)\s*(.*)$/
  );

  if (match) {
    const amount = match[1].trim();
    const unit = match[2].trim();
    return {
      amount: amount,
      unit: unit,
    };
  }

  // If no clear pattern found, treat whole thing as unit
  return { amount: '', unit: trimmed };
};

export function EditIngredientsModal({
  visible,
  ingredients,
  onClose,
  onSave,
  mealName,
}: EditIngredientsModalProps) {
  const { activeTheme } = useTheme();
  const colors = Colors[activeTheme];
  const insets = useSafeAreaInsets();
  const [editedIngredients, setEditedIngredients] = useState<Ingredient[]>([]);
  const [showUnitPicker, setShowUnitPicker] = useState(false);
  const [selectedIngredientIndex, setSelectedIngredientIndex] = useState<number | null>(null);

  useEffect(() => {
    // Start with ingredients in their original format - don't parse anything initially
    const convertedIngredients = ingredients.map((ingredient) => {
      return {
        name: ingredient.name,
        amount: '', // Empty initially - will be populated when user starts editing
        unit: '', // Empty initially - will be populated when user starts editing
        emoji: ingredient.emoji,
        isEdited: false, // Track that this ingredient hasn't been edited
        originalAmount: ingredient.amount, // Keep the original combined amount intact
      };
    });

    setEditedIngredients(convertedIngredients);
  }, [ingredients, visible]);

  const updateIngredient = (
    index: number,
    field: 'name' | 'amount' | 'unit',
    value: string
  ) => {
    const updated = [...editedIngredients];

    // If this is the first time editing amount or unit, parse the original
    if ((field === 'amount' || field === 'unit') && !updated[index].isEdited) {
      const parsed = parseAmountAndUnit(updated[index].originalAmount);

      updated[index] = {
        ...updated[index],
        amount: parsed.amount,
        unit: parsed.unit,
        isEdited: true,
        [field]: value,
        emoji: updated[index].emoji, // Keep existing emoji since field can't be 'name' here
      };
    } else {
      updated[index] = {
        ...updated[index],
        [field]: value,
        isEdited:
          field === 'amount' || field === 'unit'
            ? true
            : updated[index].isEdited,
        emoji:
          field === 'name' ? getIngredientEmoji(value) : updated[index].emoji,
      };
    }

    setEditedIngredients(updated);
  };

  const deleteIngredient = (index: number) => {
    const updated = editedIngredients.filter((_, i) => i !== index);
    setEditedIngredients(updated);
  };

  const addIngredient = () => {
    setEditedIngredients([
      ...editedIngredients,
      {
        name: '',
        amount: '',
        unit: '',
        emoji: '🥘',
        originalAmount: '',
        isEdited: true,
      },
    ]);
  };

  const handleUnitPickerOpen = (index: number, currentUnit: string) => {
    if (Platform.OS === 'ios') {
      const options = COOKING_UNITS.map((unit) => unit || 'No unit');
      ActionSheetIOS.showActionSheetWithOptions(
        {
          options: ['Cancel', ...options],
          cancelButtonIndex: 0,
          title: 'Select Unit',
        },
        (buttonIndex) => {
          if (buttonIndex > 0) {
            const selectedUnit = COOKING_UNITS[buttonIndex - 1];
            updateIngredient(index, 'unit', selectedUnit);
          }
        }
      );
    } else {
      // For Android and cross-platform consistency, use custom modal picker
      setSelectedIngredientIndex(index);
      setShowUnitPicker(true);
    }
  };

  const handleUnitSelect = (unit: string) => {
    if (selectedIngredientIndex !== null) {
      updateIngredient(selectedIngredientIndex, 'unit', unit);
    }
    setShowUnitPicker(false);
    setSelectedIngredientIndex(null);
  };

  const handleSave = () => {
    // Filter out empty ingredients and convert back to expected format
    const validIngredients = editedIngredients
      .filter((ing) => ing.name.trim().length > 0)
      .map((ing) => {
        let combinedAmount = '';

        if (ing.isEdited) {
          // User edited this ingredient, combine the new amount and unit
          if (ing.amount && ing.unit) {
            combinedAmount = `${ing.amount} ${ing.unit}`.trim();
          } else if (ing.amount) {
            combinedAmount = ing.amount.trim();
          } else if (ing.unit) {
            combinedAmount = ing.unit.trim();
          }
        } else {
          // User didn't edit this ingredient, use the original amount unchanged
          combinedAmount = ing.originalAmount;
        }

        return {
          name: ing.name,
          amount: combinedAmount,
          emoji: ing.emoji,
        };
      });

    onSave(validIngredients);
    onClose();
  };

  const styles = createStyles(colors);

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.modalContainer}
      >
        <Pressable style={styles.modalOverlay} onPress={onClose}>
          <Pressable
            style={[styles.modalContent, { paddingBottom: insets.bottom + 24 }]}
            onPress={() => {}}
          >
            {/* Header */}
            <View style={styles.modalHeader}>
              <ThemedText style={styles.modalTitle}>
                Edit Ingredients
              </ThemedText>
              <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                <Ionicons name="close" size={24} color={colors.text} />
              </TouchableOpacity>
            </View>
            <ThemedText style={styles.mealNameText}>{mealName}</ThemedText>

            {/* Ingredients List */}
            <ScrollView
              style={styles.ingredientsList}
              showsVerticalScrollIndicator={false}
              keyboardShouldPersistTaps="handled"
            >
              {editedIngredients.map((ingredient, index) => (
                <View key={index} style={styles.ingredientRow}>
                  <Text style={styles.ingredientEmoji}>{ingredient.emoji}</Text>

                  <View style={styles.inputContainer}>
                    <TextInput
                      style={[
                        styles.nameInput,
                        {
                          color: colors.text,
                          backgroundColor: colors.background,
                          borderColor: colors.border,
                        },
                      ]}
                      value={ingredient.name}
                      onChangeText={(text) =>
                        updateIngredient(index, 'name', text)
                      }
                      placeholder="Ingredient name"
                      placeholderTextColor={colors.text + '60'}
                    />

                    <View style={styles.amountUnitContainer}>
                      {ingredient.isEdited ? (
                        <>
                          <TextInput
                            style={[styles.amountInput, { color: colors.text }]}
                            value={ingredient.amount}
                            onChangeText={(text) =>
                              updateIngredient(index, 'amount', text)
                            }
                            placeholder="Amount"
                            placeholderTextColor={colors.text + '60'}
                            keyboardType="numeric"
                          />

                          <TouchableOpacity
                            style={[
                              styles.unitSelector,
                              {
                                backgroundColor: colors.background,
                                borderColor: colors.border,
                              },
                            ]}
                            onPress={() =>
                              handleUnitPickerOpen(index, ingredient.unit)
                            }
                          >
                            <ThemedText
                              style={[styles.unitText, { color: colors.text }]}
                            >
                              {ingredient.unit || 'Unit'}
                            </ThemedText>
                            <Ionicons
                              name="chevron-down"
                              size={16}
                              color={colors.text}
                            />
                          </TouchableOpacity>
                        </>
                      ) : (
                        <TouchableOpacity
                          style={[
                            styles.originalAmountDisplay,
                            {
                              backgroundColor: colors.background,
                              borderColor: colors.border,
                            },
                          ]}
                          onPress={() => updateIngredient(index, 'amount', '')} // This will trigger editing mode
                        >
                          <ThemedText
                            style={[
                              styles.originalAmountText,
                              { color: colors.text },
                            ]}
                          >
                            {ingredient.originalAmount || 'Tap to edit'}
                          </ThemedText>
                          <Ionicons
                            name="create-outline"
                            size={16}
                            color={colors.text}
                            style={styles.editIcon}
                          />
                        </TouchableOpacity>
                      )}
                    </View>
                  </View>

                  <TouchableOpacity
                    onPress={() => deleteIngredient(index)}
                    style={styles.deleteButton}
                  >
                    <Ionicons name="trash-outline" size={20} color="#FF4444" />
                  </TouchableOpacity>
                </View>
              ))}

              {/* Add Ingredient Button */}
              <TouchableOpacity
                style={styles.addButton}
                onPress={addIngredient}
              >
                <Ionicons
                  name="add-circle-outline"
                  size={20}
                  color={colors.primary}
                />
                <ThemedText
                  style={[styles.addButtonText, { color: colors.primary }]}
                >
                  Add Ingredient
                </ThemedText>
              </TouchableOpacity>
            </ScrollView>

            {/* Action Buttons */}
            <View style={styles.actionButtons}>
              <TouchableOpacity
                style={[styles.button, styles.cancelButton]}
                onPress={onClose}
              >
                <ThemedText style={styles.cancelButtonText}>Cancel</ThemedText>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.button,
                  styles.saveButton,
                  { borderColor: colors.primary },
                ]}
                onPress={handleSave}
              >
                <ThemedText
                  style={[styles.saveButtonText, { color: colors.primary }]}
                >
                  Save Changes
                </ThemedText>
              </TouchableOpacity>
            </View>
          </Pressable>
        </Pressable>
      </KeyboardAvoidingView>

      {/* Unit Picker Modal for Android */}
      <Modal
        visible={showUnitPicker}
        transparent={true}
        animationType="fade"
        onRequestClose={() => {
          setShowUnitPicker(false);
          setSelectedIngredientIndex(null);
        }}
      >
        <Pressable
          style={styles.pickerOverlay}
          onPress={() => {
            setShowUnitPicker(false);
            setSelectedIngredientIndex(null);
          }}
        >
          <Pressable
            style={[styles.pickerContent, { backgroundColor: colors.cardBackground }]}
            onPress={() => {}}
          >
            <View style={styles.pickerHeader}>
              <ThemedText style={styles.pickerTitle}>Select Unit</ThemedText>
              <TouchableOpacity
                onPress={() => {
                  setShowUnitPicker(false);
                  setSelectedIngredientIndex(null);
                }}
                style={styles.pickerCloseButton}
              >
                <Ionicons name="close" size={24} color={colors.text} />
              </TouchableOpacity>
            </View>
            
            <ScrollView style={styles.pickerList} showsVerticalScrollIndicator={false}>
              {COOKING_UNITS.map((unit, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.pickerOption,
                    { borderBottomColor: colors.border }
                  ]}
                  onPress={() => handleUnitSelect(unit)}
                >
                  <ThemedText style={styles.pickerOptionText}>
                    {unit || 'No unit'}
                  </ThemedText>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </Pressable>
        </Pressable>
      </Modal>
    </Modal>
  );
}

const createStyles = (colors: any) =>
  StyleSheet.create({
    modalContainer: {
      flex: 1,
    },
    modalOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'flex-end',
    },
    modalContent: {
      backgroundColor: colors.cardBackground,
      borderTopLeftRadius: 20,
      borderTopRightRadius: 20,
      padding: 24,
      maxHeight: '90%',
    },
    modalHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 8,
    },
    modalTitle: {
      fontSize: 20,
      fontWeight: '600',
    },
    closeButton: {
      padding: 4,
    },
    mealNameText: {
      fontSize: 14,
      opacity: 0.7,
      marginBottom: 20,
    },
    ingredientsList: {
      flexGrow: 0,
      marginBottom: 20,
    },
    ingredientRow: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 12,
      backgroundColor: colors.background,
      padding: 12,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: colors.border,
    },
    ingredientEmoji: {
      fontSize: 20,
      marginRight: 12,
    },
    inputContainer: {
      flex: 1,
      gap: 8,
    },
    nameInput: {
      fontSize: 16,
      fontWeight: '500',
      paddingHorizontal: 12,
      paddingVertical: 8,
      borderRadius: 6,
      borderWidth: 1,
      marginBottom: 8,
    },
    amountUnitContainer: {
      flexDirection: 'row',
      gap: 8,
      alignItems: 'center',
      flex: 1,
    },
    amountInput: {
      fontSize: 14,
      paddingHorizontal: 12,
      paddingVertical: 8,
      borderRadius: 6,
      borderWidth: 1,
      borderColor: colors.border,
      backgroundColor: colors.background,
      flex: 1,
      minWidth: 60,
      height: 40,
    },
    unitSelector: {
      borderRadius: 6,
      borderWidth: 1,
      flex: 1,
      maxWidth: 120,
      height: 40,
      justifyContent: 'space-between',
      alignItems: 'center',
      flexDirection: 'row',
      paddingHorizontal: 12,
    },
    unitText: {
      fontSize: 14,
      flex: 1,
    },
    originalAmountDisplay: {
      borderRadius: 6,
      borderWidth: 1,
      flex: 1,
      height: 40,
      justifyContent: 'space-between',
      alignItems: 'center',
      flexDirection: 'row',
      paddingHorizontal: 12,
    },
    originalAmountText: {
      fontSize: 14,
      flex: 1,
    },
    editIcon: {
      opacity: 0.6,
    },
    deleteButton: {
      padding: 8,
      marginLeft: 8,
    },
    addButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      padding: 16,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: colors.primary,
    },
    addButtonText: {
      fontSize: 16,
      fontWeight: '500',
      marginLeft: 8,
    },
    actionButtons: {
      flexDirection: 'row',
      gap: 12,
    },
    button: {
      flex: 1,
      paddingVertical: 16,
      borderRadius: 8,
      alignItems: 'center',
      justifyContent: 'center',
      borderWidth: 1,
    },
    cancelButton: {
      backgroundColor: colors.background,
      borderWidth: 1,
      borderColor: colors.border,
    },
    saveButton: {},
    cancelButtonText: {
      fontSize: 16,
      fontWeight: '600',
      opacity: 0.7,
    },
    saveButtonText: {
      fontSize: 16,
      fontWeight: '600',
    },
    // Unit picker modal styles
    pickerOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 20,
    },
    pickerContent: {
      width: '100%',
      maxWidth: 300,
      maxHeight: '60%',
      borderRadius: 12,
      padding: 0,
      overflow: 'hidden',
    },
    pickerHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      padding: 16,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    pickerTitle: {
      fontSize: 18,
      fontWeight: '600',
    },
    pickerCloseButton: {
      padding: 4,
    },
    pickerList: {
      maxHeight: 300,
    },
    pickerOption: {
      paddingVertical: 16,
      paddingHorizontal: 20,
      borderBottomWidth: 1,
    },
    pickerOptionText: {
      fontSize: 16,
    },
  });
