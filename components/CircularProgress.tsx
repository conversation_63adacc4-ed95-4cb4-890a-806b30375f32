import React from 'react';
import { View, StyleSheet } from 'react-native';
import Svg, { Circle, G } from 'react-native-svg';
import { ThemedText } from './ThemedText';

interface CircularProgressProps {
  size: number;
  strokeWidth: number;
  progress: number; // 0 to 1
  color: string;
  backgroundColor: string;
  value: string;
  label: string;
  maxValue?: number;
}

export function CircularProgress({
  size,
  strokeWidth,
  progress,
  color,
  backgroundColor,
  value,
  label,
  maxValue,
}: CircularProgressProps) {
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const strokeDashoffset = circumference - progress * circumference;

  return (
    <View style={styles.container}>
      <View style={{ width: size, height: size }}>
        <Svg width={size} height={size} style={styles.svg}>
          <G rotation="-90" origin={`${size / 2}, ${size / 2}`}>
            {/* Background circle */}
            <Circle
              stroke={backgroundColor}
              fill="none"
              cx={size / 2}
              cy={size / 2}
              r={radius}
              strokeWidth={strokeWidth}
            />
            {/* Progress circle */}
            <Circle
              stroke={color}
              fill="none"
              cx={size / 2}
              cy={size / 2}
              r={radius}
              strokeWidth={strokeWidth}
              strokeDasharray={`${circumference} ${circumference}`}
              strokeDashoffset={strokeDashoffset}
              strokeLinecap="round"
            />
          </G>
        </Svg>
        <View style={[styles.valueContainer, { width: size, height: size }]}>
          <ThemedText style={[styles.value, { color }]}>{value}</ThemedText>
          {maxValue && (
            <ThemedText style={[styles.maxValue, { color: color + '80' }]}>
              /{maxValue}
            </ThemedText>
          )}
        </View>
      </View>
      <ThemedText style={styles.label}>{label}</ThemedText>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
  },
  svg: {
    position: 'absolute',
  },
  valueContainer: {
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
  },
  value: {
    fontSize: 18,
    fontWeight: '700',
  },
  maxValue: {
    fontSize: 12,
    fontWeight: '500',
  },
  label: {
    fontSize: 12,
    marginTop: 4,
    opacity: 0.6,
  },
});
