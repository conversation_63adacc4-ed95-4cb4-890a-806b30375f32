import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useTheme } from '@/contexts/ThemeContext';
import { Colors } from '@/constants/Colors';

export function ThemedRootStack() {
  const { activeTheme } = useTheme();
  const backgroundColor = Colors[activeTheme].background;

  return (
    <>
      <Stack
        screenOptions={{
          animation: 'none',
          contentStyle: { backgroundColor },
        }}
      >
        <Stack.Screen name="(auth)" options={{ headerShown: false }} />
        <Stack.Screen name="(paywall)" options={{ headerShown: false }} />
        <Stack.Screen name="(onboarding)" options={{ headerShown: false }} />
        <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
        <Stack.Screen name="index" options={{ headerShown: false }} />
        <Stack.Screen name="+not-found" />
        <Stack.Screen
          name="meal-detail"
          options={{
            headerShown: false,
            animation: 'none',
            contentStyle: { backgroundColor },
          }}
        />
        <Stack.Screen
          name="side-dish-detail"
          options={{
            headerShown: false,
            animation: 'none',
            contentStyle: { backgroundColor },
          }}
        />
        <Stack.Screen
          name="ai-meal-detail"
          options={{
            headerShown: false,
            animation: 'none',
            contentStyle: { backgroundColor },
          }}
        />
        <Stack.Screen
          name="profile"
          options={{
            headerShown: false,
            animation: 'none',
            contentStyle: { backgroundColor },
          }}
        />
        <Stack.Screen
          name="user-meal-detail"
          options={{
            headerShown: false,
            animation: 'none',
            contentStyle: { backgroundColor },
          }}
        />
      </Stack>
      <StatusBar style={activeTheme === 'dark' ? 'light' : 'dark'} />
    </>
  );
}
