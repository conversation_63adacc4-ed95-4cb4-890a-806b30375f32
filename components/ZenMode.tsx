import React, { useState } from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { ThemedText } from './ThemedText';
import { Colors } from '@/constants/Colors';
import { useTheme } from '@/contexts/ThemeContext';

interface ZenModeProps {
  visible: boolean;
  instructions: string[];
  onClose: () => void;
  mealName: string;
}

export function ZenMode({
  visible,
  instructions,
  onClose,
  mealName,
}: ZenModeProps) {
  const { activeTheme } = useTheme();
  const colors = Colors[activeTheme];
  const styles = createStyles(colors);
  const [currentStep, setCurrentStep] = useState(0);

  const handleNext = () => {
    if (currentStep < instructions.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleClose = () => {
    setCurrentStep(0);
    onClose();
  };

  const isLastStep = currentStep === instructions.length - 1;
  const isFirstStep = currentStep === 0;

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="fullScreen"
      onRequestClose={handleClose}
    >
      <SafeAreaView
        style={[styles.container, { backgroundColor: colors.background }]}
      >
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={handleClose} style={styles.closeButton}>
            <Ionicons name="close" size={28} color={colors.text} />
          </TouchableOpacity>
          <ThemedText style={styles.title}>{mealName}</ThemedText>
          <View style={styles.placeholder} />
        </View>

        {/* Step Indicator */}
        <View style={styles.stepIndicator}>
          <ThemedText style={styles.stepText}>
            Step {currentStep + 1} of {instructions.length}
          </ThemedText>
        </View>

        {/* Instruction Content */}
        <View style={styles.contentContainer}>
          <View
            style={[styles.stepNumber, { backgroundColor: colors.primary }]}
          >
            <Text style={[styles.stepNumberText, { color: colors.background }]}>
              {currentStep + 1}
            </Text>
          </View>
          <ThemedText style={styles.instructionText}>
            {instructions[currentStep]}
          </ThemedText>
        </View>

        {/* Navigation */}
        <View style={styles.navigation}>
          <TouchableOpacity
            style={[styles.navButton, { opacity: isFirstStep ? 0.3 : 1 }]}
            onPress={handlePrevious}
            disabled={isFirstStep}
          >
            <Ionicons name="chevron-back" size={32} color={colors.text} />
            <ThemedText style={styles.navText}>Previous</ThemedText>
          </TouchableOpacity>

          {isLastStep ? (
            <TouchableOpacity
              style={[styles.doneButton, { backgroundColor: colors.primary }]}
              onPress={handleClose}
            >
              <Ionicons
                name="checkmark-circle"
                size={24}
                color={colors.background}
              />
              <Text style={[styles.doneText, { color: colors.background }]}>
                Done Cooking
              </Text>
            </TouchableOpacity>
          ) : (
            <TouchableOpacity style={styles.navButton} onPress={handleNext}>
              <ThemedText style={styles.navText}>Next</ThemedText>
              <Ionicons name="chevron-forward" size={32} color={colors.text} />
            </TouchableOpacity>
          )}
        </View>

        {/* Progress Bar */}
        <View style={styles.progressContainer}>
          <View
            style={[styles.progressTrack, { backgroundColor: colors.border }]}
          >
            <View
              style={[
                styles.progressBar,
                {
                  backgroundColor: colors.primary,
                  width: `${((currentStep + 1) / instructions.length) * 100}%`,
                },
              ]}
            />
          </View>
        </View>
      </SafeAreaView>
    </Modal>
  );
}

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 20,
      paddingVertical: 16,
    },
    closeButton: {
      padding: 8,
    },
    title: {
      fontSize: 18,
      fontWeight: '600',
      textAlign: 'center',
      flex: 1,
      marginHorizontal: 16,
    },
    placeholder: {
      width: 44,
    },
    stepIndicator: {
      alignItems: 'center',
      marginBottom: 20,
    },
    stepText: {
      fontSize: 16,
      opacity: 0.7,
    },
    contentContainer: {
      flex: 1,
      paddingHorizontal: 30,
      justifyContent: 'center',
      alignItems: 'center',
    },
    stepNumber: {
      width: 60,
      height: 60,
      borderRadius: 30,
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: 30,
    },
    stepNumberText: {
      fontSize: 24,
      fontWeight: '700',
    },
    instructionText: {
      fontSize: 24,
      lineHeight: 36,
      textAlign: 'center',
      paddingHorizontal: 20,
    },
    navigation: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 30,
      paddingVertical: 20,
    },
    navButton: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 12,
      gap: 8,
    },
    navText: {
      fontSize: 18,
      fontWeight: '500',
    },
    doneButton: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 24,
      paddingVertical: 14,
      borderRadius: 25,
      gap: 8,
    },
    doneText: {
      fontSize: 18,
      fontWeight: '600',
    },
    progressContainer: {
      paddingHorizontal: 30,
      paddingBottom: 20,
    },
    progressTrack: {
      height: 4,
      borderRadius: 2,
      overflow: 'hidden',
    },
    progressBar: {
      height: '100%',
      borderRadius: 2,
    },
  });
