import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { useTheme } from '@/contexts/ThemeContext';
import { useThemeColor } from '@/hooks/useThemeColor';
import { Ionicons } from '@expo/vector-icons';
import React, { useState } from 'react';
import {
  Modal,
  Pressable,
  ScrollView,
  StyleSheet,
  Switch,
  TouchableOpacity,
  View,
} from 'react-native';

interface EditWeekModalProps {
  visible: boolean;
  onClose: () => void;
  onSave: (disabledDays: number[]) => void;
  currentDisabledDays: number[];
}

const weekDays = [
  { name: 'Sunday', index: 0 },
  { name: 'Monday', index: 1 },
  { name: 'Tuesday', index: 2 },
  { name: 'Wednesday', index: 3 },
  { name: 'Thursday', index: 4 },
  { name: 'Friday', index: 5 },
  { name: 'Saturday', index: 6 },
];

export function EditWeekModal({
  visible,
  onClose,
  onSave,
  currentDisabledDays,
}: EditWeekModalProps) {
  const { activeTheme } = useTheme();

  // Theme-aware colors
  const cardBackgroundColor = useThemeColor({}, 'cardBackground');
  const textColor = useThemeColor({}, 'text');
  const borderColor = useThemeColor({}, 'border');
  const primaryColor = useThemeColor({}, 'primary');
  const errorColor = useThemeColor(
    { light: '#E17055', dark: '#FF6B6B' },
    'error'
  );

  const [disabledDays, setDisabledDays] =
    useState<number[]>(currentDisabledDays);

  const toggleDay = (dayIndex: number) => {
    setDisabledDays((prev) => {
      if (prev.includes(dayIndex)) {
        return prev.filter((day) => day !== dayIndex);
      } else {
        return [...prev, dayIndex];
      }
    });
  };

  const handleSave = () => {
    onSave(disabledDays);
    onClose();
  };

  const handleCancel = () => {
    setDisabledDays(currentDisabledDays); // Reset to original state
    onClose();
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={handleCancel}
    >
      <ThemedView style={styles.container}>
        {/* Header */}
        <View style={[styles.header, { borderBottomColor: borderColor }]}>
          <Pressable style={styles.cancelButton} onPress={handleCancel}>
            <ThemedText style={[styles.cancelText, { color: errorColor }]}>
              Cancel
            </ThemedText>
          </Pressable>

          <ThemedText style={[styles.title, { color: textColor }]}>
            Edit Week
          </ThemedText>

          <Pressable style={styles.saveButton} onPress={handleSave}>
            <ThemedText style={[styles.saveText, { color: primaryColor }]}>
              Save
            </ThemedText>
          </Pressable>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Description */}
          <View
            style={[
              styles.descriptionContainer,
              {
                backgroundColor:
                  activeTheme === 'light'
                    ? 'rgba(108, 154, 58, 0.1)'
                    : 'rgba(255, 229, 152, 0.1)',
                borderColor:
                  activeTheme === 'light'
                    ? 'rgba(108, 154, 58, 0.3)'
                    : 'rgba(255, 229, 152, 0.3)',
              },
            ]}
          >
            <Ionicons
              name="information-circle"
              size={24}
              color={primaryColor}
            />
            <ThemedText
              style={[styles.description, { color: textColor, opacity: 0.9 }]}
            >
              Disable days when you won&apos;t be eating at home. Disabled meals
              won&apos;t appear in your shopping list.
            </ThemedText>
          </View>

          {/* Days List */}
          <View style={styles.daysContainer}>
            <ThemedText style={[styles.sectionTitle, { color: textColor }]}>
              Days of the Week
            </ThemedText>

            {weekDays.map((day) => {
              const isDisabled = disabledDays.includes(day.index);

              return (
                <TouchableOpacity
                  key={day.index}
                  style={[
                    styles.dayRow,
                    {
                      backgroundColor: cardBackgroundColor,
                      borderColor: borderColor,
                    },
                  ]}
                  onPress={() => toggleDay(day.index)}
                  activeOpacity={0.7}
                >
                  <View style={styles.dayInfo}>
                    <ThemedText style={[styles.dayName, { color: textColor }]}>
                      {day.name}
                    </ThemedText>
                    <ThemedText
                      style={[
                        styles.dayStatus,
                        { color: isDisabled ? errorColor : primaryColor },
                      ]}
                    >
                      {isDisabled ? 'Disabled' : 'Enabled'}
                    </ThemedText>
                  </View>

                  <Switch
                    value={!isDisabled}
                    onValueChange={() => toggleDay(day.index)}
                    trackColor={{
                      false: errorColor,
                      true: primaryColor,
                    }}
                    thumbColor={activeTheme === 'light' ? '#FFFFFF' : '#FFFFFF'}
                    ios_backgroundColor={errorColor}
                  />
                </TouchableOpacity>
              );
            })}
          </View>

          {/* Summary */}
          <View
            style={[
              styles.summaryContainer,
              {
                backgroundColor: cardBackgroundColor,
                borderColor: borderColor,
              },
            ]}
          >
            <View style={styles.summaryRow}>
              <ThemedText
                style={[
                  styles.summaryLabel,
                  { color: textColor, opacity: 0.8 },
                ]}
              >
                Active days:
              </ThemedText>
              <ThemedText
                style={[styles.summaryValue, { color: primaryColor }]}
              >
                {7 - disabledDays.length}
              </ThemedText>
            </View>

            <View style={styles.summaryRow}>
              <ThemedText
                style={[
                  styles.summaryLabel,
                  { color: textColor, opacity: 0.8 },
                ]}
              >
                Disabled days:
              </ThemedText>
              <ThemedText style={[styles.summaryValue, { color: errorColor }]}>
                {disabledDays.length}
              </ThemedText>
            </View>
          </View>
        </ScrollView>
      </ThemedView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 0.5,
  },
  cancelButton: {
    padding: 4,
  },
  cancelText: {
    fontSize: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
  },
  saveButton: {
    padding: 4,
  },
  saveText: {
    fontSize: 16,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  descriptionContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 12,
    marginTop: 20,
    marginBottom: 32,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
  },
  description: {
    flex: 1,
    fontSize: 14,
    lineHeight: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 16,
  },
  daysContainer: {
    marginBottom: 32,
  },
  dayRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    marginBottom: 8,
    borderRadius: 12,
    borderWidth: 1,
  },
  dayInfo: {
    flex: 1,
  },
  dayName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  dayStatus: {
    fontSize: 12,
    fontWeight: '500',
  },
  summaryContainer: {
    padding: 20,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 40,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  summaryLabel: {
    fontSize: 16,
  },
  summaryValue: {
    fontSize: 18,
    fontWeight: '600',
  },
});
