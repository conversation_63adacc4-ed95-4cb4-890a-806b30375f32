import React from 'react';
import {
  View,
  ActivityIndicator,
  StyleSheet,
  Text,
  ViewStyle,
} from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { Colors } from '@/constants/Colors';
import { ThemedView } from '@/components/ThemedView';
import { ThemedText } from '@/components/ThemedText';

export interface LoadingIndicatorProps {
  size?: 'small' | 'large';
  color?: string;
  text?: string;
  fullScreen?: boolean;
  overlay?: boolean;
  style?: ViewStyle;
}

export function LoadingIndicator({
  size = 'large',
  color,
  text,
  fullScreen = false,
  overlay = false,
  style,
}: LoadingIndicatorProps) {
  const { activeTheme } = useTheme();
  const colors = Colors[activeTheme];
  const indicatorColor = color || colors.accent;

  const content = (
    <View style={[styles.container, style]}>
      <ActivityIndicator size={size} color={indicatorColor} />
      {text && (
        <ThemedText style={styles.text} type="default">
          {text}
        </ThemedText>
      )}
    </View>
  );

  if (fullScreen) {
    return (
      <ThemedView
        style={[
          styles.fullScreenContainer,
          overlay && styles.overlay,
        ]}
      >
        {content}
      </ThemedView>
    );
  }

  return content;
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  fullScreenContainer: {
    ...StyleSheet.absoluteFillObject,
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 999,
  },
  overlay: {
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  text: {
    marginTop: 12,
    textAlign: 'center',
  },
});