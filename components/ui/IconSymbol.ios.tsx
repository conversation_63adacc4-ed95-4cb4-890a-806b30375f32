import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { SymbolView, SymbolViewProps, SymbolWeight } from 'expo-symbols';
import { type ComponentProps } from 'react';
import { Platform } from 'react-native';

const ICON_MAPPING: Record<string, string> = {
  home: 'house.fill',
  cart: 'cart.fill',
  chat: 'message.fill',
  heart: 'heart.fill',
  account: 'person.fill',
};

export function IconSymbol({
  name,
  size = 24,
  color,
  style,
  weight = 'regular',
  ...rest
}: {
  name: ComponentProps<typeof MaterialCommunityIcons>['name'];
  size?: number;
  color: string;
  style?: SymbolViewProps['style'];
  weight?: SymbolWeight;
}) {
  const sfSymbolName =
    typeof name === 'string' ? ICON_MAPPING[name] : undefined;

  if (Platform.OS === 'ios' && sfSymbolName) {
    return (
      <SymbolView
        name={sfSymbolName}
        size={size}
        tintColor={color}
        weight={weight}
        style={style}
        {...rest}
      />
    );
  }

  return (
    <MaterialCommunityIcons
      name={name}
      size={size}
      color={color}
      style={style}
      {...rest}
    />
  );
}
