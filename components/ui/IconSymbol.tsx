import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { type ComponentProps } from 'react';

export function IconSymbol({
  name,
  size = 24,
  color,
  style,
  ...rest
}: {
  name: ComponentProps<typeof MaterialCommunityIcons>['name'];
  size?: number;
  color: string;
  style?: ComponentProps<typeof MaterialCommunityIcons>['style'];
}) {
  return (
    <MaterialCommunityIcons
      name={name}
      size={size}
      color={color}
      style={style}
      {...rest}
    />
  );
}
