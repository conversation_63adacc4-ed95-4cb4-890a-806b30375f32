import { BlurView } from 'expo-blur';
import { useTheme } from '@/contexts/ThemeContext';

export function TabBarBackground() {
  const { activeTheme } = useTheme();
  const tint =
    activeTheme === 'light'
      ? 'systemChromeMaterialLight'
      : 'systemChromeMaterialDark';

  return (
    <BlurView
      intensity={100}
      tint={tint}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
      }}
    />
  );
}
