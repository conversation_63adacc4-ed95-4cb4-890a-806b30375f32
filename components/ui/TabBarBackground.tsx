import { useThemeColor } from '@/hooks/useThemeColor';
import { useTheme } from '@/contexts/ThemeContext';
import { View } from 'react-native';

export function TabBarBackground() {
  const { activeTheme } = useTheme();
  const backgroundColor = useThemeColor(
    {
      light: '#FFFFFF', // Pure white for light mode tab bar
      dark: '#212121', // Dark background for dark mode
    },
    'background'
  );
  const borderColor = useThemeColor({}, 'border');

  return (
    <View
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor,
        borderTopLeftRadius: 20,
        borderTopRightRadius: 20,
        borderTopWidth: 1,
        borderTopColor: borderColor,
        // Add subtle shadow for light mode
        ...(activeTheme === 'light' && {
          shadowColor: '#000',
          shadowOffset: {
            width: 0,
            height: -2,
          },
          shadowOpacity: 0.1,
          shadowRadius: 4,
          elevation: 5,
        }),
        // Extend beyond the rounded corners to cover gray areas
        marginLeft: -10,
        marginRight: -10,
        paddingLeft: 10,
        paddingRight: 10,
      }}
    />
  );
}
