import React from 'react';
import { TouchableOpacity, View, StyleSheet } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useTheme, type ThemeMode } from '@/contexts/ThemeContext';
import { useThemeColor } from '@/hooks/useThemeColor';

interface ThemePickerProps {
  style?: any;
}

export function ThemePicker({ style }: ThemePickerProps) {
  const { themeMode, setThemeMode } = useTheme();
  const borderColor = useThemeColor({}, 'border');
  const cardBackground = useThemeColor({}, 'cardBackground');
  const primaryColor = useThemeColor({}, 'primary');
  const textColor = useThemeColor({}, 'text');

  const themeOptions: {
    value: ThemeMode;
    label: string;
    icon: string;
    description: string;
  }[] = [
    {
      value: 'light',
      label: 'Light',
      icon: 'white-balance-sunny',
      description: 'Light theme',
    },
    {
      value: 'dark',
      label: 'Dark',
      icon: 'moon-waning-crescent',
      description: 'Dark theme',
    },
    {
      value: 'system',
      label: 'System',
      icon: 'cog-outline',
      description: 'Follow system settings',
    },
  ];

  const handleThemeChange = async (mode: ThemeMode) => {
    await setThemeMode(mode);
  };

  return (
    <View style={[styles.container, style]}>
      <View style={styles.header}>
        <IconSymbol name="palette-outline" size={20} color={primaryColor} />
        <ThemedText style={styles.title}>App Theme</ThemedText>
      </View>

      <View style={styles.optionsContainer}>
        {themeOptions.map((option) => (
          <TouchableOpacity
            key={option.value}
            style={[
              styles.option,
              {
                backgroundColor: cardBackground,
                borderColor:
                  themeMode === option.value ? primaryColor : borderColor,
                borderWidth: themeMode === option.value ? 2 : 1,
              },
            ]}
            onPress={() => handleThemeChange(option.value)}
            activeOpacity={0.7}
          >
            <View style={styles.optionContent}>
              <View style={styles.optionLeft}>
                <IconSymbol
                  name={option.icon}
                  size={24}
                  color={themeMode === option.value ? primaryColor : textColor}
                />
                <View style={styles.optionText}>
                  <ThemedText
                    style={[
                      styles.optionLabel,
                      themeMode === option.value && { color: primaryColor },
                    ]}
                  >
                    {option.label}
                  </ThemedText>
                  <ThemedText style={styles.optionDescription}>
                    {option.description}
                  </ThemedText>
                </View>
              </View>

              {themeMode === option.value && (
                <IconSymbol
                  name="check-circle"
                  size={20}
                  color={primaryColor}
                />
              )}
            </View>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 24,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
  },
  optionsContainer: {
    gap: 12,
  },
  option: {
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  optionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  optionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    flex: 1,
  },
  optionText: {
    flex: 1,
  },
  optionLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 2,
  },
  optionDescription: {
    fontSize: 14,
    opacity: 0.6,
  },
});
