import { useRef, useEffect, useCallback, useState } from 'react';
import { Animated, Easing } from 'react-native';
import { useFocusEffect } from 'expo-router';

interface AnimationSection {
  opacity: Animated.Value;
  translateY: Animated.Value;
}

export function useStaggeredAnimation(sectionCount: number) {
  const sections = useRef<AnimationSection[]>(
    Array.from({ length: sectionCount }, () => {
      const opacity = new Animated.Value(0);
      const translateY = new Animated.Value(30);
      
      // Set initial values immediately to prevent flicker
      opacity.setValue(0);
      translateY.setValue(30);
      
      return { opacity, translateY };
    })
  ).current;

  const hasAnimated = useRef(false);
  const [isReady, setIsReady] = useState(false);

  const resetAnimations = () => {
    sections.forEach(section => {
      section.opacity.setValue(0);
      section.translateY.setValue(30);
    });
    hasAnimated.current = false;
    setIsReady(false);
  };

  const startAnimations = () => {
    if (hasAnimated.current) return;
    hasAnimated.current = true;
    setIsReady(true);

    const animations = sections.map(section =>
      Animated.parallel([
        Animated.timing(section.opacity, {
          toValue: 1,
          duration: 600,
          easing: Easing.out(Easing.cubic),
          useNativeDriver: true,
        }),
        Animated.timing(section.translateY, {
          toValue: 0,
          duration: 600,
          easing: Easing.out(Easing.cubic),
          useNativeDriver: true,
        }),
      ])
    );

    // Start animations with staggered delays
    animations.forEach((animation, index) => {
      setTimeout(() => animation.start(), index * 100);
    });
  };

  // Reset animations when screen loses focus and start when it gains focus
  // Initialize on mount
  useEffect(() => {
    resetAnimations();
  }, []);

  useFocusEffect(
    useCallback(() => {
      resetAnimations();
      setIsReady(true);
      // Very small delay to prevent flicker
      requestAnimationFrame(() => {
        startAnimations();
      });
    }, [])
  );

  return {
    sections,
    resetAnimations,
    startAnimations,
    isReady,
  };
}