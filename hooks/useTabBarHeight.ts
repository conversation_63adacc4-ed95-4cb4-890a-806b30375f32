import { Platform } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

export function useTabBarHeight() {
  const insets = useSafeAreaInsets();

  // Detect if Android navigation bar is present
  const hasAndroidNavBar = Platform.OS === 'android' && insets.bottom > 20;

  // Calculate dynamic tab bar height and content padding
  const tabBarHeight = hasAndroidNavBar ? 88 + insets.bottom : 88;
  const contentBottomPadding = Platform.OS === 'android' ? 0 : 120; // No padding on Android, 120px on iOS

  return {
    tabBarHeight,
    contentBottomPadding,
    hasAndroidNavBar,
    androidNavBarHeight: hasAndroidNavBar ? insets.bottom : 0,
  };
}
