import { CustomAlert } from '@/components/CustomAlert';
import { ThemedText } from '@/components/ThemedText';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { ZenMode } from '@/components/ZenMode';
import { CircularProgress } from '@/components/CircularProgress';
import { Colors } from '@/constants/Colors';
import { useAuth } from '@/contexts/AuthContext';
import { useTheme } from '@/contexts/ThemeContext';
import type { Database , Json } from '@/lib/database.types';
import { getIngredientEmoji } from '@/lib/ingredientEmoji';
import { scaleIngredient } from '@/lib/ingredientScaling';
import { supabase } from '@/lib/supabase';
import {
  getWeekStartDate,
  getGlobalWeeklyCycleStartDay,
} from '@/lib/weekCalculation';
import { Ionicons } from '@expo/vector-icons';
import { router, Stack, useLocalSearchParams } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Modal,
  Pressable,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { Image } from 'expo-image';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

// Type guard functions
function ensureUserCustomizedIngredients(
  value: Json | null
): Record<string, any> {
  if (value !== null && typeof value === 'object' && !Array.isArray(value)) {
    return value as Record<string, any>;
  }
  return {};
}

type UserMeal = Database['public']['Tables']['user_meals']['Row'];

function UserMealDetailScreen() {
  const { activeTheme } = useTheme();
  const colors = Colors[activeTheme];
  const styles = createStyles(colors);
  const insets = useSafeAreaInsets();
  const params = useLocalSearchParams();
  const { session } = useAuth();
  const [showAlert, setShowAlert] = useState(false);
  const [alertConfig, setAlertConfig] = useState({ title: '', message: '' });
  const [mealData, setMealData] = useState<UserMeal | null>(null);
  const [loading, setLoading] = useState(true);
  const [showDayPicker, setShowDayPicker] = useState(false);
  const [showDuplicateAlert, setShowDuplicateAlert] = useState(false);
  const [showZenMode, setShowZenMode] = useState(false);
  const [adjustedServingSize, setAdjustedServingSize] = useState<number | null>(
    null
  );
  const [isSavingServingSize, setIsSavingServingSize] = useState(false);

  const userMealId = params.userMealId as string;

  const formatTime = (hours: number | null, minutes: number | null) => {
    if (!hours && !minutes) return '0 min';
    const h = hours || 0;
    const m = minutes || 0;
    return h > 0 ? `${h}h ${m}m` : `${m} min`;
  };

  const getImageSource = (image: string | null) => {
    if (!image || image === 'default.jpg') {
      return require('@/assets/default.png');
    }

    if (image.startsWith('http')) {
      return { uri: image };
    }

    // Construct Supabase Storage URL for meal images with cache busting
    const timestamp = Date.now();
    return {
      uri: `${process.env.EXPO_PUBLIC_SUPABASE_URL}/storage/v1/object/public/meals/${image}?t=${timestamp}`,
    };
  };

  const parseIngredients = (ingredients: any) => {
    if (!ingredients) return [];

    let parsedIngredients: { name: string; amount: string; emoji: string }[] = [];

    try {
      if (Array.isArray(ingredients)) {
        parsedIngredients = ingredients
          .map((ingredient: any) => {
            if (typeof ingredient === 'object' && ingredient !== null) {
              const name = ingredient.name || '';
              const emoji = ingredient.emoji || getIngredientEmoji(name);
              const amount = ingredient.amount;
              const unit = ingredient.unit || '';
              
              let amountText = '';
              if (amount && unit) {
                amountText = `${amount} ${unit}`;
              } else if (amount) {
                amountText = String(amount);
              }
              
              return { name, amount: amountText, emoji };
            } else {
              const text = String(ingredient);
              return {
                name: text,
                amount: '',
                emoji: getIngredientEmoji(text),
              };
            }
          })
          .filter((item) => item.name && item.name.length > 0);
      }
    } catch (error) {
      console.error('Error parsing ingredients:', error);
    }

    return parsedIngredients;
  };

  const getEquipmentIcon = (equipment: string) => {
    const equipmentMap: Record<string, any> = {
      'oven': require('@/assets/icons/icon-oven.png'),
      'skillet': require('@/assets/icons/icon-skillet.png'),
      'pot': require('@/assets/icons/icon-pot.png'),
      'grill': require('@/assets/icons/icon-grill.png'),
      'microwave': require('@/assets/icons/icon-microwave.png'),
      'crockpot': require('@/assets/icons/icon-crockpot.png'),
      'slow cooker': require('@/assets/icons/icon-crockpot.png'),
      'bowl': require('@/assets/icons/icon-bowl.png'),
      'mixing bowl': require('@/assets/icons/icon-bowl.png'),
    };
    
    const equipmentLower = equipment.toLowerCase();
    return equipmentMap[equipmentLower] || null;
  };

  useEffect(() => {
    if (userMealId) {
      loadMealData();
    }
  }, [userMealId]);

  const loadMealData = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('user_meals')
        .select('*')
        .eq('id', userMealId)
        .single();

      if (error) throw error;

      setMealData(data);
    } catch (error) {
      console.error('Error loading meal data:', error);
      setAlertConfig({
        title: 'Error',
        message: 'Failed to load meal details',
      });
      setShowAlert(true);
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    router.back();
  };

  const handleServingSizeChange = async (newServingSize: number) => {
    setAdjustedServingSize(newServingSize);
    await saveServingSizeAdjustment(newServingSize);
  };

  const saveServingSizeAdjustment = async (newServingSize: number) => {
    if (!session?.user?.id || !mealData || !mealData.id) return;

    try {
      setIsSavingServingSize(true);

      // Get current meal plan
      const { data: mealPlan, error: planError } = await supabase
        .from('weekly_meal_plans')
        .select('id, user_customized_ingredients')
        .eq('user_id', session.user.id)
        .eq('is_active', true)
        .single();

      if (planError || !mealPlan) {
        return;
      }

      // Update customized ingredients with adjusted serving size
      const existingCustomizations = ensureUserCustomizedIngredients(
        mealPlan.user_customized_ingredients
      );
      const updatedCustomizations = {
        ...existingCustomizations,
        [mealData.id]: {
          ...existingCustomizations[mealData.id],
          adjustedServingSize: newServingSize,
          updatedAt: new Date().toISOString(),
        },
      };

      const { error: updateError } = await supabase
        .from('weekly_meal_plans')
        .update({
          user_customized_ingredients: updatedCustomizations,
          updated_at: new Date().toISOString(),
        })
        .eq('id', mealPlan.id);

      if (updateError) throw updateError;
    } catch (error) {
      console.error('Error saving serving size adjustment:', error);
    } finally {
      setIsSavingServingSize(false);
    }
  };

  const handleEdit = () => {
    if (!mealData) return;

    // Navigate to my-recipes with edit mode and data
    router.push({
      pathname: '/(tabs)/my-recipes',
      params: {
        editMode: 'true',
        editData: JSON.stringify(mealData),
      },
    });
  };

  const handleAddToMealPlan = () => {
    setShowDayPicker(true);
  };

  const handleDaySelect = async (day: string) => {
    setShowDayPicker(false);
    await confirmMealAdd(day);
  };

  const confirmMealAdd = async (day: string) => {
    if (!mealData || !session?.user?.id || !day) return;

    try {
      const userCycleStartDay = getGlobalWeeklyCycleStartDay();
      const weekStartDate = getWeekStartDate(userCycleStartDay);

      // Get or create current week's meal plan
      let { data: mealPlan, error: planError } = await supabase
        .from('weekly_meal_plans')
        .select('meal_ids')
        .eq('user_id', session.user.id)
        .eq('week_start_date', weekStartDate)
        .single();

      const dayIndex = [
        'Sunday',
        'Monday',
        'Tuesday',
        'Wednesday',
        'Thursday',
        'Friday',
        'Saturday',
      ].indexOf(day);

      if (planError || !mealPlan) {
        // Create a new meal plan for the week
        const newMealIds = new Array(7).fill(null);
        newMealIds[dayIndex] = mealData.id;

        const { error: insertError } = await supabase
          .from('weekly_meal_plans')
          .insert({
            user_id: session.user.id,
            week_start_date: weekStartDate,
            meal_ids: newMealIds,
            is_active: true,
          });

        if (insertError) throw insertError;
      } else {
        // Check if this meal is already in the meal plan
        const currentMealIds = mealPlan.meal_ids || [];
        if (currentMealIds.includes(mealData.id)) {
          setShowDuplicateAlert(true);
          return;
        }

        // Update existing meal plan
        const updatedMealIds = [
          ...(mealPlan.meal_ids || new Array(7).fill(null)),
        ];
        updatedMealIds[dayIndex] = mealData.id;

        const { error: updateError } = await supabase
          .from('weekly_meal_plans')
          .update({ meal_ids: updatedMealIds })
          .eq('user_id', session.user.id)
          .eq('week_start_date', weekStartDate);

        if (updateError) throw updateError;
      }

      // Navigate to meal plan with refresh flag
      router.push('/(tabs)/meal-plan?refresh=true');
    } catch (error) {
      console.error('Error updating meal plan:', error);
      setAlertConfig({
        title: 'Error',
        message: 'Failed to add recipe to meal plan. Please try again.',
      });
      setShowAlert(true);
    }
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <View style={[styles.header, { paddingTop: insets.top + 10 }]}>
          <TouchableOpacity style={styles.backButton} onPress={handleBack}>
            <IconSymbol name="chevron-left" size={24} color={colors.text} />
          </TouchableOpacity>
          <ThemedText type="title" style={styles.headerTitle}>
            Recipe Details
          </ThemedText>
          <View style={styles.headerActions} />
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <ThemedText>Loading meal details...</ThemedText>
        </View>
      </View>
    );
  }

  if (!mealData) {
    return (
      <View style={styles.container}>
        <View style={[styles.header, { paddingTop: insets.top + 10 }]}>
          <TouchableOpacity style={styles.backButton} onPress={handleBack}>
            <IconSymbol name="chevron-left" size={24} color={colors.text} />
          </TouchableOpacity>
          <ThemedText type="title" style={styles.headerTitle}>
            Recipe Details
          </ThemedText>
          <View style={styles.headerActions} />
        </View>
        <View style={styles.loadingContainer}>
          <ThemedText>Recipe not found</ThemedText>
        </View>
      </View>
    );
  }

  // Get the effective serving size
  const effectiveServingSize =
    adjustedServingSize || mealData.serving_size || 4;
  const originalServingSize = mealData.serving_size || 4;

  // Scale ingredients if serving size was adjusted
  const ingredients = parseIngredients(mealData.ingredients).map(
    (ingredient) => {
      if (adjustedServingSize && adjustedServingSize !== originalServingSize) {
        return scaleIngredient(
          ingredient,
          originalServingSize,
          adjustedServingSize
        );
      }
      return ingredient;
    }
  );
  const instructions: string[] = Array.isArray(mealData.instructions)
    ? mealData.instructions.filter(Boolean).map(String)
    : typeof mealData.instructions === 'string'
      ? mealData.instructions.split('\n').filter(Boolean)
      : [];

  return (
    <>
      <Stack.Screen options={{ headerShown: false }} />
      <View style={styles.container}>
        {/* Header */}
        <View style={[styles.header, { paddingTop: insets.top + 10 }]}>
          <TouchableOpacity style={styles.backButton} onPress={handleBack}>
            <IconSymbol name="chevron-left" size={24} color={colors.text} />
          </TouchableOpacity>
          <ThemedText type="title" style={styles.headerTitle}>
            Recipe Details
          </ThemedText>
          <TouchableOpacity style={styles.editButton} onPress={handleEdit}>
            <Ionicons name="create-outline" size={24} color={colors.text} />
          </TouchableOpacity>
        </View>

        <View style={styles.contentWrapper}>
          <ScrollView
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.scrollContent}
          >
            {/* Meal Image with Equipment Icons Overlay */}
            <View style={styles.mealImageContainer}>
              <Image
                source={getImageSource(mealData.image)}
                style={styles.mealImage}
                resizeMode="cover"
              />
              
              {/* Equipment Icons Overlay - Not available for user meals */}
              {false && (
                <View style={styles.equipmentOverlayContainer}>
                  {[].map((equipment: any, idx: number) => {
                    const icon = getEquipmentIcon(equipment);
                    return icon ? (
                      <View key={idx} style={styles.equipmentOverlayItem}>
                        <Image
                          source={icon}
                          style={styles.equipmentOverlayIcon}
                          contentFit="contain"
                        />
                      </View>
                    ) : null;
                  })}
                </View>
              )}
            </View>

            {/* Meal Info */}
            <View style={styles.content}>

              <ThemedText type="title" style={styles.mealName}>
                {mealData.name}
              </ThemedText>

              <ThemedText style={styles.description}>
                {mealData.description || 'No description provided'}
              </ThemedText>

              {/* Add to Meal Plan Button */}
              <View style={styles.section}>
                <TouchableOpacity
                  style={styles.addToMealPlanButton}
                  onPress={handleAddToMealPlan}
                >
                  <Ionicons
                    name="calendar-outline"
                    size={20}
                    color={colors.primary}
                  />
                  <ThemedText style={styles.addToMealPlanText}>
                    + Add to Meal Plan
                  </ThemedText>
                </TouchableOpacity>
              </View>

              {/* Quick Stats */}
              <View style={styles.statsContainer}>
                <View style={styles.statItem}>
                  <IconSymbol
                    name="clock-outline"
                    size={20}
                    color={colors.primary}
                  />
                  <ThemedText style={styles.statLabel}>Prep</ThemedText>
                  <ThemedText style={styles.statValue}>
                    {formatTime(
                      mealData.prep_time_hour,
                      mealData.prep_time_min
                    )}
                  </ThemedText>
                </View>
                <View style={styles.statItem}>
                  <IconSymbol name="fire" size={20} color={colors.primary} />
                  <ThemedText style={styles.statLabel}>Cook</ThemedText>
                  <ThemedText style={styles.statValue}>
                    {formatTime(
                      mealData.cook_time_hour,
                      mealData.cook_time_min
                    )}
                  </ThemedText>
                </View>
                <View style={styles.statItem}>
                  <IconSymbol
                    name="account-group"
                    size={20}
                    color={colors.primary}
                  />
                  <ThemedText style={styles.statLabel}>Serves</ThemedText>
                  <ThemedText style={styles.statValue}>
                    {adjustedServingSize || mealData.serving_size || 4}
                  </ThemedText>
                </View>
              </View>

              {/* Course and Cuisine Info */}
              <View style={styles.categoryInfo}>
                <View style={styles.categoryItem}>
                  <ThemedText style={styles.categoryText}>
                    {mealData.course || 'Not specified'}
                  </ThemedText>
                </View>
                <View style={styles.categoryItem}>
                  <ThemedText style={styles.categoryText}>
                    {mealData.cuisine_type || 'Not specified'}
                  </ThemedText>
                </View>
              </View>

              {/* Nutrition */}
              {(mealData.calories ||
                mealData.protein ||
                mealData.carbs ||
                mealData.fats) &&
                !(
                  parseInt(String(mealData.calories || '0')) === 0 &&
                  parseInt(String(mealData.protein || '0')) === 0 &&
                  parseInt(String(mealData.carbs || '0')) === 0 &&
                  parseInt(String(mealData.fats || '0')) === 0
                ) && (
                  <View style={styles.section}>
                    <ThemedText type="subtitle" style={styles.sectionTitle}>
                      Nutrition (per serving)
                    </ThemedText>
                    <View style={styles.nutritionGrid}>
                      <CircularProgress
                        size={80}
                        strokeWidth={6}
                        progress={Math.min(
                          parseInt(String(mealData.calories || '0')) / 800,
                          1
                        )} // Assuming 800 cal as high reference
                        color={colors.primary}
                        backgroundColor={
                          activeTheme === 'dark'
                            ? colors.primary + '20'
                            : colors.primary + '15'
                        }
                        value={String(mealData.calories || '0')}
                        label="Calories"
                      />
                      <CircularProgress
                        size={80}
                        strokeWidth={6}
                        progress={Math.min(
                          parseInt(String(mealData.protein || '0')) / 50,
                          1
                        )} // 50g protein as reference
                        color="#F44336"
                        backgroundColor={
                          activeTheme === 'dark' ? '#F4433620' : '#F4433615'
                        }
                        value={`${mealData.protein || 0}g`}
                        label="Protein"
                      />
                      <CircularProgress
                        size={80}
                        strokeWidth={6}
                        progress={Math.min(
                          parseInt(String(mealData.carbs || '0')) / 100,
                          1
                        )} // 100g carbs as reference
                        color="#2196F3"
                        backgroundColor={
                          activeTheme === 'dark' ? '#2196F320' : '#2196F315'
                        }
                        value={`${mealData.carbs || 0}g`}
                        label="Carbs"
                      />
                      <CircularProgress
                        size={80}
                        strokeWidth={6}
                        progress={Math.min(
                          parseInt(String(mealData.fats || '0')) / 35,
                          1
                        )} // 35g fat as reference
                        color="#FF9800"
                        backgroundColor={
                          activeTheme === 'dark' ? '#FF980020' : '#FF980015'
                        }
                        value={`${mealData.fats || 0}g`}
                        label="Fat"
                      />
                    </View>
                  </View>
                )}

              {/* Ingredients */}
              <View style={styles.section}>
                <View style={styles.ingredientsSectionHeader}>
                  <ThemedText type="subtitle" style={styles.sectionTitle}>
                    Ingredients
                  </ThemedText>
                  {adjustedServingSize &&
                    adjustedServingSize !== (mealData.serving_size || 4) && (
                      <View
                        style={[
                          styles.customizedIndicator,
                          { backgroundColor: `${colors.primary}10` },
                        ]}
                      >
                        <Ionicons
                          name="calculator-outline"
                          size={14}
                          color={colors.primary}
                        />
                        <ThemedText
                          style={[
                            styles.customizedText,
                            { color: colors.primary },
                          ]}
                        >
                          Scaled
                        </ThemedText>
                      </View>
                    )}
                </View>
                
                {/* Serving Size Adjustment */}
                <View style={styles.servingAdjustmentContainer}>
                  <ThemedText style={styles.servingAdjustmentText}>
                    Adjust servings
                  </ThemedText>
                  <View style={styles.servingAdjustmentControls}>
                    <TouchableOpacity
                      style={styles.servingAdjustmentButton}
                      onPress={() => {
                        const currentSize =
                          adjustedServingSize || mealData.serving_size || 4;
                        if (currentSize > 2) {
                          handleServingSizeChange(currentSize - 1);
                        }
                      }}
                      disabled={
                        isSavingServingSize ||
                        (adjustedServingSize || mealData.serving_size || 4) <= 2
                      }
                    >
                      <Ionicons
                        name="remove-circle-outline"
                        size={24}
                        color={
                          isSavingServingSize ||
                          (adjustedServingSize || mealData.serving_size || 4) <= 2
                            ? colors.text + '50'
                            : colors.primary
                        }
                      />
                    </TouchableOpacity>
                    <ThemedText style={styles.servingAdjustmentValue}>
                      {adjustedServingSize || mealData.serving_size || 4}
                    </ThemedText>
                    <TouchableOpacity
                      style={styles.servingAdjustmentButton}
                      onPress={() => {
                        const currentSize =
                          adjustedServingSize || mealData.serving_size || 4;
                        if (currentSize < 12) {
                          handleServingSizeChange(currentSize + 1);
                        }
                      }}
                      disabled={isSavingServingSize}
                    >
                      <Ionicons
                        name="add-circle-outline"
                        size={24}
                        color={
                          isSavingServingSize
                            ? colors.text + '50'
                            : colors.primary
                        }
                      />
                    </TouchableOpacity>
                  </View>
                </View>
                <View style={styles.ingredientsColumn}>
                  {ingredients.map(
                    (
                      ingredient: {
                        name: string;
                        amount: string;
                        emoji: string;
                      },
                      index: number
                    ) => (
                      <View
                        key={index}
                        style={[
                          styles.ingredientContainer,
                          {
                            backgroundColor: colors.cardBackground,
                            borderColor: colors.border,
                          },
                        ]}
                      >
                        <Text style={styles.ingredientEmoji}>
                          {ingredient.emoji}
                        </Text>
                        <View style={styles.ingredientTextContainer}>
                          <ThemedText style={styles.ingredientName}>
                            {ingredient.name}
                          </ThemedText>
                          {ingredient.amount && String(ingredient.amount).trim() && (
                            <ThemedText style={styles.ingredientAmount}>
                              {ingredient.amount}
                            </ThemedText>
                          )}
                        </View>
                      </View>
                    )
                  )}
                </View>
              </View>

              {/* Instructions */}
              <View style={styles.section}>
                <View style={styles.instructionsSectionHeader}>
                  <ThemedText type="subtitle" style={styles.sectionTitle}>
                    Instructions
                  </ThemedText>
                  <TouchableOpacity
                    style={styles.zenModeButton}
                    onPress={() => setShowZenMode(true)}
                  >
                    <Ionicons
                      name="expand-outline"
                      size={20}
                      color={colors.primary}
                    />
                    <ThemedText style={styles.zenModeText}>
                      Cook Mode
                    </ThemedText>
                  </TouchableOpacity>
                </View>
                {instructions.map((instruction: string, index: number) => (
                  <View key={index} style={styles.instructionItem}>
                    <View
                      style={[
                        styles.stepNumber,
                        { backgroundColor: colors.primary },
                      ]}
                    >
                      <Text style={styles.stepNumberText}>{index + 1}</Text>
                    </View>
                    <ThemedText style={styles.instructionText}>
                      {instruction}
                    </ThemedText>
                  </View>
                ))}
              </View>
            </View>
          </ScrollView>
        </View>

        {/* Day Picker Modal */}
        <Modal
          visible={showDayPicker}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setShowDayPicker(false)}
        >
          <Pressable
            style={styles.modalOverlay}
            onPress={() => setShowDayPicker(false)}
          >
            <View style={styles.modalContent}>
              <ThemedText style={styles.modalTitle}>
                Choose a day for {mealData?.name}
              </ThemedText>

              {[
                'Sunday',
                'Monday',
                'Tuesday',
                'Wednesday',
                'Thursday',
                'Friday',
                'Saturday',
              ].map((day) => (
                <TouchableOpacity
                  key={day}
                  style={styles.dayOption}
                  onPress={() => handleDaySelect(day)}
                >
                  <ThemedText style={styles.dayText}>{day}</ThemedText>
                  <Ionicons
                    name="chevron-forward"
                    size={20}
                    color={colors.text}
                  />
                </TouchableOpacity>
              ))}

              <TouchableOpacity
                style={styles.cancelButton}
                onPress={() => setShowDayPicker(false)}
              >
                <ThemedText style={styles.cancelText}>Cancel</ThemedText>
              </TouchableOpacity>
            </View>
          </Pressable>
        </Modal>

        <CustomAlert
          visible={showAlert}
          title={alertConfig.title}
          message={alertConfig.message}
          onConfirm={() => setShowAlert(false)}
          onCancel={() => setShowAlert(false)}
        />

        <CustomAlert
          visible={showDuplicateAlert}
          title="Recipe Already Added"
          message={`"${mealData?.name}" is already in your meal plan. Each recipe can only be added once per week.`}
          confirmText="OK"
          onConfirm={() => setShowDuplicateAlert(false)}
          onCancel={() => setShowDuplicateAlert(false)}
        />

        {/* Zen Mode */}
        <ZenMode
          visible={showZenMode}
          instructions={instructions}
          onClose={() => setShowZenMode(false)}
          mealName={mealData?.name || ''}
        />
      </View>
    </>
  );
}

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      gap: 16,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 24,
      paddingBottom: 16,
      justifyContent: 'space-between',
      backgroundColor: colors.background,
    },
    backButton: {
      padding: 8,
    },
    headerTitle: {
      flex: 1,
      textAlign: 'center',
      marginHorizontal: 16,
      fontSize: 18,
      fontWeight: '600',
    },
    headerActions: {
      width: 40,
    },
    contentWrapper: {
      flex: 1,
      backgroundColor: colors.background,
    },
    mealImageContainer: {
      position: 'relative',
      width: '100%',
      height: 250,
      marginBottom: 24,
    },
    mealImage: {
      width: '100%',
      height: '100%',
      resizeMode: 'cover',
    },
    equipmentOverlayContainer: {
      position: 'absolute',
      bottom: 12,
      right: 12,
      flexDirection: 'row',
      gap: 4,
    },
    equipmentOverlayItem: {
      width: 28,
      height: 28,
      borderRadius: 14,
      backgroundColor: 'rgba(255, 255, 255, 0.9)',
      borderWidth: 1,
      borderColor: 'rgba(0, 0, 0, 0.1)',
      alignItems: 'center',
      justifyContent: 'center',
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    },
    equipmentOverlayIcon: {
      width: 18,
      height: 18,
    },
    content: {
      paddingHorizontal: 24,
    },
    mealName: {
      marginBottom: 12,
    },
    description: {
      fontSize: 16,
      lineHeight: 24,
      opacity: 0.7,
      marginBottom: 24,
    },
    statsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: 24,
      paddingVertical: 20,
    },
    statItem: {
      alignItems: 'center',
      flex: 1,
    },
    statLabel: {
      fontSize: 16,
      opacity: 0.6,
      marginTop: 4,
    },
    statValue: {
      fontSize: 18,
      fontWeight: '600',
      marginTop: 2,
    },
    categoryInfo: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 12,
      marginBottom: 32,
    },
    categoryItem: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 6,
      backgroundColor: `${colors.primary}10`,
      paddingHorizontal: 12,
      paddingVertical: 8,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: `${colors.primary}30`,
    },
    categoryText: {
      fontSize: 12,
      fontWeight: '500',
      color: colors.text,
      opacity: 0.9,
    },
    section: {
      marginBottom: 32,
    },
    sectionTitle: {
      marginBottom: 16,
      fontSize: 18,
      fontWeight: '600',
    },
    ingredientsColumn: {
      gap: 12,
    },
    ingredientContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderRadius: 8,
      borderWidth: 1,
      gap: 12,
    },
    ingredientEmoji: {
      fontSize: 18,
    },
    ingredientContainerText: {
      fontSize: 16,
      fontWeight: '500',
      flex: 1,
    },
    ingredientTextContainer: {
      flex: 1,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    ingredientName: {
      fontSize: 16,
      fontWeight: '500',
      flex: 1,
    },
    ingredientAmount: {
      fontSize: 16,
      fontWeight: '600',
      color: colors.primary,
      marginLeft: 8,
    },
    instructionItem: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      marginBottom: 16,
    },
    stepNumber: {
      width: 28,
      height: 28,
      borderRadius: 14,
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: 12,
      marginTop: 2,
    },
    stepNumberText: {
      color: colors.background,
      fontSize: 14,
      fontWeight: '600',
    },
    instructionText: {
      flex: 1,
      fontSize: 16,
      lineHeight: 24,
      paddingTop: 3,
    },
    nutritionGrid: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      paddingVertical: 8,
    },
    scrollContent: {
      paddingBottom: 24,
    },
    editButton: {
      padding: 8,
    },
    addToMealPlanButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      borderWidth: 1,
      borderColor: colors.primary,
      paddingVertical: 12,
      paddingHorizontal: 16,
      borderRadius: 8,
      backgroundColor: 'transparent',
      gap: 8,
    },
    addToMealPlanText: {
      fontSize: 16,
      fontWeight: '600',
      color: colors.primary,
    },
    modalOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'flex-end',
    },
    modalContent: {
      backgroundColor: colors.cardBackground,
      borderTopLeftRadius: 20,
      borderTopRightRadius: 20,
      padding: 24,
      paddingBottom: 40,
    },
    modalTitle: {
      fontSize: 20,
      fontWeight: '600',
      color: colors.text,
      marginBottom: 20,
      textAlign: 'center',
    },
    dayOption: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: 16,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    dayText: {
      fontSize: 18,
      color: colors.text,
    },
    cancelButton: {
      marginTop: 20,
      paddingVertical: 12,
      alignItems: 'center',
    },
    cancelText: {
      fontSize: 16,
      color: colors.text,
      opacity: 0.7,
    },
    instructionsSectionHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 16,
    },
    zenModeButton: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 6,
      paddingHorizontal: 12,
      paddingVertical: 8,
      borderRadius: 16,
      backgroundColor: `${colors.primary}10`,
      borderWidth: 1,
      borderColor: `${colors.primary}30`,
    },
    zenModeText: {
      fontSize: 14,
      fontWeight: '500',
      color: colors.primary,
    },
    ingredientsSectionHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 16,
    },
    customizedIndicator: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 4,
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12,
    },
    customizedText: {
      fontSize: 12,
      fontWeight: '500',
    },
    servingAdjustmentContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: 16,
      paddingHorizontal: 16,
      paddingVertical: 12,
      backgroundColor: `${colors.primary}10`,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: `${colors.primary}20`,
    },
    servingAdjustmentText: {
      fontSize: 14,
      fontWeight: '500',
      color: colors.text,
      opacity: 0.8,
    },
    servingAdjustmentControls: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 12,
    },
    servingAdjustmentButton: {
      padding: 4,
    },
    servingAdjustmentValue: {
      fontSize: 16,
      fontWeight: '600',
      minWidth: 20,
      textAlign: 'center',
    },
    equipmentContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 8,
      marginBottom: 16,
    },
    equipmentItem: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: '#F5F5F5',
      borderWidth: 1,
      borderColor: '#E0E0E0',
      alignItems: 'center',
      justifyContent: 'center',
      padding: 4,
    },
    equipmentIcon: {
      width: 28,
      height: 28,
    },
  });

export default function ProtectedUserMealDetailScreen() {
  return <UserMealDetailScreen />;
}
