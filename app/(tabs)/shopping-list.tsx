import { CustomAlert } from '@/components/CustomAlert';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Button, LoadingIndicator } from '@/components/ui';
import { Modal ,
  ActivityIndicator,
  Animated,
  Easing,
  Platform,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import { Colors } from '@/constants/Colors';
import { useAuth } from '@/contexts/AuthContext';
import { useTheme } from '@/contexts/ThemeContext';
import { useTabBarHeight } from '@/hooks/useTabBarHeight';
import { useStaggeredAnimation } from '@/hooks/useStaggeredAnimation';
import { getIngredientEmoji } from '@/lib/ingredientEmoji';
import { scaleIngredients } from '@/lib/ingredientScaling';
import {
  consolidateIngredients,
  getIngredientCategory,
} from '@/lib/simpleIngredientConsolidation';
import {
  createInstacartShoppingList,
  generateTrackingUrl,
  getPartnerLinkbackUrl,
  type AffiliateTrackingData,
} from '@/lib/instacartService';
import { supabase } from '@/lib/supabase';
import { displayError, handleAsync } from '@/lib/errorHandling';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { SecureStorage } from '@/lib/secureStorage';
import { useFocusEffect, useLocalSearchParams } from 'expo-router';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import type { Json } from '@/lib/database.types';
import { WebView } from 'react-native-webview';
import { Image } from 'expo-image';

// Type definitions for better type safety
interface IngredientCustomization {
  ingredients: any[];
  originalMealId: string;
  adjustedServingSize?: number;
  originalServingSize?: number;
  updatedAt: string;
}

interface UserCustomizedIngredients {
  [mealId: string]: IngredientCustomization;
}

// Type guard functions
function isUserCustomizedIngredients(
  value: Json | null
): value is Record<string, any> {
  return value !== null && typeof value === 'object' && !Array.isArray(value);
}

function ensureUserCustomizedIngredients(
  value: Json | null
): Record<string, any> {
  if (isUserCustomizedIngredients(value)) {
    return value;
  }
  return {};
}

interface ShoppingListItem {
  id: string;
  name: string;
  category: string;
  isChecked: boolean;
  originalText: string;
  totalAmount?: string; // For consolidated amounts
  emoji?: string; // Emoji for the ingredient
  instacartUnit?: {
    code: string;
    name: string;
    original_unit: string;
  };
}

interface ParsedIngredient {
  name: string;
  category: string;
  originalText: string;
  emoji?: string;
}

export default function ShoppingListScreen() {
  const { activeTheme } = useTheme();
  const colors = Colors[activeTheme];
  const styles = createStyles(colors);
  const { session } = useAuth();
  const params = useLocalSearchParams();
  const { contentBottomPadding } = useTabBarHeight();
  const scrollViewRef = useRef<ScrollView>(null);
  const [shoppingList, setShoppingList] = useState<ShoppingListItem[]>([]);
  const [sideDishSections, setSideDishSections] = useState<any[]>([]);
  const [aiGeneratedSections, setAiGeneratedSections] = useState<any[]>([]);

  // Use staggered animation hook for 3 sections: title, content, walmart button
  const { sections: animatedSections } = useStaggeredAnimation(3);
  const [titleSection, contentSection, buttonSection] = animatedSections;
  
  // Individual ingredient animations
  const ingredientAnimations = useRef<Map<string, {opacity: Animated.Value, translateY: Animated.Value}>>(new Map());
  const [animationsReady, setAnimationsReady] = useState(false);
  const [loading, setLoading] = useState(true);
  const [showAlert, setShowAlert] = useState(false);
  const [alertConfig, setAlertConfig] = useState({ title: '', message: '' });
  const [showWalmart, setShowWalmart] = useState(false);
  const [showInstacart, setShowInstacart] = useState(false);
  const [instacartUrl, setInstacartUrl] = useState<string>('');
  const [consolidatedIngredientsData, setConsolidatedIngredientsData] =
    useState<any[]>([]);
  const [currentSearchIndex, setCurrentSearchIndex] = useState(0);
  const [checkedItems, setCheckedItems] = useState<Set<string>>(new Set());
  const [webViewLoading, setWebViewLoading] = useState(false);
  const [affiliateData, setAffiliateData] =
    useState<AffiliateTrackingData | null>(null);
  const [walmartAddedItems, setWalmartAddedItems] = useState<Set<string>>(
    new Set()
  );
  const [showWalmartListView, setShowWalmartListView] = useState(false);
  const [showShopOnlineModal, setShowShopOnlineModal] = useState(false);

  // Function to get or create animation for an ingredient
  const getIngredientAnimation = useCallback((itemId: string) => {
    if (!ingredientAnimations.current.has(itemId)) {
      const opacity = new Animated.Value(0);
      const translateY = new Animated.Value(30);
      // Ensure initial values are set immediately
      opacity.setValue(0);
      translateY.setValue(30);
      ingredientAnimations.current.set(itemId, { opacity, translateY });
    }
    return ingredientAnimations.current.get(itemId)!;
  }, []);

  // Function to animate ingredients with staggered delays
  const animateIngredients = useCallback(() => {
    const allItems: Array<{id: string, index: number}> = [];
    
    // Collect all items in display order
    let currentIndex = 0;
    
    // Side dish ingredients first
    sideDishSections.forEach((section) => {
      section.ingredients.forEach((ingredient: ShoppingListItem) => {
        allItems.push({ id: ingredient.id, index: currentIndex++ });
      });
    });
    
    // AI-generated ingredients second
    aiGeneratedSections.forEach((section) => {
      section.ingredients.forEach((ingredient: ShoppingListItem) => {
        allItems.push({ id: ingredient.id, index: currentIndex++ });
      });
    });
    
    // Main ingredients last
    shoppingList.forEach((ingredient) => {
      allItems.push({ id: ingredient.id, index: currentIndex++ });
    });

    // Start staggered animations (only animate items that aren't already animated)
    allItems.forEach(({ id, index }) => {
      const animation = getIngredientAnimation(id);
      const delay = index * 50; // 50ms delay between each item
      
      setTimeout(() => {
        Animated.parallel([
          Animated.timing(animation.opacity, {
            toValue: 1,
            duration: 400,
            easing: Easing.out(Easing.cubic),
            useNativeDriver: true,
          }),
          Animated.timing(animation.translateY, {
            toValue: 0,
            duration: 400,
            easing: Easing.out(Easing.cubic),
            useNativeDriver: true,
          }),
        ]).start();
      }, delay);
    });
  }, [shoppingList, sideDishSections, aiGeneratedSections, getIngredientAnimation]);

  // Reload shopping list when screen comes into focus or refresh is requested
  useFocusEffect(
    useCallback(() => {
      // Scroll to top when tab comes into focus
      scrollViewRef.current?.scrollTo({ y: 0, animated: false });

      const checkForUpdates = async () => {
        if (!session?.user?.id) return;

        // Check if meal plan was updated
        const lastUpdate = await AsyncStorage.getItem('mealPlanUpdated');
        const lastCheck = await SecureStorage.getItem(
          `shoppingListLastCheck_${session.user.id}`
        );

        if (
          shoppingList.length === 0 ||
          params.refresh === 'true' ||
          (lastUpdate &&
            lastCheck &&
            parseInt(lastUpdate) > parseInt(lastCheck))
        ) {
          loadShoppingList();
          // Update last check timestamp
          await SecureStorage.setItem(
            `shoppingListLastCheck_${session.user.id}`,
            Date.now().toString()
          );
        }
      };

      checkForUpdates();
    }, [session?.user?.id, shoppingList.length, params.refresh])
  );

  useEffect(() => {
    if (session?.user?.id) {
      loadShoppingList();
      loadCheckedItems();
      loadWalmartAddedItems();
      loadAffiliateData();
    }
  }, [session]);

  // Trigger ingredient animations when data is loaded and not loading
  useEffect(() => {
    if (!loading && (shoppingList.length > 0 || sideDishSections.length > 0 || aiGeneratedSections.length > 0)) {
      // Small delay to ensure content is rendered
      const timer = setTimeout(() => {
        animateIngredients();
      }, 100);
      
      return () => clearTimeout(timer);
    }
  }, [loading, shoppingList.length, sideDishSections.length, aiGeneratedSections.length, animateIngredients]);

  const loadAffiliateData = async () => {
    if (!session?.user?.id) return;

    try {
      // Check if user is an affiliate or was referred by one
      const { data: userPrefs, error: prefsError } = await supabase
        .from('user_preferences')
        .select('referred_by_affiliate_code')
        .eq('user_id', session.user.id)
        .single();

      if (prefsError && prefsError.code !== 'PGRST116') {
        return;
      }

      // Check if user is an affiliate themselves
      const { data: affiliate, error: affiliateError } = await supabase
        .from('affiliates')
        .select('affiliate_code, coupon_code')
        .eq('user_id', session.user.id)
        .single();

      if (affiliateError && affiliateError.code !== 'PGRST116') {
        // Affiliate data not found or error loading
      }

      // Set affiliate data for tracking - prioritize user's own affiliate code
      const trackingData: AffiliateTrackingData = {
        userId: session.user.id,
        userEmail: session.user.email || undefined,
        affiliateCode:
          affiliate?.coupon_code ||
          affiliate?.affiliate_code ||
          userPrefs?.referred_by_affiliate_code ||
          undefined,
      };

      setAffiliateData(trackingData);
    } catch {
      // Error loading affiliate data
    }
  };

  const loadCheckedItems = async () => {
    try {
      const stored = await SecureStorage.getItem(
        `checkedItems_${session?.user?.id}`
      );
      if (stored) {
        setCheckedItems(new Set(JSON.parse(stored)));
      }
    } catch {
      // Error loading checked items
    }
  };

  const saveCheckedItems = async (newCheckedItems: Set<string>) => {
    try {
      await SecureStorage.setItem(
        `checkedItems_${session?.user?.id}`,
        JSON.stringify(Array.from(newCheckedItems))
      );
    } catch {
      // Error saving checked items
    }
  };

  const loadWalmartAddedItems = async () => {
    try {
      const stored = await SecureStorage.getItem(
        `walmartAddedItems_${session?.user?.id}`
      );
      if (stored) {
        setWalmartAddedItems(new Set(JSON.parse(stored)));
      }
    } catch {
      // Error loading Walmart added items
    }
  };

  const saveWalmartAddedItems = async (newAddedItems: Set<string>) => {
    try {
      await SecureStorage.setItem(
        `walmartAddedItems_${session?.user?.id}`,
        JSON.stringify(Array.from(newAddedItems))
      );
    } catch {
      // Error saving Walmart added items
    }
  };

  const deleteSideDish = async (sideDishId: string, sideDishName: string) => {
    if (!session?.user?.id) return;

    try {
      // Get current meal plan
      const { data: mealPlan, error: planError } = await supabase
        .from('weekly_meal_plans')
        .select('id, additional_ingredients, selected_side_dishes')
        .eq('user_id', session.user.id)
        .eq('is_active', true)
        .single();

      if (planError || !mealPlan) {
        setAlertConfig({
          title: 'Error',
          message: 'Unable to delete side dish',
        });
        setShowAlert(true);
        return;
      }

      // Remove from additional_ingredients
      const additionalIngredients = Array.isArray(
        mealPlan.additional_ingredients
      )
        ? mealPlan.additional_ingredients
        : [];
      const filteredIngredients = additionalIngredients.filter(
        (item: any) => item.source_id !== sideDishId
      );

      // Remove from selected_side_dishes
      const selectedSideDishes = Array.isArray(mealPlan.selected_side_dishes)
        ? mealPlan.selected_side_dishes
        : [];
      const filteredSideDishes = selectedSideDishes.filter(
        (dish: any) => dish.id !== sideDishId
      );

      // Update database
      const { error: updateError } = await supabase
        .from('weekly_meal_plans')
        .update({
          additional_ingredients: filteredIngredients,
          selected_side_dishes: filteredSideDishes,
          updated_at: new Date().toISOString(),
        })
        .eq('id', mealPlan.id);

      if (updateError) throw updateError;

      // Update local state
      setSideDishSections((prev) =>
        prev.filter((section) => section.id !== sideDishId)
      );

      // Trigger meal plan refresh
      await AsyncStorage.setItem('mealPlanUpdated', Date.now().toString());

      setAlertConfig({
        title: 'Side Dish Deleted',
        message: `"${sideDishName}" has been removed from your meal plan.`,
      });
      setShowAlert(true);
    } catch {
      setAlertConfig({ title: 'Error', message: 'Failed to delete side dish' });
      setShowAlert(true);
    }
  };

  const deleteAiGeneratedRecipe = async (
    recipeId: string,
    recipeName: string
  ) => {
    if (!session?.user?.id) return;

    try {
      // Get current meal plan
      const { data: mealPlan, error: planError } = await supabase
        .from('weekly_meal_plans')
        .select('id, ai_generated_meals')
        .eq('user_id', session.user.id)
        .eq('is_active', true)
        .single();

      if (planError || !mealPlan) {
        setAlertConfig({
          title: 'Error',
          message: 'Unable to delete AI recipe',
        });
        setShowAlert(true);
        return;
      }

      // Remove from ai_generated_meals
      const aiGeneratedMeals = Array.isArray(mealPlan.ai_generated_meals)
        ? mealPlan.ai_generated_meals
        : [];
      const filteredMeals = aiGeneratedMeals.filter(
        (meal: any) => meal.id !== recipeId
      );

      // Update database
      const { error: updateError } = await supabase
        .from('weekly_meal_plans')
        .update({
          ai_generated_meals: filteredMeals,
          updated_at: new Date().toISOString(),
        })
        .eq('id', mealPlan.id);

      if (updateError) throw updateError;

      // Update local state
      setAiGeneratedSections((prev) =>
        prev.filter((section) => section.id !== recipeId)
      );

      // Trigger meal plan refresh
      await AsyncStorage.setItem('mealPlanUpdated', Date.now().toString());

      setAlertConfig({
        title: 'AI Recipe Deleted',
        message: `"${recipeName}" has been removed from your meal plan.`,
      });
      setShowAlert(true);
    } catch {
      setAlertConfig({ title: 'Error', message: 'Failed to delete AI recipe' });
      setShowAlert(true);
    }
  };

  const parseIngredient = (ingredientText: string): ParsedIngredient => {
    // Ensure ingredientText is a string
    const textString =
      typeof ingredientText === 'string'
        ? ingredientText
        : String(ingredientText || '');
    const text = textString.toLowerCase().trim();

    // Determine category based on keywords
    let category = 'Other';

    if (
      text.includes('chicken') ||
      text.includes('beef') ||
      text.includes('pork') ||
      text.includes('fish') ||
      text.includes('salmon') ||
      text.includes('meat') ||
      text.includes('turkey') ||
      text.includes('lamb')
    ) {
      category = 'Meat & Seafood';
    } else if (
      text.includes('milk') ||
      text.includes('cheese') ||
      text.includes('butter') ||
      text.includes('cream') ||
      text.includes('yogurt') ||
      text.includes('egg')
    ) {
      category = 'Dairy & Eggs';
    } else if (
      text.includes('tomato') ||
      text.includes('onion') ||
      text.includes('pepper') ||
      text.includes('carrot') ||
      text.includes('potato') ||
      text.includes('lettuce') ||
      text.includes('spinach') ||
      text.includes('broccoli') ||
      text.includes('cucumber') ||
      text.includes('garlic') ||
      text.includes('ginger') ||
      text.includes('herb') ||
      text.includes('basil') ||
      text.includes('parsley') ||
      text.includes('cilantro')
    ) {
      category = 'Fresh Produce';
    } else if (
      text.includes('rice') ||
      text.includes('pasta') ||
      text.includes('bread') ||
      text.includes('flour') ||
      text.includes('oats') ||
      text.includes('quinoa') ||
      text.includes('noodle') ||
      text.includes('cereal')
    ) {
      category = 'Pantry & Grains';
    } else if (
      text.includes('oil') ||
      text.includes('vinegar') ||
      text.includes('sauce') ||
      text.includes('spice') ||
      text.includes('salt') ||
      text.includes('pepper') ||
      text.includes('honey') ||
      text.includes('sugar') ||
      text.includes('vanilla')
    ) {
      category = 'Condiments & Spices';
    } else if (
      text.includes('can') ||
      text.includes('jar') ||
      text.includes('bottle') ||
      text.includes('dried') ||
      text.includes('canned')
    ) {
      category = 'Pantry & Canned';
    }

    // Extract ingredient name (remove quantities and measurements)
    let name = textString
      // Remove leading numbers with optional decimals or fractions
      .replace(/^(\d+\.?\d*|\d+\/\d+|\d+\s+\d+\/\d+)\s*/, '')
      // Remove units
      .replace(
        /\b(cup|cups|tbsp|tsp|tablespoon|tablespoons|teaspoon|teaspoons|oz|ounces|lb|lbs|pound|pounds|g|grams|kg|ml|liter|liters|clove|cloves|can|jar|bottle|package|pkg|stick|sticks|piece|pieces|slice|slices)\b/gi,
        ''
      )
      // Remove any remaining numbers that might be quantities
      .replace(/^(\d+\.?\d*|\d+\/\d+|\d+\s+\d+\/\d+)\s*/, '')
      // Remove descriptive words
      .replace(
        /\b(of|to|taste|fresh|dried|chopped|sliced|minced|diced|crushed|ground)\b/gi,
        ''
      )
      .replace(/\s+/g, ' ')
      .trim();

    // Remove leading commas or "of"
    name = name.replace(/^(,|of)\s*/, '').trim();

    // Capitalize first letter
    name = name.charAt(0).toUpperCase() + name.slice(1);

    return {
      name: name || textString, // Fallback to original if parsing fails
      category,
      originalText: textString,
      emoji: getIngredientEmoji(textString),
    };
  };

  const loadShoppingList = async () => {
    if (!session?.user?.id) return;

    try {
      setLoading(true);

      // Get current week's meal plan
      const { data: mealPlan, error: planError } = await supabase
        .from('weekly_meal_plans')
        .select(
          'meal_ids, additional_ingredients, ai_generated_meals, disabled_days, user_customized_ingredients'
        )
        .eq('user_id', session.user.id)
        .eq('is_active', true)
        .single();

      if (planError || !mealPlan?.meal_ids) {
        setShoppingList([]);
        return;
      }

      // Filter out meals from disabled days
      // meal_ids array corresponds to days 0-6 (Sunday-Saturday)
      const disabledDays = mealPlan.disabled_days || [];
      const enabledMealIds = mealPlan.meal_ids
        .filter(Boolean) // Remove null/undefined values
        .filter((mealId: string, index: number) => {
          const isEnabled = !disabledDays.includes(index);

          return isEnabled;
        });

      if (enabledMealIds.length === 0) {
        setShoppingList([]);
        return;
      }

      // Get meal details with ingredients and serving sizes
      const { data: meals, error: mealsError } = await supabase
        .from('meals')
        .select('id, name, ingredients, servingSize')
        .in('id', enabledMealIds);

      if (mealsError) throw mealsError;

      // Collect all ingredients for consolidation
      const allIngredientsToConsolidate: {
        text: string;
        mealId?: string;
        mealName?: string;
      }[] = [];

      meals?.forEach((meal) => {
        let ingredients: any[] = [];

        // Check if there are customized ingredients for this meal
        const customizations = ensureUserCustomizedIngredients(
          mealPlan.user_customized_ingredients
        );
        const customization = customizations[meal.id];

        // Use customized ingredients if available, otherwise use original ingredients
        const mealIngredients: any =
          customization?.ingredients || meal.ingredients;

        if (mealIngredients) {
          if (typeof mealIngredients === 'string') {
            ingredients = (mealIngredients as string)
              .split('\n')
              .filter(Boolean);
          } else if (Array.isArray(mealIngredients)) {
            ingredients = mealIngredients.filter(Boolean);
          } else if (typeof mealIngredients === 'object') {
            try {
              const ingredientsObj = mealIngredients as any;
              if (Array.isArray(ingredientsObj)) {
                ingredients = ingredientsObj.filter(Boolean);
              } else if (typeof ingredientsObj === 'string') {
                ingredients = ingredientsObj.split('\n').filter(Boolean);
              }
            } catch {
              // Error parsing ingredients object
            }
          }
        }

        // Apply serving size scaling if needed
        if (
          customization?.originalServingSize &&
          customization?.adjustedServingSize &&
          customization.originalServingSize !==
            customization.adjustedServingSize
        ) {
          // Convert ingredients to objects with emoji if they're just strings
          const formattedIngredients = ingredients.map((ingredient) => {
            if (typeof ingredient === 'string') {
              return {
                name: ingredient,
                amount: '', // Will be parsed from the ingredient string
                emoji: getIngredientEmoji(ingredient),
              };
            }
            return ingredient;
          });

          // Scale ingredients based on serving size difference
          const scaledIngredients = scaleIngredients(
            formattedIngredients,
            customization.originalServingSize,
            customization.adjustedServingSize
          );

          // Convert back to string format for processing
          ingredients = scaledIngredients.map((ingredient) =>
            ingredient.amount
              ? `${ingredient.amount} ${ingredient.name}`
              : ingredient.name
          );
        }

        ingredients.forEach((ingredient) => {
          if (!ingredient) return;

          let originalText = '';

          if (
            typeof ingredient === 'object' &&
            ingredient !== null &&
            ingredient.name
          ) {
            // Handle both database format: {name, unit, amount, emoji} and customized format: {name, amount, emoji}
            if (ingredient.unit !== undefined) {
              // Database format with separate unit field
              const amount = parseFloat(ingredient.amount) || 0;
              const unit = ingredient.unit || '';
              
              // Skip ingredients with no amount and no unit (e.g., amount: 0, unit: '')
              if (amount === 0 && !unit.trim()) {
                return; // Skip this ingredient - don't add to shopping list
              }
              
              originalText =
                amount > 0 && unit.trim()
                  ? `${amount} ${unit} ${ingredient.name}`
                  : amount > 0
                    ? `${amount} ${ingredient.name}`
                    : unit.trim()
                      ? `${unit} ${ingredient.name}`
                      : ingredient.name;
            } else {
              // Customized format with amount containing both amount and unit
              const amount = ingredient.amount || '';
              originalText = amount
                ? `${amount} ${ingredient.name}`
                : ingredient.name;
            }
          } else {
            // Handle string format
            originalText = String(ingredient);
          }

          if (originalText && originalText.length >= 2) {
            allIngredientsToConsolidate.push({
              text: originalText,
              mealId: meal.id,
              mealName: meal.name,
            });
          }
        });
      });

      // Also collect side dish ingredients for consolidation
      if (
        mealPlan.additional_ingredients &&
        Array.isArray(mealPlan.additional_ingredients)
      ) {
        mealPlan.additional_ingredients.forEach((additionalItem: any) => {
          if (
            additionalItem.source === 'side_dish' &&
            additionalItem.ingredients &&
            Array.isArray(additionalItem.ingredients)
          ) {
            // Add side dish ingredients to consolidation array
            additionalItem.ingredients.forEach((ingredient: any) => {
              if (!ingredient) return;

              let originalText = '';
              if (
                typeof ingredient === 'object' &&
                ingredient !== null &&
                ingredient.name
              ) {
                if (ingredient.unit !== undefined) {
                  // Database format with separate unit field
                  const amount = parseFloat(ingredient.amount) || 0;
                  const unit = ingredient.unit || '';
                  
                  // Skip ingredients with no amount and no unit (e.g., amount: 0, unit: '')
                  if (amount === 0 && !unit.trim()) {
                    return; // Skip this ingredient - don't add to shopping list
                  }
                  
                  originalText =
                    amount > 0 && unit.trim()
                      ? `${amount} ${unit} ${ingredient.name}`
                      : amount > 0
                        ? `${amount} ${ingredient.name}`
                        : unit.trim()
                          ? `${unit} ${ingredient.name}`
                          : ingredient.name;
                } else {
                  // Customized format with amount containing both amount and unit
                  const amount = ingredient.amount || '';
                  originalText = amount
                    ? `${amount} ${ingredient.name}`
                    : ingredient.name;
                }
              } else {
                originalText = String(ingredient);
              }

              if (originalText && originalText.length >= 2) {
                allIngredientsToConsolidate.push({
                  text: originalText,
                  mealId: `side_${additionalItem.source_id}`,
                  mealName: additionalItem.source_name,
                });
              }
            });
          }
        });
      }

      // Process side dish ingredients separately for UI sections
      const processedSideDishes: any[] = [];
      if (
        mealPlan.additional_ingredients &&
        Array.isArray(mealPlan.additional_ingredients)
      ) {
        mealPlan.additional_ingredients.forEach((additionalItem: any) => {
          if (
            additionalItem.source === 'side_dish' &&
            additionalItem.ingredients &&
            Array.isArray(additionalItem.ingredients)
          ) {
            const sideDishIngredients: ShoppingListItem[] = [];

            additionalItem.ingredients.forEach(
              (ingredient: any, index: number) => {
                if (!ingredient) return;

                let ingredientName = '';
                let amount = 0;
                let unit = '';
                let originalText = '';
                let emoji = '';

                if (typeof ingredient === 'object' && ingredient !== null) {
                  if (ingredient.name) {
                    ingredientName = ingredient.name;
                    emoji =
                      ingredient.emoji || getIngredientEmoji(ingredientName);

                    if (ingredient.unit !== undefined) {
                      // Database format with separate unit field
                      amount = parseFloat(ingredient.amount) || 0;
                      unit = ingredient.unit || '';
                      
                      // Skip ingredients with no amount and no unit (e.g., amount: 0, unit: '')
                      if (amount === 0 && !unit.trim()) {
                        return; // Skip this ingredient - don't add to shopping list
                      }
                      
                      originalText = amount > 0 && unit.trim()
                        ? `${amount} ${unit} ${ingredientName}`.trim()
                        : amount > 0
                          ? `${amount} ${ingredientName}`.trim()
                          : unit.trim()
                            ? `${unit} ${ingredientName}`.trim()
                            : ingredientName;
                    } else {
                      // Customized format with amount containing both amount and unit
                      const amountStr = ingredient.amount || '';
                      originalText = amountStr
                        ? `${amountStr} ${ingredientName}`
                        : ingredientName;
                      amount = 0; // Can't easily parse combined amount for numeric operations
                      unit = '';
                    }
                  }
                } else {
                  originalText = String(ingredient);
                  ingredientName = originalText;
                  emoji = getIngredientEmoji(ingredientName);
                }

                if (ingredientName && ingredientName.length >= 2) {
                  const parsed = parseIngredient(originalText);
                  const totalAmount =
                    ingredient.unit !== undefined
                      ? amount > 0 && unit
                        ? `${amount} ${unit}`
                        : ''
                      : ingredient.amount || '';

                  sideDishIngredients.push({
                    id: `side_${additionalItem.source_id}_${index}`,
                    name: parsed.name,
                    category: parsed.category,
                    isChecked: false,
                    originalText,
                    totalAmount,
                    emoji:
                      emoji || parsed.emoji || getIngredientEmoji(parsed.name),
                  });
                }
              }
            );

            if (sideDishIngredients.length > 0) {
              processedSideDishes.push({
                id: additionalItem.source_id,
                name: additionalItem.source_name,
                ingredients: sideDishIngredients,
              });
            }
          }
        });
      }

      setSideDishSections(processedSideDishes);

      // Also collect AI-generated meal ingredients for consolidation
      if (
        mealPlan.ai_generated_meals &&
        Array.isArray(mealPlan.ai_generated_meals)
      ) {
        mealPlan.ai_generated_meals.forEach((aiMeal: any) => {
          if (
            aiMeal &&
            aiMeal.ingredients &&
            Array.isArray(aiMeal.ingredients)
          ) {
            aiMeal.ingredients.forEach((ingredient: any) => {
              if (!ingredient) return;

              let originalText = '';

              // AI-generated recipes should have object format: {name, unit, amount, emoji}
              if (
                typeof ingredient === 'object' &&
                ingredient !== null &&
                ingredient.name
              ) {
                const amount = parseFloat(ingredient.amount) || 0;
                const unit = ingredient.unit || '';
                
                // Skip ingredients with no amount and no unit (e.g., amount: 0, unit: '')
                if (amount === 0 && !unit.trim()) {
                  return; // Skip this ingredient - don't add to shopping list
                }
                
                originalText =
                  amount > 0 && unit.trim()
                    ? `${amount} ${unit} ${ingredient.name}`
                    : amount > 0
                      ? `${amount} ${ingredient.name}`
                      : unit.trim()
                        ? `${unit} ${ingredient.name}`
                        : ingredient.name;
              } else if (typeof ingredient === 'string') {
                // Fallback for legacy string format
                originalText = ingredient;
              } else {
                originalText = String(ingredient);
              }

              if (originalText && originalText.length >= 2) {
                allIngredientsToConsolidate.push({
                  text: originalText,
                  mealId: `ai_${aiMeal.id}`,
                  mealName: aiMeal.name || aiMeal.title || 'AI Recipe',
                });
              }
            });
          }
        });
      }

      // Process AI-generated recipe ingredients separately for UI sections
      const processedAiGenerated: any[] = [];
      if (
        mealPlan.ai_generated_meals &&
        Array.isArray(mealPlan.ai_generated_meals)
      ) {
        mealPlan.ai_generated_meals.forEach((aiMeal: any) => {
          if (
            aiMeal &&
            aiMeal.ingredients &&
            Array.isArray(aiMeal.ingredients)
          ) {
            const aiIngredients: ShoppingListItem[] = [];

            aiMeal.ingredients.forEach((ingredient: any, index: number) => {
              if (!ingredient) return;

              let ingredientName = '';
              let amount = 0;
              let unit = '';
              let originalText = '';
              let emoji = '';

              // AI-generated recipes should have object format: {name, unit, amount, emoji}
              if (typeof ingredient === 'object' && ingredient !== null) {
                if (ingredient.name) {
                  ingredientName = ingredient.name;
                  amount = parseFloat(ingredient.amount) || 0;
                  unit = ingredient.unit || '';
                  emoji =
                    ingredient.emoji || getIngredientEmoji(ingredientName);
                  
                  // Skip ingredients with no amount and no unit (e.g., amount: 0, unit: '')
                  if (amount === 0 && !unit.trim()) {
                    return; // Skip this ingredient - don't add to shopping list
                  }
                  
                  originalText = amount > 0 && unit.trim()
                    ? `${amount} ${unit} ${ingredientName}`.trim()
                    : amount > 0
                      ? `${amount} ${ingredientName}`.trim()
                      : unit.trim()
                        ? `${unit} ${ingredientName}`.trim()
                        : ingredientName;
                } else {
                  originalText = String(ingredient);
                  ingredientName = originalText;
                  emoji = getIngredientEmoji(ingredientName);
                }
              } else if (typeof ingredient === 'string') {
                // Fallback for legacy string format
                originalText = ingredient;
                ingredientName = ingredient;
                emoji = getIngredientEmoji(ingredientName);
              }

              if (ingredientName && ingredientName.length >= 2) {
                const parsed = parseIngredient(originalText);
                const totalAmount =
                  amount > 0 && unit ? `${amount} ${unit}` : '';

                aiIngredients.push({
                  id: `ai_${aiMeal.id}_${index}`,
                  name: parsed.name,
                  category: parsed.category,
                  isChecked: false,
                  originalText,
                  totalAmount,
                  emoji:
                    emoji || parsed.emoji || getIngredientEmoji(parsed.name),
                });
              }
            });

            if (aiIngredients.length > 0) {
              processedAiGenerated.push({
                id: aiMeal.id,
                name: aiMeal.name || aiMeal.title || 'AI Recipe',
                ingredients: aiIngredients,
              });
            }
          }
        });
      }

      setAiGeneratedSections(processedAiGenerated);

      // Consolidate ingredients by combining amounts of like items
      const consolidatedIngredients = consolidateIngredients(
        allIngredientsToConsolidate
      );

      // Convert consolidated ingredients to shopping list items
      const allIngredients: ShoppingListItem[] = consolidatedIngredients.map(
        (ingredient, index) => {
          return {
            id: `ingredient_${index}`,
            name: ingredient.name,
            category: getIngredientCategory(ingredient.name),
            isChecked: false,
            originalText: ingredient.displayText,
            totalAmount: ingredient.displayText.replace(ingredient.name, '').trim(),
            emoji: ingredient.emoji || getIngredientEmoji(ingredient.name),
          };
        }
      );

      // Store consolidated ingredients for Instacart
      setConsolidatedIngredientsData(consolidatedIngredients);

      // Sort alphabetically by name for easy browsing
      const sortedIngredients = allIngredients.sort((a, b) => {
        return a.name.localeCompare(b.name);
      });

      setShoppingList(sortedIngredients);
    } catch {
      setAlertConfig({
        title: 'Error',
        message: 'Failed to load shopping list',
      });
      setShowAlert(true);
    } finally {
      setLoading(false);
    }
  };

  const toggleItem = async (itemId: string) => {
    const newCheckedItems = new Set(checkedItems);
    if (newCheckedItems.has(itemId)) {
      newCheckedItems.delete(itemId);
    } else {
      newCheckedItems.add(itemId);
    }
    setCheckedItems(newCheckedItems);
    await saveCheckedItems(newCheckedItems);
  };

  const getUncheckedItems = () => {
    // Return ingredients in the order they appear in the shopping list UI:
    // 1. Side dish ingredients first
    // 2. AI-generated recipe ingredients second
    // 3. Main meal ingredients last
    const uncheckedItems: ShoppingListItem[] = [];

    // Add side dish ingredients first (as they appear first in UI)
    sideDishSections.forEach((section) => {
      section.ingredients.forEach((ingredient: ShoppingListItem) => {
        if (!checkedItems.has(ingredient.id)) {
          uncheckedItems.push(ingredient);
        }
      });
    });

    // Add AI-generated recipe ingredients second (as they appear second in UI)
    aiGeneratedSections.forEach((section) => {
      section.ingredients.forEach((ingredient: ShoppingListItem) => {
        if (!checkedItems.has(ingredient.id)) {
          uncheckedItems.push(ingredient);
        }
      });
    });

    // Add main meal ingredients last (as they appear last in UI)
    const mainIngredients = shoppingList.filter(
      (item) => !checkedItems.has(item.id)
    );
    uncheckedItems.push(...mainIngredients);

    return uncheckedItems;
  };

  const startWalmartShopping = () => {
    const uncheckedItems = getUncheckedItems();
    if (uncheckedItems.length === 0) {
      setAlertConfig({
        title: 'No Items',
        message: 'All items are checked off your list!',
      });
      setShowAlert(true);
      return;
    }

    // Find the first item that hasn't been added yet
    const firstUnaddedIndex = uncheckedItems.findIndex(
      (item) => !walmartAddedItems.has(item.id)
    );
    setCurrentSearchIndex(firstUnaddedIndex >= 0 ? firstUnaddedIndex : 0);

    setShowWalmart(true);
  };

  const getWalmartSearchUrl = () => {
    const uncheckedItems = getUncheckedItems();
    if (currentSearchIndex >= uncheckedItems.length) {
      return 'https://www.walmart.com/cart';
    }

    const item = uncheckedItems[currentSearchIndex];
    const searchTerm = encodeURIComponent(item.name);
    return `https://www.walmart.com/search?q=${searchTerm}`;
  };

  const startInstacartShopping = async () => {
    try {
      setWebViewLoading(true);

      const uncheckedItems = getUncheckedItems();
      if (uncheckedItems.length === 0) {
        setAlertConfig({
          title: 'No Items',
          message: 'All items are checked off your list!',
        });
        setShowAlert(true);
        setWebViewLoading(false);
        return;
      }

      // Convert shopping list items to separate Instacart entries
      // For items with multiple measurements (like "4 oz, 3 each"), create separate entries
      const instacartIngredients: any[] = [];
      
      uncheckedItems.forEach((item) => {
        const name = item.name;
        const emoji = item.emoji || '';
        
        if (item.totalAmount && item.totalAmount.includes(',')) {
          // Multiple measurements - split them into separate entries
          const measurements = item.totalAmount.split(',').map(m => m.trim());
          
          measurements.forEach(measurement => {
            const parsed = parseMeasurement(measurement);
            instacartIngredients.push({
              name: name,
              amount: parsed.amount,
              unit: parsed.unit,
              displayText: `${measurement} ${name}`,
              emoji: emoji,
              sources: [],
            });
          });
        } else {
          // Single measurement
          const parsed = parseMeasurement(item.totalAmount || '');
          instacartIngredients.push({
            name: name,
            amount: parsed.amount,
            unit: parsed.unit,
            displayText: item.totalAmount ? `${item.totalAmount} ${name}` : name,
            emoji: emoji,
            sources: [],
          });
        }
      });

      // Helper function to parse a single measurement
      function parseMeasurement(measurementStr: string): { amount: number; unit: string } {
        if (!measurementStr || measurementStr.trim() === '') {
          return { amount: 1, unit: '' };
        }

        const parts = measurementStr.trim().split(/\s+/);
        const firstPart = parts[0];

        let amount = 1;
        let unit = '';

        // Try to parse the first part as a number
        if (firstPart.includes('/')) {
          // Handle fractions like "1/2" or mixed numbers like "1 1/2"
          const fractionParts = measurementStr.match(/^(\d+\s+)?(\d+)\/(\d+)(.*)$/);
          if (fractionParts) {
            const whole = fractionParts[1] ? parseInt(fractionParts[1]) : 0;
            const numerator = parseInt(fractionParts[2]);
            const denominator = parseInt(fractionParts[3]);
            amount = whole + numerator / denominator;
            unit = fractionParts[4].trim();
          }
        } else {
          const numericAmount = parseFloat(firstPart);
          if (!isNaN(numericAmount)) {
            amount = numericAmount;
            unit = parts.slice(1).join(' ');
          } else {
            // First part is not a number, treat as unit with amount 1
            unit = measurementStr.trim();
            amount = 1;
          }
        }

        return { amount: Math.max(amount, 1), unit: unit.trim() };
      }

      // Create Instacart shopping list with affiliate tracking
      const instacartUrl = await createInstacartShoppingList(
        instacartIngredients,
        'My Weekly Meal Plan - MenuMaker',
        getPartnerLinkbackUrl(),
        affiliateData || undefined
      );

      // Add affiliate tracking to the URL if available
      const trackingUrl = affiliateData?.affiliateCode
        ? generateTrackingUrl(instacartUrl, affiliateData.affiliateCode)
        : instacartUrl;

      // Instacart URL generated successfully
      setInstacartUrl(trackingUrl);
      setShowInstacart(true);
    } catch {
      setAlertConfig({
        title: 'Error',
        message:
          'Failed to create shopping list on Instacart. Please try again.',
      });
      setShowAlert(true);
    } finally {
      setWebViewLoading(false);
    }
  };

  const goToNextItem = () => {
    const uncheckedItems = getUncheckedItems();

    // Mark current item as added to cart
    if (currentSearchIndex < uncheckedItems.length) {
      const currentItem = uncheckedItems[currentSearchIndex];
      const newAddedItems = new Set([...walmartAddedItems, currentItem.id]);
      setWalmartAddedItems(newAddedItems);
      saveWalmartAddedItems(newAddedItems);
    }

    if (currentSearchIndex < uncheckedItems.length - 1) {
      setWebViewLoading(true);
      setCurrentSearchIndex(currentSearchIndex + 1);
    }
  };

  const goToPreviousItem = () => {
    if (currentSearchIndex > 0) {
      // Unmark the current item as added when going back
      const uncheckedItems = getUncheckedItems();
      const currentItem = uncheckedItems[currentSearchIndex];
      if (currentItem) {
        const newAddedItems = new Set(walmartAddedItems);
        newAddedItems.delete(currentItem.id);
        setWalmartAddedItems(newAddedItems);
        saveWalmartAddedItems(newAddedItems);
      }

      setWebViewLoading(true);
      setCurrentSearchIndex(currentSearchIndex - 1);
    }
  };

  const goToCart = () => {
    setWebViewLoading(true);
    setCurrentSearchIndex(getUncheckedItems().length); // This will trigger cart URL
  };

  if (loading) {
    return (
      <ThemedView style={styles.container}>
        <LoadingIndicator
          fullScreen
          text="Building your shopping list..."
        />
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <Animated.View
        style={{
          opacity: titleSection.opacity,
          transform: [{ translateY: titleSection.translateY }]
        }}
      >
        <ThemedText style={styles.title}>Shopping List</ThemedText>
      </Animated.View>

      <Animated.View
        style={{
          flex: 1,
          opacity: contentSection.opacity,
          transform: [{ translateY: contentSection.translateY }]
        }}
      >
        {shoppingList.length === 0 ? (
          <View style={styles.emptyState}>
            <Ionicons
              name="basket-outline"
              size={64}
              color={colors.text}
              style={styles.emptyIcon}
            />
            <ThemedText style={styles.emptyText}>No meal plan found</ThemedText>
            <ThemedText style={styles.emptySubtext}>
              Create a weekly meal plan to see your shopping list here
            </ThemedText>
          </View>
        ) : (
          <>
            <ScrollView
            ref={scrollViewRef}
            style={styles.scrollView}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={[
              styles.scrollContent,
              { paddingBottom: contentBottomPadding },
            ]}
          >
            {/* Side Dish Sections */}
            {sideDishSections.map((section) => (
              <View key={section.id} style={styles.sideDishSection}>
                <View style={styles.sideDishHeader}>
                  <View style={styles.sideDishHeaderLeft}>
                    <Ionicons
                      name="restaurant"
                      size={20}
                      color={colors.primary}
                    />
                    <ThemedText style={styles.sideDishTitle}>
                      {section.name}
                    </ThemedText>
                  </View>
                  <TouchableOpacity
                    style={[
                      styles.sideDishDelete,
                      { backgroundColor: '#ff4444' },
                    ]}
                    onPress={() => deleteSideDish(section.id, section.name)}
                  >
                    <Ionicons
                      name="trash"
                      size={16}
                      color={colors.background}
                    />
                  </TouchableOpacity>
                </View>

                <View style={styles.sideDishIngredients}>
                  {section.ingredients.map((ingredient: ShoppingListItem) => {
                    const animation = getIngredientAnimation(ingredient.id);
                    return (
                      <Animated.View
                        key={ingredient.id}
                        style={{
                          opacity: animation.opacity,
                          transform: [{ translateY: animation.translateY }],
                        }}
                      >
                        <TouchableOpacity
                          style={[
                            styles.ingredientItem,
                            { backgroundColor: colors.cardBackground },
                            checkedItems.has(ingredient.id) && styles.checkedItem,
                          ]}
                          onPress={() => toggleItem(ingredient.id)}
                        >
                      <View style={styles.ingredientContent}>
                        <ThemedText style={styles.ingredientEmoji}>
                          {ingredient.emoji ||
                            getIngredientEmoji(ingredient.name)}
                        </ThemedText>

                        <ThemedText
                          style={[
                            styles.ingredientName,
                            checkedItems.has(ingredient.id) &&
                              styles.checkedText,
                          ]}
                        >
                          {ingredient.name}
                        </ThemedText>

                        {ingredient.totalAmount && (
                          <ThemedText style={styles.ingredientAmount}>
                            {ingredient.totalAmount}
                          </ThemedText>
                        )}

                        <View
                          style={[
                            styles.checkbox,
                            checkedItems.has(ingredient.id) &&
                              styles.checkedCheckbox,
                          ]}
                        >
                          {checkedItems.has(ingredient.id) && (
                            <Ionicons
                              name="checkmark"
                              size={14}
                              color={colors.background}
                            />
                          )}
                        </View>
                      </View>
                    </TouchableOpacity>
                  </Animated.View>
                    );
                  })}
                </View>
              </View>
            ))}

            {/* AI-Generated Recipe Sections */}
            {aiGeneratedSections.map((section) => (
              <View key={section.id} style={styles.aiGeneratedSection}>
                <View style={styles.aiGeneratedHeader}>
                  <View style={styles.aiGeneratedHeaderLeft}>
                    <Ionicons
                      name="sparkles"
                      size={20}
                      color={colors.primary}
                    />
                    <ThemedText style={styles.aiGeneratedTitle}>
                      AI Recipe: {section.name}
                    </ThemedText>
                  </View>
                  <TouchableOpacity
                    style={[
                      styles.sideDishDelete,
                      { backgroundColor: '#ff4444' },
                    ]}
                    onPress={() =>
                      deleteAiGeneratedRecipe(section.id, section.name)
                    }
                  >
                    <Ionicons
                      name="trash"
                      size={16}
                      color={colors.background}
                    />
                  </TouchableOpacity>
                </View>
                <View style={styles.aiGeneratedIngredients}>
                  {section.ingredients.map((item: ShoppingListItem) => {
                    const animation = getIngredientAnimation(item.id);
                    return (
                      <Animated.View
                        key={item.id}
                        style={{
                          opacity: animation.opacity,
                          transform: [{ translateY: animation.translateY }],
                        }}
                      >
                        <TouchableOpacity
                          style={[
                            styles.ingredientItem,
                            { backgroundColor: colors.cardBackground },
                            checkedItems.has(item.id) && styles.checkedItem,
                          ]}
                          onPress={() => toggleItem(item.id)}
                        >
                      <View style={styles.ingredientContent}>
                        <ThemedText style={styles.ingredientEmoji}>
                          {item.emoji || getIngredientEmoji(item.name)}
                        </ThemedText>

                        <ThemedText
                          style={[
                            styles.ingredientName,
                            checkedItems.has(item.id) && styles.checkedText,
                          ]}
                        >
                          {item.name}
                        </ThemedText>

                        {item.totalAmount && (
                          <ThemedText style={styles.ingredientAmount}>
                            {item.totalAmount}
                          </ThemedText>
                        )}

                        <View
                          style={[
                            styles.checkbox,
                            checkedItems.has(item.id) && styles.checkedCheckbox,
                          ]}
                        >
                          {checkedItems.has(item.id) && (
                            <Ionicons
                              name="checkmark"
                              size={14}
                              color={colors.background}
                            />
                          )}
                        </View>
                      </View>
                    </TouchableOpacity>
                  </Animated.View>
                    );
                  })}
                </View>
              </View>
            ))}

            {/* Main Meal Ingredients */}
            {shoppingList.length > 0 && (
              <View style={styles.mainIngredientsHeader}>
                <ThemedText style={styles.mainIngredientsTitle}>
                  Main Meal Ingredients
                </ThemedText>
              </View>
            )}

            {shoppingList.map((item) => {
              const animation = getIngredientAnimation(item.id);
              return (
                <Animated.View
                  key={item.id}
                  style={{
                    opacity: animation.opacity,
                    transform: [{ translateY: animation.translateY }],
                  }}
                >
                  <TouchableOpacity
                    style={[
                      styles.ingredientItem,
                      { backgroundColor: colors.cardBackground },
                      checkedItems.has(item.id) && styles.checkedItem,
                    ]}
                    onPress={() => toggleItem(item.id)}
                  >
                <View style={styles.ingredientContent}>
                  <ThemedText style={styles.ingredientEmoji}>
                    {item.emoji || getIngredientEmoji(item.name)}
                  </ThemedText>

                  <ThemedText
                    style={[
                      styles.ingredientName,
                      checkedItems.has(item.id) && styles.checkedText,
                    ]}
                  >
                    {item.name}
                  </ThemedText>

                  {item.totalAmount && (
                    <ThemedText style={styles.ingredientAmount}>
                      {item.totalAmount}
                    </ThemedText>
                  )}

                  <View
                    style={[
                      styles.checkbox,
                      checkedItems.has(item.id) && styles.checkedCheckbox,
                    ]}
                  >
                    {checkedItems.has(item.id) && (
                      <Ionicons
                        name="checkmark"
                        size={14}
                        color={colors.background}
                      />
                    )}
                  </View>
                </View>
              </TouchableOpacity>
            </Animated.View>
              );
            })}
          </ScrollView>

          <Animated.View 
            style={[
              styles.bottomSection,
              {
                opacity: buttonSection.opacity,
                transform: [{ translateY: buttonSection.translateY }]
              }
            ]}
          >
            <View style={styles.summaryRow}>
              <ThemedText style={styles.summaryText}>
                {getUncheckedItems().length} items remaining
              </ThemedText>
              <ThemedText style={styles.summaryText}>
                {checkedItems.size} checked off
              </ThemedText>
            </View>

            <TouchableOpacity
              style={[
                styles.shopOnlineButton,
                { 
                  borderColor: colors.primary,
                  opacity: getUncheckedItems().length === 0 ? 0.5 : 1 
                }
              ]}
              onPress={() => setShowShopOnlineModal(true)}
              disabled={getUncheckedItems().length === 0}
            >
              <Ionicons 
                name="storefront" 
                size={20} 
                color={colors.primary} 
                style={{ marginRight: 8 }}
              />
              <ThemedText style={styles.shopOnlineButtonText}>
                Shop Online
              </ThemedText>
            </TouchableOpacity>
          </Animated.View>
        </>
        )}
      </Animated.View>

      {/* Walmart WebView Modal */}
      <Modal
        visible={showWalmart}
        animationType="slide"
        presentationStyle="fullScreen"
      >
        <SafeAreaView style={styles.webViewContainer}>
          {/* Compact Header */}
          <View style={styles.webViewHeader}>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setShowWalmart(false)}
            >
              <Ionicons name="close" size={24} color={colors.text} />
            </TouchableOpacity>

            <View style={styles.walmartBranding}>
              <Image
                source={require('@/assets/images/walmart-full.png')}
                style={styles.walmartHeaderLogo}
                contentFit="contain"
              />
            </View>

            <View style={styles.headerSpacer} />
          </View>

          {/* Progress Bar */}
          <View style={styles.progressBar}>
            <View
              style={[
                styles.progressFill,
                {
                  width: `${
                    ((currentSearchIndex + 1) /
                      Math.max(getUncheckedItems().length, 1)) *
                    100
                  }%`,
                },
              ]}
            />
          </View>

          {/* WebView Content */}
          <View style={{ flex: 1 }}>
            <WebView
              source={{ uri: getWalmartSearchUrl() }}
              style={styles.webView}
              startInLoadingState={true}
              onLoadEnd={() => setWebViewLoading(false)}
              onLoadStart={() => setWebViewLoading(true)}
              onNavigationStateChange={(navState) => {
                // Reset loading state when navigation completes
                if (!navState.loading) {
                  setWebViewLoading(false);
                }
              }}
              javaScriptEnabled={true}
              domStorageEnabled={true}
              renderLoading={() => (
                <LoadingIndicator text="Loading Walmart..." />
              )}
            />

            {/* Loading Overlay */}
            {webViewLoading && (
              <LoadingIndicator
                fullScreen
                overlay
                text={`Loading ${getUncheckedItems()[currentSearchIndex]?.name || 'cart'}...`}
              />
            )}
          </View>

          {/* Bottom Navigation Bar */}
          <View style={styles.bottomNavigation}>
            {/* Current Item Info */}
            <View style={styles.bottomItemInfo}>
              {currentSearchIndex >= getUncheckedItems().length ? (
                <View style={styles.completedInline}>
                  <Ionicons name="card" size={20} color={colors.primary} />
                  <ThemedText style={styles.completedTextInline}>
                    Complete checkout in Walmart
                  </ThemedText>
                </View>
              ) : (
                <View style={styles.currentItemInline}>
                  <ThemedText style={styles.currentItemEmoji}>
                    {getUncheckedItems()[currentSearchIndex]?.emoji ||
                      getIngredientEmoji(
                        getUncheckedItems()[currentSearchIndex]?.name || ''
                      )}
                  </ThemedText>
                  <ThemedText
                    style={styles.currentItemNameInline}
                    numberOfLines={1}
                  >
                    {getUncheckedItems()[currentSearchIndex]?.name}
                  </ThemedText>
                  {getUncheckedItems()[currentSearchIndex]?.totalAmount && (
                    <ThemedText style={styles.currentItemAmountInline}>
                      {getUncheckedItems()[currentSearchIndex]?.totalAmount}
                    </ThemedText>
                  )}
                </View>
              )}
            </View>

            {/* Navigation Controls */}
            <View style={styles.bottomControls}>
              <TouchableOpacity
                style={[
                  styles.navButton,
                  styles.prevButton,
                  {
                    opacity:
                      currentSearchIndex > 0 && !webViewLoading ? 1 : 0.3,
                  },
                ]}
                onPress={goToPreviousItem}
                disabled={currentSearchIndex === 0 || webViewLoading}
              >
                <Ionicons name="chevron-back" size={20} color={colors.text} />
                <ThemedText style={styles.navButtonText}>Previous</ThemedText>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.progressIndicator}
                onPress={() => {
                  // Close WebView and show progress modal
                  setShowWalmart(false);
                  setShowWalmartListView(true);
                }}
                activeOpacity={0.7}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                <Ionicons
                  name="list"
                  size={16}
                  color={colors.text}
                  style={{ marginRight: 6 }}
                />
                <ThemedText style={styles.progressText}>
                  {currentSearchIndex >= getUncheckedItems().length
                    ? 'Cart'
                    : `${currentSearchIndex + 1} of ${
                        getUncheckedItems().length
                      }`}
                </ThemedText>
              </TouchableOpacity>

              {currentSearchIndex < getUncheckedItems().length - 1 ? (
                <TouchableOpacity
                  style={[
                    styles.navButton,
                    styles.nextButton,
                    { opacity: !webViewLoading ? 1 : 0.3 },
                  ]}
                  onPress={goToNextItem}
                  disabled={webViewLoading}
                >
                  <ThemedText style={styles.navButtonText}>Next</ThemedText>
                  <Ionicons
                    name="chevron-forward"
                    size={20}
                    color={colors.text}
                  />
                </TouchableOpacity>
              ) : (
                <TouchableOpacity
                  style={[
                    styles.navButton,
                    styles.cartButton,
                    { opacity: !webViewLoading ? 1 : 0.3 },
                  ]}
                  onPress={
                    currentSearchIndex >= getUncheckedItems().length
                      ? () => setShowWalmart(false)
                      : goToCart
                  }
                  disabled={webViewLoading}
                >
                  <Ionicons
                    name={
                      currentSearchIndex >= getUncheckedItems().length
                        ? 'checkmark'
                        : 'cart'
                    }
                    size={18}
                    color={colors.background}
                  />
                  <ThemedText style={styles.cartButtonText}>
                    {currentSearchIndex >= getUncheckedItems().length
                      ? 'Done'
                      : 'Cart'}
                  </ThemedText>
                </TouchableOpacity>
              )}
            </View>
          </View>
        </SafeAreaView>
      </Modal>

      {/* Instacart WebView Modal */}
      <Modal
        visible={showInstacart}
        animationType="slide"
        presentationStyle="fullScreen"
      >
        <SafeAreaView style={styles.webViewContainer}>
          {/* Header */}
          <View style={styles.webViewHeader}>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setShowInstacart(false)}
            >
              <Ionicons name="close" size={24} color={colors.text} />
            </TouchableOpacity>

            <View style={styles.instacartBranding}>
              <Image
                source={
                  activeTheme === 'light'
                    ? require('@/assets/images/Instacart_Logo_Kale.svg')
                    : require('@/assets/images/instacart-icon.svg')
                }
                style={styles.instacartHeaderLogo}
                contentFit="contain"
              />
            </View>

            <View style={styles.headerSpacer} />
          </View>

          {/* WebView */}
          {instacartUrl ? (
            <WebView
              source={{ uri: instacartUrl }}
              style={styles.webView}
              onLoadStart={() => setWebViewLoading(true)}
              onLoadEnd={() => setWebViewLoading(false)}
              onError={() => {
                setWebViewLoading(false);
              }}
              onNavigationStateChange={(navState) => {
                // Reset loading state when navigation completes
                if (!navState.loading) {
                  setWebViewLoading(false);
                }
              }}
              javaScriptEnabled={true}
              domStorageEnabled={true}
              startInLoadingState={false}
            />
          ) : (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={colors.primary} />
              <ThemedText style={styles.loadingText}>
                Creating Instacart Shopping List...
              </ThemedText>
            </View>
          )}

          {webViewLoading && (
            <View style={styles.webViewLoadingOverlay}>
              <ActivityIndicator size="large" color={colors.primary} />
              <ThemedText style={styles.loadingText}>
                Loading Instacart...
              </ThemedText>
            </View>
          )}
        </SafeAreaView>
      </Modal>

      {/* Shop Online Choice Modal */}
      <Modal
        visible={showShopOnlineModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowShopOnlineModal(false)}
      >
        <SafeAreaView style={styles.shopOnlineModalContainer}>
          <View style={styles.shopOnlineModalHeader}>
            <ThemedText style={styles.shopOnlineModalTitle}>
              Choose Your Store
            </ThemedText>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setShowShopOnlineModal(false)}
            >
              <Ionicons name="close" size={24} color={colors.text} />
            </TouchableOpacity>
          </View>

          <View style={styles.shopOnlineModalContent}>
            <ThemedText style={styles.shopOnlineModalSubtitle}>
              Select where you&apos;d like to shop for your {getUncheckedItems().length} ingredients
            </ThemedText>

            <View style={styles.storeOptions}>
              <TouchableOpacity
                style={[styles.storeOption, { borderColor: colors.border }]}
                onPress={() => {
                  setShowShopOnlineModal(false);
                  startWalmartShopping();
                }}
              >
                <Image
                  source={require('@/assets/images/walmart-full.png')}
                  style={styles.storeOptionLogo}
                  contentFit="contain"
                />
                <ThemedText style={styles.storeOptionDescription}>
                  Browse and add items step by step
                </ThemedText>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.storeOption, { borderColor: colors.border }]}
                onPress={() => {
                  setShowShopOnlineModal(false);
                  startInstacartShopping();
                }}
              >
                <Image
                  source={
                    activeTheme === 'light'
                      ? require('@/assets/images/Instacart_Logo_Kale.svg')
                      : require('@/assets/images/instacart-icon.svg')
                  }
                  style={styles.storeOptionLogo}
                  contentFit="contain"
                />
                <ThemedText style={styles.storeOptionDescription}>
                  Create shopping list automatically
                </ThemedText>
              </TouchableOpacity>
            </View>
          </View>
        </SafeAreaView>
      </Modal>

      <CustomAlert
        visible={showAlert}
        title={alertConfig.title}
        message={alertConfig.message}
        onConfirm={() => setShowAlert(false)}
        onCancel={() => setShowAlert(false)}
      />

      {/* Walmart Shopping List View Modal */}
      <Modal
        visible={showWalmartListView}
        animationType="slide"
        presentationStyle="fullScreen"
        onRequestClose={() => {
          setShowWalmartListView(false);
          setShowWalmart(true);
        }}
      >
        <SafeAreaView style={styles.listViewContainer}>
          <View style={styles.listViewHeader}>
            <ThemedText style={styles.listViewTitle}>
              Shopping Progress
            </ThemedText>
            <View style={{ flexDirection: 'row', gap: 8 }}>
              <TouchableOpacity
                style={[styles.closeButton, { backgroundColor: '#ff4444' }]}
                onPress={() => {
                  const emptySet = new Set<string>();
                  setWalmartAddedItems(emptySet);
                  saveWalmartAddedItems(emptySet);
                  setCurrentSearchIndex(0);
                  setShowWalmartListView(false);
                  setShowWalmart(true);
                }}
              >
                <Ionicons name="refresh" size={20} color={colors.background} />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => {
                  setShowWalmartListView(false);
                  setShowWalmart(true);
                }}
              >
                <Ionicons name="close" size={24} color={colors.text} />
              </TouchableOpacity>
            </View>
          </View>

          <ScrollView style={styles.listViewContent}>
            {/* Added Items Section */}
            {walmartAddedItems.size > 0 && (
              <View style={styles.listSection}>
                <View style={styles.sectionHeader}>
                  <Ionicons name="checkmark-circle" size={20} color={colors.primary} />
                  <ThemedText
                    style={[styles.sectionTitle, { color: colors.primary }]}
                  >
                    Added to Cart ({walmartAddedItems.size})
                  </ThemedText>
                </View>
                {getUncheckedItems()
                  .filter((item) => walmartAddedItems.has(item.id))
                  .map((item) => (
                    <View
                      key={item.id}
                      style={[styles.listItem, styles.addedItem]}
                    >
                      <ThemedText style={styles.itemEmoji}>
                        {item.emoji || getIngredientEmoji(item.name)}
                      </ThemedText>
                      <View style={styles.itemDetails}>
                        <ThemedText
                          style={[styles.itemName, styles.addedItemText]}
                        >
                          {item.name}
                        </ThemedText>
                        {item.totalAmount && (
                          <ThemedText
                            style={[styles.itemAmount, styles.addedItemText]}
                          >
                            {item.totalAmount}
                          </ThemedText>
                        )}
                      </View>
                      <Ionicons
                        name="checkmark-circle"
                        size={20}
                        color={colors.primary}
                      />
                    </View>
                  ))}
              </View>
            )}

            {/* Remaining Items Section */}
            <View style={styles.listSection}>
              <View style={styles.sectionHeader}>
                <Ionicons name="list" size={20} color={colors.text} />
                <ThemedText style={styles.sectionTitle}>
                  Remaining Items (
                  {getUncheckedItems().length - walmartAddedItems.size})
                </ThemedText>
              </View>
              {getUncheckedItems()
                .filter((item) => !walmartAddedItems.has(item.id))
                .map((item, index) => {
                  // The current item is the one at currentSearchIndex in the full unchecked list
                  const currentItem = getUncheckedItems()[currentSearchIndex];
                  const isCurrentItem =
                    currentItem && currentItem.id === item.id;

                  return (
                    <View
                      key={item.id}
                      style={[
                        styles.listItem,
                        isCurrentItem && styles.currentItem,
                      ]}
                    >
                      <ThemedText style={styles.itemEmoji}>
                        {item.emoji || getIngredientEmoji(item.name)}
                      </ThemedText>
                      <View style={styles.itemDetails}>
                        <ThemedText
                          style={[
                            styles.itemName,
                            isCurrentItem && styles.currentItemText,
                          ]}
                        >
                          {item.name}
                        </ThemedText>
                        {item.totalAmount && (
                          <ThemedText
                            style={[
                              styles.itemAmount,
                              isCurrentItem && styles.currentItemText,
                            ]}
                          >
                            {item.totalAmount}
                          </ThemedText>
                        )}
                      </View>
                    </View>
                  );
                })}
            </View>
          </ScrollView>
        </SafeAreaView>
      </Modal>
    </ThemedView>
  );
}

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 24,
    },
    loadingText: {
      marginTop: 16,
      fontSize: 16,
      opacity: 0.7,
    },
    title: {
      fontSize: 28,
      fontWeight: 'bold',
      color: colors.text,
      paddingHorizontal: 24,
      paddingTop: 80,
      paddingBottom: 20,
    },
    subtitle: {
      fontSize: 14,
      color: colors.text,
      opacity: 0.7,
    },
    scrollView: {
      flex: 1,
    },
    scrollContent: {
      paddingHorizontal: 20,
      // paddingBottom will be set dynamically based on tab bar height
    },
    emptyState: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 40,
    },
    emptyIcon: {
      opacity: 0.5,
      marginBottom: 16,
    },
    emptyText: {
      fontSize: 20,
      fontWeight: '600',
      color: colors.text,
      marginBottom: 8,
    },
    emptySubtext: {
      fontSize: 16,
      color: colors.text,
      opacity: 0.7,
      textAlign: 'center',
    },
    categorySection: {
      marginBottom: 20,
    },
    categoryHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 10,
      paddingBottom: 6,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    categoryTitle: {
      fontSize: 16,
      fontWeight: '600',
      color: colors.text,
      marginLeft: 8,
      flex: 1,
    },
    categoryCount: {
      fontSize: 12,
      backgroundColor: colors.primary,
      paddingHorizontal: 8,
      paddingVertical: 2,
      borderRadius: 10,
      color: colors.background,
      fontWeight: '500',
    },
    ingredientItem: {
      borderRadius: 6,
      marginBottom: 8,
      borderWidth: 1,
      borderColor: colors.border,
    },
    checkedItem: {
      opacity: 0.6,
      backgroundColor: `${colors.cardBackground}99`,
    },
    ingredientContent: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 12,
      paddingHorizontal: 8,
    },
    checkbox: {
      width: 18,
      height: 18,
      borderRadius: 9,
      borderWidth: 2,
      borderColor: colors.border,
      alignItems: 'center',
      justifyContent: 'center',
    },
    checkedCheckbox: {
      backgroundColor: colors.primary,
      borderColor: colors.primary,
    },
    ingredientEmoji: {
      fontSize: 16,
      marginRight: 8,
    },
    ingredientName: {
      fontSize: 15,
      fontWeight: '500',
      color: colors.text,
      flex: 1,
      marginRight: 8,
    },
    checkedText: {
      textDecorationLine: 'line-through',
      opacity: 0.7,
    },
    ingredientAmount: {
      fontSize: 15,
      color: colors.primary,
      opacity: 0.8,
      fontWeight: '500',
      marginRight: 8,
      textAlign: 'right',
    },
    bottomSection: {
      paddingHorizontal: 20,
      paddingBottom: Platform.select({
        ios: 110, // For iOS home indicator
        android: 20, // Less padding for Android
      }),
      paddingTop: 12,
      borderTopWidth: 1,
      borderTopColor: colors.border,
      backgroundColor: colors.background,
    },
    summaryRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: 12,
    },
    summaryText: {
      fontSize: 14,
      color: colors.text,
      opacity: 0.7,
    },
    walmartButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 16,
      borderRadius: 8,
      gap: 8,
    },
    walmartButtonOutline: {
      backgroundColor: 'transparent',
      borderWidth: 2,
      borderColor: colors.primary,
    },
    walmartButtonText: {
      fontSize: 16,
      fontWeight: '600',
      color: colors.background,
    },
    walmartButtonTextOutline: {
      fontSize: 16,
      fontWeight: '600',
      color: colors.primary,
    },
    walmartButtonIcon: {
      width: 20,
      height: 20,
      marginRight: 8,
    },
    walmartButtonLogo: {
      width: 120,
      height: 32,
    },
    buttonsContainer: {
      flexDirection: 'row',
      gap: 12,
    },
    shoppingButton: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 16,
      borderRadius: 8,
      gap: 8,
    },
    instacartButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 16,
      borderRadius: 8,
      gap: 8,
      marginTop: 12,
    },
    instacartButtonOutline: {
      backgroundColor: 'transparent',
      borderWidth: 2,
      borderColor: colors.primary,
    },
    instacartButtonText: {
      fontSize: 16,
      fontWeight: '600',
      color: colors.background,
    },
    instacartButtonTextOutline: {
      fontSize: 16,
      fontWeight: '600',
      color: colors.primary,
    },
    instacartButtonIcon: {
      width: 20,
      height: 20,
      marginRight: 8,
    },
    instacartButtonLogo: {
      width: 120,
      height: 32,
    },
    instacartBranding: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 8,
    },
    instacartLogo: {
      width: 32,
      height: 32,
      borderRadius: 16,
      backgroundColor: colors.primary,
      alignItems: 'center',
      justifyContent: 'center',
    },
    instacartText: {
      fontSize: 18,
      fontWeight: '600',
      color: colors.primary,
    },
    webViewContainer: {
      flex: 1,
      backgroundColor: colors.background,
    },
    webViewHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 16,
      paddingVertical: 12,
      backgroundColor: colors.background,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    closeButton: {
      padding: 8,
      borderRadius: 8,
      backgroundColor: colors.cardBackground,
    },
    walmartBranding: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 8,
    },
    walmartLogo: {
      width: 32,
      height: 32,
      borderRadius: 16,
      backgroundColor: '#0071ce',
      alignItems: 'center',
      justifyContent: 'center',
    },
    walmartLogoText: {
      color: colors.background,
      fontSize: 18,
      fontWeight: 'bold',
    },
    walmartHeaderLogo: {
      width: 120,
      height: 32,
    },
    instacartHeaderLogo: {
      width: 120,
      height: 32,
    },
    walmartText: {
      fontSize: 18,
      fontWeight: '600',
      color: colors.text,
    },
    headerSpacer: {
      width: 48, // Same width as close button for centering
    },
    progressBar: {
      height: 3,
      backgroundColor: colors.border,
    },
    progressFill: {
      height: '100%',
      backgroundColor: colors.primary,
    },
    bottomNavigation: {
      backgroundColor: colors.background,
      borderTopWidth: 1,
      borderTopColor: colors.border,
      paddingBottom: 8, // Minimal padding since we're in a modal
    },
    bottomItemInfo: {
      paddingHorizontal: 16,
      paddingTop: 10,
      paddingBottom: 6,
    },
    completedInline: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 8,
      justifyContent: 'center',
    },
    completedTextInline: {
      fontSize: 16,
      fontWeight: '600',
      color: colors.text,
    },
    currentItemInline: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 8,
      justifyContent: 'center',
    },
    currentItemEmoji: {
      fontSize: 20,
    },
    currentItemNameInline: {
      fontSize: 16,
      fontWeight: '600',
      color: colors.text,
      maxWidth: '50%',
    },
    currentItemAmountInline: {
      fontSize: 14,
      color: colors.primary,
      fontWeight: '500',
    },
    bottomControls: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 16,
      paddingBottom: 8,
      gap: 12,
    },
    navButton: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 14,
      paddingVertical: 8,
      borderRadius: 8,
      gap: 6,
      minWidth: 85,
    },
    prevButton: {
      backgroundColor: colors.cardBackground,
      justifyContent: 'flex-start',
    },
    nextButton: {
      backgroundColor: colors.cardBackground,
      justifyContent: 'flex-end',
    },
    navButtonText: {
      fontSize: 14,
      color: colors.text,
      fontWeight: '500',
    },
    progressIndicator: {
      backgroundColor: colors.cardBackground,
      paddingHorizontal: 12,
      paddingVertical: 8,
      borderRadius: 8,
      flexDirection: 'row',
      alignItems: 'center',
      borderWidth: 1,
      borderColor: colors.border,
      zIndex: 1000,
      elevation: 5,
      minHeight: 36,
      minWidth: 80,
    },
    progressText: {
      fontSize: 12,
      color: colors.text,
      fontWeight: '600',
      opacity: 0.8,
    },
    cartButton: {
      backgroundColor: colors.primary,
      borderRadius: 8,
      paddingHorizontal: 16,
      paddingVertical: 10,
      flexDirection: 'row',
      alignItems: 'center',
      gap: 6,
      minWidth: 90,
      justifyContent: 'center',
    },
    cartButtonText: {
      color: colors.background,
      fontWeight: '600',
      fontSize: 14,
    },
    webView: {
      flex: 1,
    },
    webViewLoading: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: colors.background,
    },
    webViewLoadingText: {
      marginTop: 16,
      fontSize: 16,
      color: colors.text,
    },
    webViewLoadingOverlay: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      zIndex: 1000,
    },
    loadingOverlay: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 1000,
    },
    loadingContent: {
      backgroundColor: colors.cardBackground,
      padding: 24,
      borderRadius: 12,
      alignItems: 'center',
      gap: 12,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.3,
      shadowRadius: 8,
      elevation: 8,
    },
    sideDishSection: {
      marginBottom: 20,
      backgroundColor: 'rgba(255, 229, 152, 0.05)',
      borderRadius: 12,
      borderWidth: 1,
      borderColor: 'rgba(255, 229, 152, 0.2)',
      overflow: 'hidden',
    },
    sideDishHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      padding: 12,
      backgroundColor: 'rgba(255, 229, 152, 0.1)',
    },
    sideDishHeaderLeft: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
      gap: 8,
    },
    sideDishTitle: {
      fontSize: 16,
      fontWeight: '600',
      color: colors.text,
      flex: 1,
    },
    sideDishDelete: {
      width: 32,
      height: 32,
      borderRadius: 16,
      alignItems: 'center',
      justifyContent: 'center',
    },
    sideDishIngredients: {
      paddingHorizontal: 4,
      paddingBottom: 8,
    },
    mainIngredientsHeader: {
      marginBottom: 16,
      paddingBottom: 8,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    mainIngredientsTitle: {
      fontSize: 12,
      fontWeight: '600',
      color: colors.text,
      textAlign: 'left',
    },
    // AI-Generated Recipe Styles
    aiGeneratedSection: {
      backgroundColor: 'rgba(255, 229, 152, 0.05)',
      borderRadius: 12,
      marginBottom: 16,
      borderWidth: 1,
      borderColor: 'rgba(255, 229, 152, 0.2)',
    },
    aiGeneratedHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderBottomWidth: 1,
      borderBottomColor: 'rgba(255, 229, 152, 0.2)',
    },
    aiGeneratedHeaderLeft: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    aiGeneratedTitle: {
      fontSize: 16,
      fontWeight: '600',
      color: colors.primary,
      marginLeft: 8,
      flex: 1,
    },
    aiGeneratedIngredients: {
      paddingHorizontal: 8,
      paddingVertical: 4,
    },
    shoppingItem: {
      borderRadius: 6,
      marginBottom: 4,
      borderWidth: 1,
      borderColor: colors.border,
    },
    itemContent: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 8,
    },
    itemLeft: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    itemText: {
      flex: 1,
      marginLeft: 8,
    },
    itemName: {
      fontSize: 15,
      fontWeight: '500',
      color: colors.text,
      marginBottom: 1,
    },
    emoji: {
      fontSize: 16,
      marginRight: 8,
    },
    // Walmart List View Styles
    listViewContainer: {
      flex: 1,
      backgroundColor: colors.background,
    },
    listViewHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 20,
      paddingVertical: 16,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
      backgroundColor: colors.background,
    },
    listViewTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: colors.text,
    },
    listViewContent: {
      flex: 1,
      paddingHorizontal: 20,
    },
    listSection: {
      marginVertical: 16,
    },
    sectionHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 12,
      gap: 8,
    },
    sectionTitle: {
      fontSize: 16,
      fontWeight: '600',
      color: colors.text,
    },
    listItem: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 12,
      paddingHorizontal: 12,
      marginBottom: 8,
      backgroundColor: colors.cardBackground,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: colors.border,
    },
    addedItem: {
      backgroundColor: '#E8F5E8',
      borderColor: colors.primary,
    },
    currentItem: {
      backgroundColor: colors.primary + '20',
      borderColor: colors.primary,
    },
    itemEmoji: {
      fontSize: 18,
      marginRight: 12,
    },
    itemDetails: {
      flex: 1,
    },
    itemNameSecondary: {
      fontSize: 15,
      fontWeight: '500',
      color: colors.text,
      marginBottom: 2,
    },
    itemAmount: {
      fontSize: 13,
      color: colors.text,
      opacity: 0.7,
    },
    addedItemText: {
      color: colors.primary,
    },
    currentItemText: {
      color: colors.primary,
      fontWeight: '600',
    },
    // Shop Online Button Styles
    shopOnlineButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 16,
      borderRadius: 8,
      marginTop: 0,
      borderWidth: 1,
    },
    shopOnlineButtonText: {
      fontSize: 16,
      fontWeight: '600',
      color: colors.primary,
    },
    // Shop Online Modal Styles
    shopOnlineModalContainer: {
      flex: 1,
      backgroundColor: colors.background,
    },
    shopOnlineModalHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 20,
      paddingVertical: 16,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    shopOnlineModalTitle: {
      fontSize: 20,
      fontWeight: '600',
      color: colors.text,
    },
    shopOnlineModalContent: {
      flex: 1,
      paddingHorizontal: 20,
      paddingTop: 24,
    },
    shopOnlineModalSubtitle: {
      fontSize: 16,
      color: colors.text,
      opacity: 0.7,
      textAlign: 'center',
      marginBottom: 32,
    },
    storeOptions: {
      gap: 16,
    },
    storeOption: {
      backgroundColor: colors.cardBackground,
      borderRadius: 12,
      borderWidth: 1,
      padding: 20,
      alignItems: 'center',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
    storeOptionLogo: {
      width: 120,
      height: 40,
      marginBottom: 16,
    },
    storeOptionDescription: {
      fontSize: 14,
      color: colors.text,
      opacity: 0.6,
      textAlign: 'center',
    },
  });
