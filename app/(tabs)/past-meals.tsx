import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { useAuth } from '@/contexts/AuthContext';
import { useTheme } from '@/contexts/ThemeContext';
import { useTabBarHeight } from '@/hooks/useTabBarHeight';
import { Database } from '@/lib/database.types';
import { getMealsByIds } from '@/lib/mealFiltering';
import { getMealCategoryIcons } from '@/lib/mealIcons';
import { supabase } from '@/lib/supabase';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { router, useFocusEffect } from 'expo-router';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
  ActivityIndicator,
  Image,
  Pressable,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

type Meal = Database['public']['Tables']['meals']['Row'];

interface ArchivedMealPlan {
  id: string;
  week_start_date: string;
  meal_ids: string[];
  archived_at: string | null;
}

export default function PastMealsScreen() {
  const { activeTheme } = useTheme();
  const colors = Colors[activeTheme];
  const insets = useSafeAreaInsets();
  const { session } = useAuth();
  const { contentBottomPadding } = useTabBarHeight();
  const scrollViewRef = useRef<ScrollView>(null);
  const [meals, setMeals] = useState<Meal[]>([]);
  const [archivedPlan, setArchivedPlan] = useState<ArchivedMealPlan | null>(
    null
  );
  const [loading, setLoading] = useState(true);
  const [favoriteIds, setFavoriteIds] = useState<string[]>([]);

  useEffect(() => {
    if (session?.user?.id) {
      loadPastMeals();
      loadFavorites();
    }
  }, [session]);

  // Scroll to top when tab comes into focus
  useFocusEffect(
    useCallback(() => {
      scrollViewRef.current?.scrollTo({ y: 0, animated: false });
    }, [])
  );

  const loadPastMeals = async () => {
    if (!session?.user?.id) return;

    try {
      // Get the most recent archived meal plan
      const { data: archivedPlans, error } = await supabase
        .from('weekly_meal_plans')
        .select('id, week_start_date, meal_ids, archived_at')
        .eq('user_id', session.user.id)
        .eq('is_active', false)
        .not('archived_at', 'is', null)
        .order('archived_at', { ascending: false })
        .limit(1);

      if (error) throw error;

      if (archivedPlans && archivedPlans.length > 0) {
        const plan = archivedPlans[0];
        setArchivedPlan(plan);

        // Fetch meal details
        if (plan.meal_ids && plan.meal_ids.length > 0) {
          const mealData = await getMealsByIds(
            plan.meal_ids,
            session.user.id,
            'id, name, "prepTimeHour", "prepTimeMin", "cookTimeHour", "cookTimeMin", "servingSize", image, course, cuisine_type, prep_method'
          );

          // Order meals according to the original plan order
          const orderedMeals = plan.meal_ids
            .map((id) => mealData?.find((meal) => meal.id === id))
            .filter(Boolean) as Meal[];

          setMeals(orderedMeals);
        }
      }
    } catch (error) {
      console.error('Error loading past meals:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadFavorites = async () => {
    if (!session?.user?.id) return;

    try {
      const { data, error } = await supabase
        .from('user_favorites')
        .select('meal_id')
        .eq('user_id', session.user.id);

      if (error) throw error;

      const ids = data?.map((item) => item.meal_id) || [];
      setFavoriteIds(ids);
    } catch (error) {
      console.error('Error loading favorites:', error);
    }
  };

  const toggleFavorite = async (mealId: string) => {
    if (!session?.user?.id) return;

    try {
      const isFavorite = favoriteIds.includes(mealId);

      if (isFavorite) {
        const { error } = await supabase
          .from('user_favorites')
          .delete()
          .eq('user_id', session.user.id)
          .eq('meal_id', mealId);

        if (error) throw error;
        setFavoriteIds((prev) => prev.filter((id) => id !== mealId));
      } else {
        const { error } = await supabase.from('user_favorites').insert({
          user_id: session.user.id,
          meal_id: mealId,
        });

        if (error) throw error;
        setFavoriteIds((prev) => [...prev, mealId]);
      }

      await AsyncStorage.setItem('favoritesUpdated', Date.now().toString());
    } catch (error) {
      console.error('Error updating favorites:', error);
    }
  };

  const formatTime = (hours: number | null, minutes: number | null) => {
    if (!hours && !minutes) return '0 min';
    const h = hours || 0;
    const m = minutes || 0;
    return h > 0 ? `${h}h ${m}m` : `${m} min`;
  };

  const getImageSource = (image: string | null) => {
    if (!image || image === 'default.jpg') {
      return require('@/assets/default.png');
    }

    if (image.startsWith('http')) {
      return { uri: image };
    }

    // Construct Supabase Storage URL for meal images with cache busting
    const timestamp = Date.now();
    return {
      uri: `${process.env.EXPO_PUBLIC_SUPABASE_URL}/storage/v1/object/public/meals/${image}?t=${timestamp}`,
    };
  };

  const formatDateRange = (weekStartDate: string) => {
    const start = new Date(weekStartDate);
    const end = new Date(start);
    end.setDate(start.getDate() + 6);

    const formatOptions: Intl.DateTimeFormatOptions = {
      month: 'short',
      day: 'numeric',
    };

    return `${start.toLocaleDateString('en-US', formatOptions)} - ${end.toLocaleDateString('en-US', formatOptions)}`;
  };

  const weekDays = [
    'Sunday',
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
    'Saturday',
  ];

  if (loading) {
    return (
      <ThemedView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <ThemedText style={styles.loadingText}>
            Loading past meals...
          </ThemedText>
        </View>
      </ThemedView>
    );
  }

  if (!archivedPlan || meals.length === 0) {
    return (
      <ThemedView style={styles.container}>
        <ThemedText style={styles.title}>Past Meals</ThemedText>

        <View style={styles.emptyState}>
          <Ionicons
            name="time-outline"
            size={64}
            color={colors.text}
            style={styles.emptyIcon}
          />
          <ThemedText style={[styles.emptyText, { color: colors.text }]}>
            No past meals yet
          </ThemedText>
          <ThemedText style={[styles.emptySubtext, { color: colors.text }]}>
            Your previous week&apos;s meals will appear here after your meal
            plan resets
          </ThemedText>
        </View>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <ThemedText style={styles.title}>Past Meals</ThemedText>

      <View
        style={[
          styles.dateRangeContainer,
          { backgroundColor: colors.cardBackground },
        ]}
      >
        <Ionicons name="calendar-outline" size={20} color={colors.primary} />
        <ThemedText style={[styles.dateRangeText, { color: colors.text }]}>
          Week of {formatDateRange(archivedPlan.week_start_date)}
        </ThemedText>
      </View>

      <ScrollView
        ref={scrollViewRef}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={[
          styles.scrollContent,
          { paddingBottom: contentBottomPadding },
        ]}
      >
        {meals.map((meal, index) => (
          <TouchableOpacity
            key={meal.id}
            style={[
              styles.mealCard,
              { backgroundColor: colors.cardBackground },
            ]}
            onPress={() => router.push(`/meal-detail?mealId=${meal.id}`)}
            activeOpacity={0.7}
          >
            <View style={styles.dayIndicator}>
              <ThemedText style={styles.dayText}>{weekDays[index]}</ThemedText>
            </View>

            <View style={styles.imageContainer}>
              <Image
                source={getImageSource(meal.image)}
                style={styles.mealImage}
              />

              <View style={styles.imageOverlayButtons}>
                <Pressable
                  style={[
                    styles.overlayButton,
                    {
                      backgroundColor:
                        activeTheme === 'light'
                          ? 'rgba(255, 255, 255, 0.9)'
                          : 'rgba(0, 0, 0, 0.6)',
                      borderColor:
                        activeTheme === 'light'
                          ? 'rgba(0, 0, 0, 0.1)'
                          : 'rgba(255, 255, 255, 0.2)',
                    },
                  ]}
                  onPress={(e) => {
                    e.stopPropagation();
                    toggleFavorite(meal.id);
                  }}
                >
                  <Ionicons
                    name={
                      favoriteIds.includes(meal.id) ? 'heart' : 'heart-outline'
                    }
                    size={18}
                    color={
                      favoriteIds.includes(meal.id)
                        ? colors.primary
                        : colors.text
                    }
                  />
                </Pressable>
              </View>
            </View>

            <View style={styles.cardContent}>
              <ThemedText
                style={[styles.mealName, { color: colors.text }]}
                numberOfLines={2}
              >
                {meal.name}
              </ThemedText>

              <View style={styles.categoryIconsContainer}>
                {(() => {
                  const icons = getMealCategoryIcons(meal);
                  return (
                    <>
                      <View
                        style={[
                          styles.categoryIcon,
                          {
                            backgroundColor: `${colors.text}15`,
                            borderColor: `${colors.text}30`,
                          },
                        ]}
                      >
                        <Ionicons
                          name={icons.course.name as any}
                          size={14}
                          color={icons.course.color}
                        />
                        <ThemedText
                          style={[styles.categoryLabel, { color: colors.text }]}
                        >
                          {icons.course.label}
                        </ThemedText>
                      </View>
                      <View
                        style={[
                          styles.categoryIcon,
                          {
                            backgroundColor: `${colors.text}15`,
                            borderColor: `${colors.text}30`,
                          },
                        ]}
                      >
                        <Ionicons
                          name={icons.cuisine.name as any}
                          size={14}
                          color={icons.cuisine.color}
                        />
                        <ThemedText
                          style={[styles.categoryLabel, { color: colors.text }]}
                        >
                          {icons.cuisine.label}
                        </ThemedText>
                      </View>
                      <View
                        style={[
                          styles.categoryIcon,
                          {
                            backgroundColor: `${colors.text}15`,
                            borderColor: `${colors.text}30`,
                          },
                        ]}
                      >
                        <Ionicons
                          name={icons.prepMethod.name as any}
                          size={14}
                          color={icons.prepMethod.color}
                        />
                        <ThemedText
                          style={[styles.categoryLabel, { color: colors.text }]}
                        >
                          {icons.prepMethod.label}
                        </ThemedText>
                      </View>
                    </>
                  );
                })()}
              </View>

              <View style={styles.pillsContainer}>
                <View style={styles.pill}>
                  <ThemedText style={[styles.pillText, { color: colors.text }]}>
                    Prep: {formatTime(meal.prepTimeHour, meal.prepTimeMin)}
                  </ThemedText>
                </View>
                <View style={styles.pill}>
                  <ThemedText style={[styles.pillText, { color: colors.text }]}>
                    Cook: {formatTime(meal.cookTimeHour, meal.cookTimeMin)}
                  </ThemedText>
                </View>
                <View style={styles.pill}>
                  <ThemedText style={[styles.pillText, { color: colors.text }]}>
                    {meal.servingSize || 4} servings
                  </ThemedText>
                </View>
              </View>
            </View>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    paddingHorizontal: 24,
    paddingTop: 80,
    paddingBottom: 20,
  },
  dateRangeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginHorizontal: 24,
    marginBottom: 20,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
  },
  dateRangeText: {
    fontSize: 16,
    fontWeight: '500',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollContent: {
    paddingHorizontal: 24,
    // paddingBottom will be set dynamically based on tab bar height
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyIcon: {
    opacity: 0.5,
    marginBottom: 16,
  },
  emptyText: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 16,
    opacity: 0.7,
    textAlign: 'center',
  },
  mealCard: {
    borderRadius: 8,
    overflow: 'hidden',
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  dayIndicator: {
    position: 'absolute',
    top: 12,
    left: 12,
    zIndex: 2,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  dayText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  imageContainer: {
    position: 'relative',
    width: '100%',
    height: 200,
  },
  mealImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  imageOverlayButtons: {
    position: 'absolute',
    top: 12,
    right: 12,
    zIndex: 1,
  },
  overlayButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  cardContent: {
    padding: 16,
  },
  mealName: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 12,
  },
  categoryIconsContainer: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 12,
  },
  categoryIcon: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    borderWidth: 1,
  },
  categoryLabel: {
    fontSize: 10,
    fontWeight: '500',
    opacity: 0.9,
  },
  pillsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  pill: {},
  pillText: {
    fontSize: 12,
    fontWeight: '400',
    opacity: 0.8,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    opacity: 0.7,
  },
});
