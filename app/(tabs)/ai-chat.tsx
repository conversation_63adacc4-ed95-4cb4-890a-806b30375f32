import { CustomAlert } from '@/components/CustomAlert';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useAuth } from '@/contexts/AuthContext';
import { useTheme } from '@/contexts/ThemeContext';
import type { Database } from '@/lib/database.types';
import {
  callOpenAI as callOpenAIProxy,
  OpenAIProxyError,
} from '@/lib/openaiProxy';
import { getCurrentSeason } from '@/lib/seasonalFiltering';
import { supabase } from '@/lib/supabase';
import {
  getGlobalWeeklyCycleStartDay,
  getWeekStartDate,
} from '@/lib/weekCalculation';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { router } from 'expo-router';
import React, { useEffect, useRef, useState } from 'react';
import {
  ActivityIndicator,
  Image,
  KeyboardAvoidingView,
  Modal,
  Platform,
  Pressable,
  ScrollView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

type UserPreferences = Database['public']['Tables']['user_preferences']['Row'];

interface Message {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
  isLoading?: boolean;
  suggestedMeals?: {
    id: string;
    name: string;
    cuisine_type?: string;
    prepTimeMin?: number;
    cookTimeMin?: number;
    course?: string;
    image?: string;
  }[];
  generatedRecipe?: {
    id: string;
    name: string;
    ingredients:
      | { name: string; unit: string; amount: number; emoji: string }[]
      | string[];
    instructions: string | string[];
    prepTime?: string;
    cookTime?: string;
    course?: string;
  };
}

interface AppAction {
  type: 'search_recipes';
  data?: any;
}

interface RecipeSearchParams {
  query?: string;
  cuisine?: string;
  course?: string;
  maxPrepTime?: number;
  dietaryRestrictions?: string[];
  excludeAllergens?: string[];
}

export default function AssistantScreen() {
  const { activeTheme } = useTheme();
  const colors = Colors[activeTheme];
  const insets = useSafeAreaInsets();
  const { session } = useAuth();
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      text: 'Hi! I\'m your Assistant, how can I help?\nSimply type the word "Generate", and I\'ll create you a new meal!',
      isUser: false,
      timestamp: new Date(),
    },
  ]);
  const [inputText, setInputText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [userPreferences, setUserPreferences] =
    useState<UserPreferences | null>(null);
  const scrollViewRef = useRef<ScrollView>(null);
  const [selectedMeal, setSelectedMeal] = useState<{
    id: string;
    name: string;
  } | null>(null);
  const [showDayPicker, setShowDayPicker] = useState(false);
  const [addingToCart, setAddingToCart] = useState<string | null>(null);
  const [alertConfig, setAlertConfig] = useState<{
    visible: boolean;
    title: string;
    message: string;
  }>({
    visible: false,
    title: '',
    message: '',
  });
  const [recentlyShown, setRecentlyShown] = useState<string[]>([]); // Track recently suggested meal IDs

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    if (session?.user?.id) {
      loadUserPreferences();
    }
  }, [session?.user?.id]); // eslint-disable-line react-hooks/exhaustive-deps

  const loadUserPreferences = async () => {
    try {
      if (!session?.user?.id) {
        return;
      }

      const { data, error } = await supabase
        .from('user_preferences')
        .select('*')
        .eq('user_id', session.user.id)
        .single();

      if (!error && data) {
        setUserPreferences(data);
      }
    } catch (error) {
      // Silently handle error
    }
  };

  const scrollToBottom = () => {
    setTimeout(() => {
      scrollViewRef.current?.scrollToEnd({ animated: true });
    }, 100);
  };

  const checkDailyRateLimit = async (): Promise<boolean> => {
    if (!session?.user?.id || !userPreferences) return false;

    // Use UTC date for consistent reset behavior with database
    const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format in UTC
    const currentUsage = userPreferences.daily_messages_used || 0;
    const currentLimit = userPreferences.daily_messages_limit || 25;
    const lastResetDate = userPreferences.last_message_reset_date;

    // Reset counter if it's a new day
    if (lastResetDate !== today) {
      const { error } = await supabase
        .from('user_preferences')
        .update({
          daily_messages_used: 0,
          last_message_reset_date: today,
        })
        .eq('user_id', session.user.id);

      if (error) {
        return currentUsage >= currentLimit; // Fallback to current limit check
      }

      // Update local state
      setUserPreferences((prev) =>
        prev
          ? {
              ...prev,
              daily_messages_used: 0,
              last_message_reset_date: today,
            }
          : null
      );

      return false; // Not rate limited after reset
    }

    const isLimited = currentUsage >= currentLimit;

    return isLimited;
  };

  const incrementMessageUsage = async () => {
    if (!session?.user?.id || !userPreferences) return;

    const newUsage = (userPreferences.daily_messages_used || 0) + 1;

    await supabase
      .from('user_preferences')
      .update({ daily_messages_used: newUsage })
      .eq('user_id', session.user.id);

    // Update local state
    setUserPreferences((prev) =>
      prev
        ? {
            ...prev,
            daily_messages_used: newUsage,
          }
        : null
    );
  };

  const sendMessage = async () => {
    if (!inputText.trim() || isLoading) return;

    // Check rate limit
    const isRateLimited = await checkDailyRateLimit();
    if (isRateLimited) {
      setAlertConfig({
        visible: true,
        title: 'Daily Message Limit Reached',
        message: `You've reached your daily limit of ${
          userPreferences?.daily_messages_limit || 25
        } messages. Your limit will reset tomorrow.`,
      });
      return;
    }

    const userMessage: Message = {
      id: Date.now().toString(),
      text: inputText.trim(),
      isUser: true,
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, userMessage]);
    setInputText('');
    setIsLoading(true);

    // Increment usage counter
    await incrementMessageUsage();

    try {
      const response = await fetchChatResponse(inputText.trim());

      // Only add message if there's actual text to show
      if (response.text && response.text.trim()) {
        const assistantMessage: Message = {
          id: (Date.now() + 1).toString(),
          text: response.text,
          isUser: false,
          timestamp: new Date(),
        };

        setMessages((prev) => [...prev, assistantMessage]);
      }

      // Handle app actions if any
      if (response.action) {
        await handleAppAction(response.action);
      }
    } catch (error) {
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: "Sorry, I'm having trouble responding right now. Please try again.",
        isUser: false,
        timestamp: new Date(),
      };
      setMessages((prev) => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchChatResponse = async (
    message: string
  ): Promise<{
    text: string;
    action?: AppAction;
    showSwapButtons?: boolean;
  }> => {
    let systemPrompt = `You are a helpful cooking assistant for a meal planning app. You have access to a large database of 2,636 recipes that you can search through.

CRITICAL INSTRUCTION: When users say "generate", "create", or "make up" a recipe, they want you to CREATE A NEW RECIPE using the generate_recipe function. DO NOT search the database when they explicitly ask for generation. Ask clarifying questions first, then generate a custom recipe tailored to their preferences.

DATABASE STRUCTURE:
- Total recipes: 2,636
- Courses: Main Course (2,124 recipes), Side Dish (512 recipes)
- Primary cuisines: American (809), Italian (446), Mexican (263), French (163), Mediterranean (163), Chinese (58), Asian (46)
- All recipes have: name, description, prep/cook times (hours/minutes), ingredients (JSONB), instructions (JSONB), nutrition info
- Recipe fields: course, cuisine_type, prep_method, dietary_tags, allergen_contains, protein_type, required_equipment
- Dietary filtering: Extensive allergen-free tags (LUPINE_FREE, ALCOHOL_FREE, PEANUT_FREE, etc.)
- All ingredients stored as JSONB with structured data including amounts, units, and categories

You can help users with:
1. Searching our recipe database for main course meals (default behavior)
2. Searching for side dishes when explicitly requested
3. Finding meal suggestions based on cuisine, prep time, dietary needs
4. Swapping main course meals in their weekly plan
5. Adding meals to favorites
6. Answering cooking questions

COURSE TYPE DETECTION:
- DEFAULT: Search for 'Main Course' meals only
- SIDE DISH MODE: Detect these patterns and search 'Side Dish' course type:
  * "side dish", "sides", "what goes with", "pair with", "what would go well with"
  * "I need a side dish for...", "what to eat with...", "I can't think of what to eat with..."
  * "accompaniment", "what should I serve with...", "what pairs with..."
- Side dishes cannot be swapped into weekly meal plans (they get recipe + add to cart buttons instead)
- IMPORTANT: When users mention a protein/main dish in side dish requests, NEVER include that protein in your search

INTELLIGENT SIDE DISH SEARCH:
When users ask about sides for a specific protein/dish, the AI must be smart about search terms:

WRONG APPROACH: 
- User: "what goes with steak" → search "steak side dish" ❌
- User: "I need a side dish for chicken" → search "chicken side dish" ❌

CORRECT APPROACH:
- User: "what goes with steak" → search "potato" or "vegetables" ✅
- User: "I need a side dish for chicken" → search "rice" or "vegetables" ✅ 
- User: "I can't think of what to eat with fish" → search "asparagus" or "lemon" ✅

SMART SIDE DISH MAPPINGS:
- Steak/Beef → search: "potato", "vegetables", "mushroom", "asparagus"
- Chicken → search: "rice", "vegetables", "potato", "salad" 
- Fish/Seafood → search: "asparagus", "lemon", "vegetables", "rice"
- Pork → search: "apple", "sweet potato", "vegetables", "beans"
- Any protein → search: "vegetables", "potato", "rice", "salad", "beans"

KEY PRINCIPLE: Side dishes are named by what they ARE, not what they complement!

CONVERSATION STYLE:
- Be conversational and engaging, like a friendly chef
- When users ask for meal suggestions, ask clarifying questions before searching for better results
- Do NOT ask about cuisine preferences - users will specify if they want a particular cuisine
- Always search the database using the search_recipes function for results
- Build rapport and show genuine interest in helping them find the perfect meal

CONVERSATION MEMORY:
- ALWAYS read the full conversation history to understand what the user has already told you
- NEVER repeat questions that have already been answered
- If user mentions protein type (chicken, beef, fish, etc.) don't ask about protein again
- If user mentions time (quick, fast, 30 minutes, etc.) don't ask about cooking time again
- Pay attention to details the user provides and build on them
- When refining a search (e.g., "chicken" then "chicken I can grill"), PRESERVE the original criteria and ADD new ones

IMPORTANT RULES:
- Ask clarifying questions, then search (unless they're very specific already)
- NEVER repeat questions - check conversation history first
- Use exact cuisine names: 'Asian', 'Italian', 'Mexican', 'American', 'Mediterranean', 'French', 'Indian'
- Default to 'Main Course' unless user specifically asks for side dishes
- NEVER mention external recipe websites or sources
- IF the user asks for a meal suggestion based on ingredients they have, provide a quick recipe suggestion, otherwise use search_recipes to find similar meals
- When suggesting meals, show them in individual cards with appropriate buttons (swap for main course, recipe + add to cart for side dishes)
- ALWAYS use the search_recipes function
- Keep responses warm, conversational, and helpful

HANDLING SEARCH REFINEMENTS:
- When a user refines their search (e.g., "chicken" → "chicken I can grill"), include BOTH criteria
- If searching for "chicken I can grill", search with query="chicken" AND add grilling-related terms
- If no results found, try broader search but MAINTAIN the key criteria (e.g., still search for chicken)
- Example: "chicken I can grill" should search: query="chicken grilled" or query="chicken" with prep method considerations

EXAMPLES OF GOOD CONVERSATION FLOW:
User: "Looking for Italian inspired meals"
AI: "I'd love to help you find something delicious Italian! What type of protein are you in the mood for today?"
User: "I want chicken, something quick is fine"
AI: "Perfect! Since you want chicken and something quick, let me find some great Italian chicken recipes for you!" [THEN SEARCH]

User: "I want chicken meals"
AI: [SEARCH with query="chicken"]
User: "What about chicken I can grill?"
AI: "Let me find some grilled chicken recipes for you!" [SEARCH with query="chicken grilled" or query="chicken grill"]

User: "Generate a recipe for me"
AI: "I'd be happy to create a custom recipe just for you! What type of protein would you like to use?"
User: "Chicken"
AI: "Perfect! Let me create a delicious chicken recipe for you!" [THEN USE generate_recipe FUNCTION IMMEDIATELY]

NEVER do this:
User: "I want chicken, something quick is fine"
AI: "Great choice! How much time do you have to cook?" [WRONG - user already said "quick"]

User: "What about chicken I can grill?"
AI: [Returns non-chicken meals] [WRONG - must maintain chicken filter]

Always use the search_recipes function after clarifying questions OR when user has provided enough info (UNLESS they explicitly asked to "generate" a recipe).

RECIPE GENERATION:
- When user specifically says "generate" or asks you to "create" or "make up" a recipe, ALWAYS use the generate_recipe function
- Do NOT search the database when user explicitly asks for generation
- STREAMLINED GENERATION PROCESS: Only ask for protein type if not specified, then generate immediately
- Do NOT ask about cooking time - assume 30-45 minutes total time for most recipes
- Do NOT ask about flavor preferences unless the user specifically mentions wanting something unique
- Keep generated recipes simple, practical, and tailored to user preferences
- ALWAYS include prepTime and cookTime when generating recipes (e.g., "15 min", "30 min")
- CRITICAL: Format ingredients as objects with name, unit, amount, and emoji properties (e.g., {"name": "olive oil", "unit": "tbsp", "amount": 2, "emoji": "🫒"})
- Keep instructions VERY brief (3-4 short steps as an array, each step under 50 characters) to avoid JSON truncation
- Never mention that you're generating a recipe vs. finding one in the database

FALLBACK STRATEGY:
- For normal meal requests (NOT using "generate"), try searching the database first with search_recipes function
- The system will automatically handle fallback recipe generation if no database results are found
- When users explicitly say "generate", "create", or "make up" a recipe, ALWAYS use the generate_recipe function immediately
- NEVER provide plain text recipes - always use the generate_recipe function to ensure recipes are interactive and addable to cart`;

    // Add user preferences to system prompt
    if (userPreferences) {
      systemPrompt += `\n\nUser Profile:\n`;
      if (userPreferences.household_size)
        systemPrompt += `- Household size: ${userPreferences.household_size} people\n`;
      if (userPreferences.cooking_skill_level)
        systemPrompt += `- Cooking skill: ${userPreferences.cooking_skill_level}\n`;
      if (userPreferences.dietary_restrictions?.length)
        systemPrompt += `- Dietary restrictions: ${userPreferences.dietary_restrictions.join(
          ', '
        )}\n`;
      if (userPreferences.allergies?.length)
        systemPrompt += `- Allergies: ${userPreferences.allergies.join(
          ', '
        )}\n`;
      if (userPreferences.cuisine_preferences?.length)
        systemPrompt += `- Favorite cuisines: ${userPreferences.cuisine_preferences.join(
          ', '
        )}\n`;
      if (userPreferences.spice_tolerance)
        systemPrompt += `- Spice tolerance: ${userPreferences.spice_tolerance}\n`;
      if (userPreferences.available_cooking_time)
        systemPrompt += `- Available cooking time: ${userPreferences.available_cooking_time}\n`;
      if (userPreferences.budget_preference)
        systemPrompt += `- Budget preference: ${userPreferences.budget_preference}\n`;
      if (userPreferences.seasonal_preference && userPreferences.seasonal_preference.length > 0) {
        const currentSeason = getCurrentSeason();
        systemPrompt += `- Seasonal preference: ${userPreferences.seasonal_preference.join(', ')}\n`;
        if (userPreferences.seasonal_preference.includes('automatic')) {
          systemPrompt += `- Current season: ${currentSeason} (auto-detected)\n`;
        }
      }
      systemPrompt += `\nAlways consider these preferences when making suggestions. Never suggest meals that conflict with their dietary restrictions or allergies. When generating recipes, consider seasonal appropriateness (e.g., no hot soup in summer unless requested, prefer fresh vegetables in spring/summer, hearty stews in fall/winter).`;
    }

    systemPrompt += `\n\nWhen users ask about food, recipes, or meal ideas, first check the conversation history to see what they've already told you. Only ask clarifying questions about information they haven't provided. If they mention protein and time preferences in one response, proceed directly to search. Never repeat questions. Keep responses conversational, helpful, and engaging.`;

    const tools = [
      {
        type: 'function',
        function: {
          name: 'search_recipes',
          description:
            'Search the recipe database for meals based on various criteria',
          parameters: {
            type: 'object',
            properties: {
              query: {
                type: 'string',
                description: 'Search term for recipe name or description',
              },
              cuisine: {
                type: 'string',
                description:
                  'Exact cuisine type from: Asian, Italian, Mexican, American, Mediterranean, French, Indian',
                enum: [
                  'Asian',
                  'Italian',
                  'Mexican',
                  'American',
                  'Mediterranean',
                  'French',
                  'Indian',
                ],
              },
              course: {
                type: 'string',
                description:
                  'Exact course type from: Main Course, Dessert, Side Dish, Breakfast, Appetizer',
                enum: [
                  'Main Course',
                  'Dessert',
                  'Side Dish',
                  'Breakfast',
                  'Appetizer',
                ],
              },
              maxPrepTime: {
                type: 'number',
                description: 'Maximum preparation time in minutes',
              },
            },
            required: [],
          },
        },
      },
      {
        type: 'function',
        function: {
          name: 'generate_recipe',
          description:
            'Generate a custom recipe when database search fails to find results',
          parameters: {
            type: 'object',
            properties: {
              name: {
                type: 'string',
                description: 'Name of the recipe',
              },
              ingredients: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    name: {
                      type: 'string',
                      description: 'Ingredient name/description',
                    },
                    unit: {
                      type: 'string',
                      description:
                        "Unit of measurement (e.g., 'cups', 'tbsp', 'cloves', 'cans')",
                    },
                    amount: { type: 'number', description: 'Quantity amount' },
                    emoji: {
                      type: 'string',
                      description: 'Relevant emoji for the ingredient',
                    },
                  },
                  required: ['name', 'unit', 'amount', 'emoji'],
                },
                description: 'List of ingredients with structured data format',
              },
              instructions: {
                type: 'array',
                items: {
                  type: 'string',
                },
                description:
                  'Brief cooking instructions as array of 3-4 short steps (each step under 50 characters to avoid truncation)',
              },
              prepTime: {
                type: 'string',
                description: "Preparation time (e.g., '10 min', '15 min')",
              },
              cookTime: {
                type: 'string',
                description: "Cooking time (e.g., '20 min', '30 min')",
              },
              course: {
                type: 'string',
                description: 'Course type: Main Course, Side Dish, etc.',
                enum: [
                  'Main Course',
                  'Side Dish',
                  'Appetizer',
                  'Dessert',
                  'Breakfast',
                ],
              },
            },
            required: [
              'name',
              'ingredients',
              'instructions',
              'prepTime',
              'cookTime',
            ],
          },
        },
      },
    ];

    // Build conversation history for better context
    const conversationHistory = messages.slice(-6).map((msg) => ({
      role: (msg.isUser ? 'user' : 'assistant') as 'user' | 'assistant',
      content: msg.text,
    }));

    let data;
    try {
      // Use the secure OpenAI proxy instead of direct API calls
      data = await callOpenAIProxy({
        model: 'gpt-4o-mini',
        messages: [
          { role: 'system', content: systemPrompt },
          ...conversationHistory,
          { role: 'user', content: message },
        ],
        tools: tools,
        tool_choice: 'auto',
        max_tokens: 800,
        temperature: 0.7,
      });
    } catch (error) {
      if (error instanceof OpenAIProxyError) {
        throw error;
      }
      throw new Error('Failed to get AI response');
    }

    const choice = data.choices[0];

    // Check if AI wants to call a tool
    if (choice?.message?.tool_calls && choice.message.tool_calls.length > 0) {
      const toolCall = choice.message.tool_calls[0];
      const functionCall = toolCall.function;

      if (functionCall.name === 'search_recipes') {
        try {
          const searchParams: RecipeSearchParams = JSON.parse(
            functionCall.arguments
          );

          // Execute the database search
          await searchRecipesInDatabase(searchParams);

          // Return empty to prevent duplicate responses
          return { text: '' };
        } catch (error) {
          // Fallback: try to parse the intent manually
          const fallbackParams: RecipeSearchParams = {
            query: message.toLowerCase().includes('asian')
              ? 'asian'
              : message.toLowerCase().includes('italian')
                ? 'italian'
                : message.toLowerCase().includes('quick')
                  ? 'quick'
                  : message.toLowerCase(),
          };

          await searchRecipesInDatabase(fallbackParams);

          return { text: '' };
        }
      } else if (functionCall.name === 'generate_recipe') {
        try {
          let recipeData;
          try {
            recipeData = JSON.parse(functionCall.arguments);
          } catch (parseError) {
            // JSON parse failed, attempting to fix truncated response

            // Try to fix common truncation issues
            let fixedArguments = functionCall.arguments;

            // Check if the JSON is truncated
            if (!fixedArguments.endsWith('}')) {
              // If the instructions array is cut off (most common case)
              if (
                fixedArguments.includes('"instructions":[') &&
                !fixedArguments.match('"instructions":\\[[^\\]]*\\]')
              ) {
                // Find the start of the instructions array
                const instructionsStart =
                  fixedArguments.lastIndexOf('"instructions":[') +
                  '"instructions":['.length;
                const beforeInstructions = fixedArguments.substring(
                  0,
                  instructionsStart
                );

                // Close the instructions array properly and add closing brace
                fixedArguments =
                  beforeInstructions +
                  '"Season and cook protein","Add vegetables","Simmer until done","Serve hot"]}';
              } else {
                // Generic fix: close any open strings and add missing braces
                const openBraces = (fixedArguments.match(/\{/g) || []).length;
                const closeBraces = (fixedArguments.match(/\}/g) || []).length;
                const missingBraces = openBraces - closeBraces;

                // If we're in the middle of a string, close it
                const quoteCount = (fixedArguments.match(/"/g) || []).length;
                if (quoteCount % 2 === 1) {
                  fixedArguments += '"';
                }

                // Add missing closing braces
                for (let i = 0; i < missingBraces; i++) {
                  fixedArguments += '}';
                }
              }

              recipeData = JSON.parse(fixedArguments);
            } else {
              throw parseError; // Re-throw if we can't fix it
            }
          }

          // Validate the recipe has required fields
          if (
            !recipeData.name ||
            !recipeData.ingredients ||
            !Array.isArray(recipeData.ingredients)
          ) {
            throw new Error('Invalid recipe format - missing required fields');
          }

          // Ensure instructions exist (add fallback if truncated)
          if (
            !recipeData.instructions ||
            (Array.isArray(recipeData.instructions) &&
              recipeData.instructions.length === 0) ||
            (typeof recipeData.instructions === 'string' &&
              recipeData.instructions.trim() === '')
          ) {
            recipeData.instructions = [
              'Season and prepare ingredients',
              'Cook according to recipe',
              'Serve hot',
            ];
          }

          // Save the generated recipe to the database for analysis and get the database ID
          const databaseId = await saveGeneratedRecipeToDatabase(
            recipeData,
            message
          );

          if (!databaseId) {
            return {
              text: 'I created a recipe for you, but had trouble saving it. Please try again.',
            };
          }

          // Create a generated recipe message with the actual database ID
          const generatedRecipeMessage: Message = {
            id: Date.now().toString(),
            text: `Here's a recipe I created for you:`,
            isUser: false,
            timestamp: new Date(),
            generatedRecipe: {
              id: databaseId, // Always use database ID (no fallback)
              name: recipeData.name,
              ingredients: recipeData.ingredients,
              instructions: recipeData.instructions,
              prepTime: recipeData.prepTime,
              cookTime: recipeData.cookTime,
              course: recipeData.course || 'Side Dish',
            },
          };

          setMessages((prev) => [...prev, generatedRecipeMessage]);

          return { text: '' };
        } catch (error) {
          return {
            text: 'I had trouble creating that recipe. Please try a different request.',
          };
        }
      }
    }

    // Regular AI response
    const aiResponse =
      choice?.message?.content || "I'm not sure how to help with that.";

    // Fallback: manual action detection if function calling fails
    let action: AppAction | undefined;
    // Note: Removed automatic search triggers to let AI ask questions first

    return { text: aiResponse, action };
  };

  const handleAppAction = async (action: AppAction) => {
    if (!session?.user?.id) return;

    try {
      switch (action.type) {
        case 'search_recipes':
          if (action.data) {
            await searchRecipesInDatabase(action.data);
          }
          break;
      }
    } catch (error) {
      // Silently handle error
    }
  };

  const handleSwapMeal = (mealId: string, mealName: string) => {
    setSelectedMeal({ id: mealId, name: mealName });
    setShowDayPicker(true);
  };

  const handleViewRecipe = (mealId: string, courseType?: string) => {
    // Find the generated recipe data from messages
    const generatedRecipe = messages.find(
      (msg) => msg.generatedRecipe?.id === mealId
    )?.generatedRecipe;

    if (generatedRecipe) {
      // For AI-generated recipes, pass the data as URL parameters
      const recipeData = {
        id: generatedRecipe.id,
        name: generatedRecipe.name,
        ingredients: generatedRecipe.ingredients,
        instructions: generatedRecipe.instructions,
        prep_time: generatedRecipe.prepTime,
        cook_time: generatedRecipe.cookTime,
        course: generatedRecipe.course || 'Main Course',
      };

      const encodedRecipe = encodeURIComponent(JSON.stringify(recipeData));

      // Use dedicated AI meal detail page for AI-generated recipes
      router.push(
        `/ai-meal-detail?mealId=${mealId}&generatedRecipe=${encodedRecipe}`
      );
    } else {
      // Fallback for database recipes
      if (courseType === 'Side Dish') {
        router.push(`/side-dish-detail?mealId=${mealId}&fromAI=true`);
      } else {
        router.push(`/meal-detail?mealId=${mealId}&fromAI=true`);
      }
    }
  };

  const closeAlert = () => {
    setAlertConfig({ visible: false, title: '', message: '' });
  };

  const saveGeneratedRecipeToDatabase = async (
    recipeData: any,
    userQuery: string
  ): Promise<string | null> => {
    try {
      if (!session?.user?.id) return null;

      // Check if this exact recipe already exists for this user
      const { data: existingRecipe, error: searchError } = await supabase
        .from('ai_generated_meals')
        .select('id, times_generated')
        .eq('name', recipeData.name)
        .eq('user_id', session.user.id)
        .single();

      if (searchError && searchError.code !== 'PGRST116') {
        return null;
      }

      if (existingRecipe) {
        // Recipe exists, increment the counter
        const { error: updateError } = await supabase
          .from('ai_generated_meals')
          .update({
            times_generated: (existingRecipe.times_generated || 0) + 1,
            last_generated_at: new Date().toISOString(),
          })
          .eq('id', existingRecipe.id);

        if (updateError) {
          // Silently handle error
        }
        return existingRecipe.id; // Return existing recipe ID
      } else {
        // Determine cuisine type if not provided
        let cuisineType = recipeData.cuisine_type;
        if (!cuisineType) {
          const recipeName = recipeData.name.toLowerCase();
          if (
            recipeName.includes('italian') ||
            recipeName.includes('pasta') ||
            recipeName.includes('marinara')
          ) {
            cuisineType = 'Italian';
          } else if (
            recipeName.includes('asian') ||
            recipeName.includes('soy') ||
            recipeName.includes('teriyaki')
          ) {
            cuisineType = 'Asian';
          } else if (
            recipeName.includes('mexican') ||
            recipeName.includes('salsa') ||
            recipeName.includes('lime')
          ) {
            cuisineType = 'Mexican';
          } else {
            cuisineType = 'American';
          }
        }

        // Convert time strings to estimated minutes
        const parseTime = (timeStr: string): number | null => {
          if (!timeStr) return null;
          const match = timeStr.match(/(\d+)/);
          return match ? parseInt(match[1]) : null;
        };
        // Now that the database column is JSONB, we can store objects directly
        const ingredientsToSave = recipeData.ingredients;

        const { data: insertData, error: insertError } = await supabase
          .from('ai_generated_meals')
          .insert({
            name: recipeData.name,
            ingredients: ingredientsToSave,
            instructions: recipeData.instructions,
            prep_time: recipeData.prepTime,
            cook_time: recipeData.cookTime,
            course: recipeData.course || 'Side Dish',
            cuisine_type: cuisineType,
            user_query: userQuery,
            user_id: session.user.id,
            estimated_prep_time_min: parseTime(recipeData.prepTime),
            estimated_cook_time_min: parseTime(recipeData.cookTime),
            times_generated: 1,
            generated_at: new Date().toISOString(),
            last_generated_at: new Date().toISOString(),
          })
          .select('id')
          .single();

        if (insertError) {
          return null;
        } else {
          return insertData?.id || null; // Return the new recipe ID
        }
      }
    } catch (error) {
      return null;
    }
  };

  const handleAddGeneratedRecipeToCart = async (recipe: {
    id: string;
    name: string;
    ingredients:
      | { name: string; unit: string; amount: number; emoji: string }[]
      | string[];
    instructions: string | string[];
    prepTime?: string;
    cookTime?: string;
    course?: string;
  }) => {
    if (!session?.user?.id) {
      setAlertConfig({
        visible: true,
        title: 'Sign In Required',
        message: 'Please sign in to add ingredients to your shopping list.',
      });
      return;
    }

    try {
      setAddingToCart(recipe.id);

      // Get current week's meal plan to add ingredients to
      const { data: mealPlan, error: planError } = await supabase
        .from('weekly_meal_plans')
        .select(
          'id, additional_ingredients, selected_side_dishes, ai_generated_meals'
        )
        .eq('user_id', session.user.id)
        .eq('is_active', true)
        .single();

      if (planError) {
        setAlertConfig({
          visible: true,
          title: 'No Meal Plan Found',
          message: 'You need an active meal plan to add recipe ingredients.',
        });
        return;
      }

      // Prepare the AI-generated meal data
      const existingAiMeals = Array.isArray(mealPlan.ai_generated_meals)
        ? mealPlan.ai_generated_meals
        : [];

      // Check if this AI-generated meal is already added
      const isAlreadyAdded = existingAiMeals.some(
        (meal: any) => meal.id === recipe.id
      );
      if (isAlreadyAdded) {
        setAlertConfig({
          visible: true,
          title: 'Already Added',
          message: 'This AI-generated recipe is already in your meal plan.',
        });
        return;
      }

      const newAiMeal = {
        id: recipe.id,
        name: recipe.name,
        ingredients: recipe.ingredients,
        instructions: recipe.instructions,
        prep_time: recipe.prepTime || '',
        cook_time: recipe.cookTime || '',
        course: recipe.course || 'Main Course',
        added_at: new Date().toISOString(),
        is_ai_generated: true,
      };

      // Update the meal plan with AI-generated meals in dedicated field
      const updatedAiMeals = [...existingAiMeals, newAiMeal];

      const { error: updateError } = await supabase
        .from('weekly_meal_plans')
        .update({
          ai_generated_meals: updatedAiMeals,
          updated_at: new Date().toISOString(),
        })
        .eq('id', mealPlan.id);

      if (updateError) {
        throw updateError;
      }

      // Trigger shopping list refresh
      await AsyncStorage.setItem('mealPlanUpdated', Date.now().toString());

      // Show success message in chat
      const successMessage: Message = {
        id: Date.now().toString(),
        text: `Perfect! I've added "${recipe.name}" ingredients to your shopping list. You can find them in your shopping list tab and at the bottom of your meal plan! 🛒✨`,
        isUser: false,
        timestamp: new Date(),
      };
      setMessages((prev) => [...prev, successMessage]);

      // Show custom alert for immediate feedback
      setAlertConfig({
        visible: true,
        title: 'Added to Shopping List!',
        message: `"${recipe.name}" ingredients have been added to your shopping list.`,
      });
    } catch (error) {
      setAlertConfig({
        visible: true,
        title: 'Error',
        message:
          'Failed to add ingredients to shopping list. Please try again.',
      });

      // Also add error message to chat
      const errorMessage: Message = {
        id: Date.now().toString(),
        text: 'Sorry, I had trouble adding those ingredients to your shopping list. Please try again.',
        isUser: false,
        timestamp: new Date(),
      };
      setMessages((prev) => [...prev, errorMessage]);
    } finally {
      setAddingToCart(null);
    }
  };

  const handleAddToCart = async (mealId: string, mealName: string) => {
    if (!session?.user?.id) {
      setAlertConfig({
        visible: true,
        title: 'Sign In Required',
        message: 'Please sign in to add ingredients to your shopping list.',
      });
      return;
    }

    try {
      setAddingToCart(mealId);

      // Get the side dish details including ingredients
      const { data: sideDish, error: sideDishError } = await supabase
        .from('meals')
        .select(
          'ingredients, prepTimeHour, prepTimeMin, cookTimeHour, cookTimeMin'
        )
        .eq('id', mealId)
        .single();

      if (sideDishError) throw sideDishError;

      // Get current week's meal plan to add ingredients to
      const { data: mealPlan, error: planError } = await supabase
        .from('weekly_meal_plans')
        .select('id, additional_ingredients, selected_side_dishes')
        .eq('user_id', session.user.id)
        .eq('is_active', true)
        .single();

      if (planError) {
        setAlertConfig({
          visible: true,
          title: 'No Meal Plan Found',
          message: 'You need an active meal plan to add side dish ingredients.',
        });
        return;
      }

      // Prepare the side dish ingredients with a source identifier
      const sideDishIngredients = sideDish.ingredients || [];
      const existingAdditional = Array.isArray(mealPlan.additional_ingredients)
        ? mealPlan.additional_ingredients
        : [];
      const existingSideDishes = mealPlan.selected_side_dishes || [];

      // Check if this side dish is already added
      const sideDishesArray = Array.isArray(existingSideDishes)
        ? existingSideDishes
        : [];
      const isAlreadyAdded = sideDishesArray.some(
        (dish: any) => dish.id === mealId
      );
      if (isAlreadyAdded) {
        setAlertConfig({
          visible: true,
          title: 'Already Added',
          message: 'This side dish is already in your meal plan.',
        });
        return;
      }

      // Helper function to format time
      const formatTime = (hours: number | null, minutes: number | null) => {
        if (!hours && !minutes) return '';
        const h = hours || 0;
        const m = minutes || 0;
        if (h > 0) {
          return m > 0 ? `${h}h ${m}m` : `${h}h`;
        }
        return `${m}m`;
      };

      // Add source information to identify where these ingredients came from
      const newIngredients = {
        source: 'side_dish',
        source_id: mealId,
        source_name: mealName,
        ingredients: sideDishIngredients,
        added_at: new Date().toISOString(),
      };

      // Add side dish to selected list
      const newSideDish = {
        id: mealId,
        name: mealName,
        prep_time: formatTime(sideDish.prepTimeHour, sideDish.prepTimeMin),
        cook_time: formatTime(sideDish.cookTimeHour, sideDish.cookTimeMin),
        added_at: new Date().toISOString(),
      };

      // Update the meal plan with additional ingredients AND selected side dishes
      const { error: updateError } = await supabase
        .from('weekly_meal_plans')
        .update({
          additional_ingredients: [...existingAdditional, newIngredients],
          selected_side_dishes: [...sideDishesArray, newSideDish],
          updated_at: new Date().toISOString(),
        })
        .eq('id', mealPlan.id);

      if (updateError) throw updateError;

      // Trigger shopping list refresh
      await AsyncStorage.setItem('mealPlanUpdated', Date.now().toString());

      // Show success message in chat
      const successMessage: Message = {
        id: Date.now().toString(),
        text: `Perfect! I've added "${mealName}" ingredients to your shopping list. You can find them in your shopping list tab and at the bottom of your meal plan! 🛒✨`,
        isUser: false,
        timestamp: new Date(),
      };
      setMessages((prev) => [...prev, successMessage]);

      // Show custom alert for immediate feedback
      setAlertConfig({
        visible: true,
        title: 'Added to Shopping List!',
        message: `"${mealName}" ingredients have been added to your shopping list.`,
      });
    } catch (error) {
      setAlertConfig({
        visible: true,
        title: 'Error',
        message:
          'Failed to add ingredients to shopping list. Please try again.',
      });

      // Also add error message to chat
      const errorMessage: Message = {
        id: Date.now().toString(),
        text: 'Sorry, I had trouble adding those ingredients to your shopping list. Please try again.',
        isUser: false,
        timestamp: new Date(),
      };
      setMessages((prev) => [...prev, errorMessage]);
    } finally {
      setAddingToCart(null);
    }
  };

  const handleDaySelect = async (day: string) => {
    setShowDayPicker(false);
    if (selectedMeal) {
      // Add a confirmation message
      const confirmMessage: Message = {
        id: Date.now().toString(),
        text: `Great! I'm adding "${selectedMeal.name}" to your ${day} meal plan... ⏳`,
        isUser: false,
        timestamp: new Date(),
      };
      setMessages((prev) => [...prev, confirmMessage]);

      await performMealSwap(selectedMeal.id, selectedMeal.name, day);
    }
  };

  const performMealSwap = async (
    newMealId: string,
    mealName: string,
    day: string
  ) => {
    try {
      if (!session?.user?.id) {
        return;
      }

      // Get current meal plan
      const { data: mealPlan, error } = await supabase
        .from('weekly_meal_plans')
        .select('id, meal_ids')
        .eq('user_id', session.user.id)
        .eq('is_active', true)
        .single();

      const dayIndex = [
        'Sunday',
        'Monday',
        'Tuesday',
        'Wednesday',
        'Thursday',
        'Friday',
        'Saturday',
      ].indexOf(day);

      if (error || !mealPlan) {
        // Create a new meal plan for the week using user's personalized cycle
        const userCycleStartDay = getGlobalWeeklyCycleStartDay();
        const weekStartDate = getWeekStartDate(userCycleStartDay);

        const newMealIds = new Array(7).fill(null);
        newMealIds[dayIndex] = newMealId;

        const { error: insertError } = await supabase
          .from('weekly_meal_plans')
          .insert({
            user_id: session.user.id,
            week_start_date: weekStartDate,
            meal_ids: newMealIds,
            is_active: true,
          });

        if (insertError) {
          const errorMessage: Message = {
            id: Date.now().toString(),
            text: 'Sorry, I had trouble creating your meal plan. Please try again.',
            isUser: false,
            timestamp: new Date(),
          };
          setMessages((prev) => [...prev, errorMessage]);
          return;
        }
      } else {
        // Update existing meal plan - place meal on specific day
        const updatedMealIds = [
          ...(mealPlan.meal_ids || new Array(7).fill(null)),
        ];
        updatedMealIds[dayIndex] = newMealId;

        // Update the meal plan
        const { error: updateError } = await supabase
          .from('weekly_meal_plans')
          .update({ meal_ids: updatedMealIds })
          .eq('id', mealPlan.id);

        if (updateError) {
          const errorMessage: Message = {
            id: Date.now().toString(),
            text: 'Sorry, I had trouble updating your meal plan. Please try again.',
            isUser: false,
            timestamp: new Date(),
          };
          setMessages((prev) => [...prev, errorMessage]);
          return;
        }
      }

      // Set a flag to trigger meal plan refresh
      await AsyncStorage.setItem('mealPlanUpdated', Date.now().toString());

      // Success message
      const successMessage: Message = {
        id: Date.now().toString(),
        text: `Perfect! I've added "${mealName}" to your ${day} meal plan. Check your meal plan tab to see the update! ✨`,
        isUser: false,
        timestamp: new Date(),
      };
      setMessages((prev) => [...prev, successMessage]);
    } catch (error) {
      const errorMessage: Message = {
        id: Date.now().toString(),
        text: 'Sorry, something went wrong with the meal swap. Please try again.',
        isUser: false,
        timestamp: new Date(),
      };
      setMessages((prev) => [...prev, errorMessage]);
    }
  };

  const searchRecipesInDatabase = async (searchParams: RecipeSearchParams) => {
    try {
      let query = supabase
        .from('meals')
        .select(
          'id, name, course, cuisine_type, "prepTimeMin", cookTimeMin, dietary_tags, allergen_contains, description, image'
        );

      // Apply filters based on search parameters and user preferences
      if (searchParams.query) {
        const searchTerm = searchParams.query.toLowerCase();

        // Check if it's a cuisine type first - enhanced mapping
        const cuisineMap: { [key: string]: string[] } = {
          asian: ['Asian'],
          italian: ['Italian'],
          mexican: ['Mexican'],
          american: ['American'],
          mediterranean: ['Mediterranean'],
          french: ['French'],
          indian: ['Indian'],
          chinese: ['Asian'],
          japanese: ['Asian'],
          thai: ['Asian'],
          korean: ['Asian'],
          greek: ['Mediterranean'],
          latin: ['Mexican'],
        };

        let foundCuisines: string[] = [];
        for (const [key, values] of Object.entries(cuisineMap)) {
          if (searchTerm.includes(key)) {
            foundCuisines = values;
            break;
          }
        }

        if (foundCuisines.length > 0) {
          if (foundCuisines.length === 1) {
            query = query
              .eq('cuisine_type', foundCuisines[0])
              .not('cuisine_type', 'is', null);
          } else {
            query = query
              .in('cuisine_type', foundCuisines)
              .not('cuisine_type', 'is', null);
          }
        } else {
          // Text search in name and description - handle nulls
          query = query
            .or(
              `name.ilike.%${searchParams.query}%,description.ilike.%${searchParams.query}%`
            )
            .not('name', 'is', null);
        }
      }

      if (
        searchParams.cuisine &&
        !searchParams.query
          ?.toLowerCase()
          .includes(searchParams.cuisine.toLowerCase())
      ) {
        query = query
          .eq('cuisine_type', searchParams.cuisine)
          .not('cuisine_type', 'is', null);
      }

      if (searchParams.course) {
        query = query
          .eq('course', searchParams.course)
          .not('course', 'is', null);
      } else {
        // Default to Main Course only if no course is specified
        query = query.eq('course', 'Main Course').not('course', 'is', null);
      }

      if (searchParams.maxPrepTime) {
        query = query
          .lte('"prepTimeMin"', searchParams.maxPrepTime)
          .not('"prepTimeMin"', 'is', null);
      }

      // Spice level filtering removed as column no longer exists

      // Filter by user preferences - handle null dietary_tags
      if (userPreferences?.dietary_restrictions?.length) {
        const dietaryTags = userPreferences.dietary_restrictions;
        // Only apply if dietary_tags is not null and user has restrictions beyond "No Restrictions"
        if (!dietaryTags.includes('No Restrictions')) {
          query = query
            .overlaps('dietary_tags', dietaryTags)
            .not('dietary_tags', 'is', null);
        }
      }

      // Filter out allergens based on user preferences and search params
      if (
        userPreferences?.allergies?.length ||
        searchParams.excludeAllergens?.length
      ) {
        const allergens = [
          ...(userPreferences?.allergies || []),
          ...(searchParams.excludeAllergens || []),
        ];

        // Filter each allergen - use simpler text search approach for reliability
        allergens.forEach((allergen) => {
          const allergenLower = allergen.toLowerCase();

          // Check common allergen mappings
          const allergenPatterns: Record<string, string[]> = {
            fish: ['fish', 'seafood', 'salmon', 'tuna', 'cod', 'halibut'],
            shellfish: [
              'shellfish',
              'shrimp',
              'crab',
              'lobster',
              'oyster',
              'clam',
            ],
            nuts: ['nuts', 'peanut', 'almond', 'walnut', 'pecan'],
            dairy: ['dairy', 'milk', 'cheese', 'butter', 'cream'],
            eggs: ['egg', 'eggs'],
            soy: ['soy', 'soya', 'soybeans'],
            gluten: ['gluten', 'wheat', 'flour'],
          };

          const patterns = allergenPatterns[allergenLower] || [allergenLower];

          // Apply text filters for each pattern - exclude meals containing these terms
          patterns.forEach((pattern: string) => {
            query = query
              .not('name', 'ilike', `%${pattern}%`)
              .not('description', 'ilike', `%${pattern}%`);
          });
        });
      }

      // Get more results and add random offset for variety
      const randomOffset = Math.floor(Math.random() * 100); // Random starting point
      const { data: recipes, error } = await query.range(
        randomOffset,
        randomOffset + 49
      ); // Get 50 results starting from random offset

      if (error) {
        throw error;
      }

      if (recipes?.length && recipes.length > 0) {
        // Filter out recently shown meals to avoid repetition
        const filteredRecipes = recipes.filter(
          (recipe) => !recentlyShown.includes(recipe.id)
        );

        // If we filtered out too many, reset the recently shown list and use all recipes
        const recipesToUse =
          filteredRecipes.length >= 6 ? filteredRecipes : recipes;

        // Randomly select 3 meals from the results for variety
        const shuffledRecipes = recipesToUse.sort(() => Math.random() - 0.5);
        const selectedRecipes = shuffledRecipes.slice(0, 3);

        // Update recently shown meals (keep last 15 to avoid too much repetition)
        const newRecentlyShown = [
          ...recentlyShown,
          ...selectedRecipes.map((r) => r.id),
        ].slice(-15);
        setRecentlyShown(newRecentlyShown);

        const followUpMessage: Message = {
          id: Date.now().toString(),
          text: `I found these recipes in our database:`,
          isUser: false,
          timestamp: new Date(),
          suggestedMeals: selectedRecipes.map((recipe) => ({
            id: recipe.id,
            name: recipe.name || 'Unnamed Recipe',
            cuisine_type: recipe.cuisine_type || 'Various',
            prepTimeMin: recipe.prepTimeMin || undefined,
            cookTimeMin: recipe.cookTimeMin || undefined,
            course: recipe.course || 'Main Course',
            image: recipe.image || undefined,
          })),
        };
        setMessages((prev) => [...prev, followUpMessage]);
      } else {
        // No results found - automatically generate a recipe instead
        const noResultsMessage: Message = {
          id: Date.now().toString(),
          text: "I couldn't find any recipes in our database matching those criteria. Let me create a custom recipe for you instead!",
          isUser: false,
          timestamp: new Date(),
        };
        setMessages((prev) => [...prev, noResultsMessage]);

        // Automatically trigger recipe generation
        await generateFallbackRecipe(searchParams);
      }
    } catch (error) {
      const errorMessage: Message = {
        id: Date.now().toString(),
        text: 'Sorry, I had trouble searching our recipe database. Please try again!',
        isUser: false,
        timestamp: new Date(),
      };
      setMessages((prev) => [...prev, errorMessage]);
    }
  };

  const generateFallbackRecipe = async (searchParams: RecipeSearchParams) => {
    try {
      // Create a recipe generation prompt based on the search parameters
      let generationPrompt = 'Generate a recipe';

      if (searchParams.query) {
        generationPrompt += ` featuring ${searchParams.query}`;
      }
      if (searchParams.cuisine) {
        generationPrompt += ` with ${searchParams.cuisine} flavors`;
      }
      if (searchParams.course) {
        generationPrompt += ` as a ${searchParams.course}`;
      }
      if (searchParams.maxPrepTime) {
        generationPrompt += ` that takes no more than ${searchParams.maxPrepTime} minutes to prepare`;
      }

      // Use the existing OpenAI generation system
      const systemPrompt = `You are a helpful cooking assistant. Generate a custom recipe based on the user's request. Format it properly with ingredients as objects containing name, unit, amount, and emoji properties.`;

      const tools = [
        {
          type: 'function',
          function: {
            name: 'generate_recipe',
            description: 'Generate a custom recipe',
            parameters: {
              type: 'object',
              properties: {
                name: {
                  type: 'string',
                  description: 'Name of the recipe',
                },
                ingredients: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      name: {
                        type: 'string',
                        description: 'Ingredient name/description',
                      },
                      unit: {
                        type: 'string',
                        description:
                          "Unit of measurement (e.g., 'cups', 'tbsp', 'cloves', 'cans')",
                      },
                      amount: {
                        type: 'number',
                        description: 'Quantity amount',
                      },
                      emoji: {
                        type: 'string',
                        description: 'Relevant emoji for the ingredient',
                      },
                    },
                    required: ['name', 'unit', 'amount', 'emoji'],
                  },
                  description:
                    'List of ingredients with structured data format',
                },
                instructions: {
                  type: 'array',
                  items: { type: 'string' },
                  description:
                    'Brief cooking instructions as array of 3-4 short steps (each step under 50 characters to avoid truncation)',
                },
                prepTime: {
                  type: 'string',
                  description: "Preparation time (e.g., '10 min', '15 min')",
                },
                cookTime: {
                  type: 'string',
                  description: "Cooking time (e.g., '20 min', '30 min')",
                },
                course: {
                  type: 'string',
                  description: 'Course type: Main Course, Side Dish, etc.',
                  enum: [
                    'Main Course',
                    'Side Dish',
                    'Appetizer',
                    'Dessert',
                    'Breakfast',
                  ],
                },
              },
              required: [
                'name',
                'ingredients',
                'instructions',
                'prepTime',
                'cookTime',
              ],
            },
          },
        },
      ];

      const data = await callOpenAIProxy({
        model: 'gpt-4o-mini',
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: generationPrompt },
        ],
        tools: tools,
        tool_choice: {
          type: 'function',
          function: { name: 'generate_recipe' },
        },
        max_tokens: 800,
        temperature: 0.7,
      });

      const choice = data.choices[0];
      if (choice?.message?.tool_calls && choice.message.tool_calls.length > 0) {
        const toolCall = choice.message.tool_calls[0];
        const functionCall = toolCall.function;

        if (functionCall.name === 'generate_recipe') {
          let recipeData = JSON.parse(functionCall.arguments);

          // Save the recipe to database and get ID
          const databaseId = await saveGeneratedRecipeToDatabase(
            recipeData,
            generationPrompt
          );

          if (databaseId) {
            // Create the recipe message with Add to Cart functionality
            const generatedRecipeMessage: Message = {
              id: Date.now().toString(),
              text: `Here's a custom recipe I created for you:`,
              isUser: false,
              timestamp: new Date(),
              generatedRecipe: {
                id: databaseId,
                name: recipeData.name,
                ingredients: recipeData.ingredients,
                instructions: recipeData.instructions,
                prepTime: recipeData.prepTime,
                cookTime: recipeData.cookTime,
                course: recipeData.course || 'Main Course',
              },
            };

            setMessages((prev) => [...prev, generatedRecipeMessage]);
          }
        }
      }
    } catch (error) {
      // Show error message if generation fails
      const errorMessage: Message = {
        id: Date.now().toString(),
        text: 'Sorry, I had trouble creating a custom recipe. Please try a different search or ask me to generate something specific!',
        isUser: false,
        timestamp: new Date(),
      };
      setMessages((prev) => [...prev, errorMessage]);
    }
  };

  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 96 : 0}
    >
      <ThemedView style={[styles.container, { paddingTop: insets.top + 8 }]}>
        <View style={[styles.header, { borderBottomColor: colors.border }]}>
          <ThemedText type="title" style={styles.headerTitle}>
            Assistant
          </ThemedText>
          <ThemedText style={styles.disclaimerText}>
            AI can make mistakes. Please verify information.
          </ThemedText>
          {userPreferences && (
            <View style={styles.usageContainer}>
              <ThemedText style={[styles.usageText, { color: colors.primary }]}>
                Messages: {userPreferences.daily_messages_used || 0}/
                {userPreferences.daily_messages_limit || 25} today
              </ThemedText>
            </View>
          )}
        </View>

        <ScrollView
          ref={scrollViewRef}
          style={styles.messagesContainer}
          contentContainerStyle={styles.messagesContent}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          {messages.map((message) => (
            <React.Fragment key={message.id}>
              <View
                style={[
                  styles.messageContainer,
                  message.isUser ? styles.userMessage : styles.assistantMessage,
                ]}
              >
                <View
                  style={[
                    styles.messageBubble,
                    message.isUser
                      ? [styles.userBubble, { backgroundColor: colors.primary }]
                      : [
                          styles.assistantBubble,
                          {
                            backgroundColor: colors.cardBackground,
                            borderColor: colors.border,
                          },
                        ],
                  ]}
                >
                  <ThemedText
                    style={[
                      styles.messageText,
                      message.isUser && { color: colors.background },
                    ]}
                  >
                    {message.text}
                  </ThemedText>
                </View>
              </View>

              {/* Full-width meal suggestions outside the chat bubble */}
              {message.suggestedMeals && !message.isUser && (
                <View style={styles.fullWidthMealsContainer}>
                  {message.suggestedMeals.map((meal, index) => (
                    <View
                      key={meal.id}
                      style={[
                        styles.compactMealCard,
                        { backgroundColor: 'rgba(255, 229, 152, 0.08)' },
                      ]}
                    >
                      {/* Main content area - different layout for main course vs side dish */}
                      {meal.course === 'Side Dish' ? (
                        // Side Dish: Single column layout (no image)
                        <View style={styles.sideDishContent}>
                          <ThemedText style={styles.mealName}>
                            {meal.name}
                          </ThemedText>
                          <View style={styles.mealDetails}>
                            {meal.cuisine_type &&
                              meal.cuisine_type !== 'Various' && (
                                <ThemedText style={styles.mealDetail}>
                                  {meal.cuisine_type}
                                </ThemedText>
                              )}
                            {meal.prepTimeMin && (
                              <ThemedText style={styles.mealDetail}>
                                Prep: {meal.prepTimeMin} min
                              </ThemedText>
                            )}
                            {meal.cookTimeMin && (
                              <ThemedText style={styles.mealDetail}>
                                Cook: {meal.cookTimeMin} min
                              </ThemedText>
                            )}
                            <ThemedText style={styles.mealDetail}>
                              Side Dish
                            </ThemedText>
                          </View>
                        </View>
                      ) : (
                        // Main Course: Two column layout (image left, content right)
                        <View style={styles.mainCourseContent}>
                          {/* Image on the left */}
                          <View style={styles.compactImageContainer}>
                            {meal.image ? (
                              <Image
                                source={{ uri: meal.image }}
                                style={styles.compactImage}
                                resizeMode="cover"
                              />
                            ) : (
                              <View
                                style={[
                                  styles.compactImagePlaceholder,
                                  {
                                    backgroundColor: 'rgba(255, 229, 152, 0.3)',
                                  },
                                ]}
                              >
                                <Ionicons
                                  name="restaurant-outline"
                                  size={20}
                                  color={colors.primary}
                                />
                              </View>
                            )}
                          </View>

                          {/* Content on the right */}
                          <View style={styles.compactContentContainer}>
                            <ThemedText style={styles.mealName}>
                              {meal.name}
                            </ThemedText>
                            <View style={styles.mealDetails}>
                              {meal.cuisine_type &&
                                meal.cuisine_type !== 'Various' && (
                                  <ThemedText style={styles.mealDetail}>
                                    {meal.cuisine_type}
                                  </ThemedText>
                                )}
                              {meal.prepTimeMin && (
                                <ThemedText style={styles.mealDetail}>
                                  Prep: {meal.prepTimeMin} min
                                </ThemedText>
                              )}
                              {meal.cookTimeMin && (
                                <ThemedText style={styles.mealDetail}>
                                  Cook: {meal.cookTimeMin} min
                                </ThemedText>
                              )}
                            </View>
                          </View>
                        </View>
                      )}

                      {/* Global footer with buttons */}
                      <View style={styles.mealFooter}>
                        <TouchableOpacity
                          style={[
                            styles.recipeButton,
                            { borderColor: colors.primary },
                          ]}
                          onPress={() => handleViewRecipe(meal.id, meal.course)}
                        >
                          <Ionicons
                            name="restaurant-outline"
                            size={16}
                            color={colors.primary}
                          />
                          <ThemedText
                            style={[
                              styles.recipeButtonText,
                              { color: colors.primary },
                            ]}
                          >
                            Recipe
                          </ThemedText>
                        </TouchableOpacity>
                        {meal.course === 'Side Dish' ? (
                          <TouchableOpacity
                            style={[
                              styles.individualSwapButton,
                              {
                                backgroundColor: colors.primary,
                                opacity: addingToCart === meal.id ? 0.7 : 1,
                              },
                            ]}
                            onPress={() => handleAddToCart(meal.id, meal.name)}
                            disabled={addingToCart === meal.id}
                          >
                            {addingToCart === meal.id ? (
                              <ActivityIndicator size="small" color="#212121" />
                            ) : (
                              <Ionicons
                                name="cart-outline"
                                size={16}
                                color="#212121"
                              />
                            )}
                            <ThemedText
                              style={[
                                styles.swapButtonText,
                                { color: '#212121' },
                              ]}
                            >
                              {addingToCart === meal.id
                                ? 'Adding...'
                                : 'Add to Cart'}
                            </ThemedText>
                          </TouchableOpacity>
                        ) : (
                          <TouchableOpacity
                            style={[
                              styles.individualSwapButton,
                              { backgroundColor: colors.primary },
                            ]}
                            onPress={() => handleSwapMeal(meal.id, meal.name)}
                          >
                            <ThemedText
                              style={[
                                styles.swapButtonText,
                                { color: '#212121' },
                              ]}
                            >
                              Swap
                            </ThemedText>
                          </TouchableOpacity>
                        )}
                      </View>
                    </View>
                  ))}
                </View>
              )}

              {/* Generated recipe display */}
              {message.generatedRecipe && !message.isUser && (
                <View style={styles.fullWidthMealsContainer}>
                  <View
                    style={[
                      styles.generatedRecipeCard,
                      { backgroundColor: 'rgba(255, 229, 152, 0.08)' },
                    ]}
                  >
                    {/* Recipe Header */}
                    <View style={styles.recipeHeader}>
                      <ThemedText
                        style={[styles.recipeTitle, { color: colors.text }]}
                      >
                        {message.generatedRecipe.name}
                      </ThemedText>
                      <View style={styles.recipeTimeInfo}>
                        {message.generatedRecipe.prepTime && (
                          <ThemedText
                            style={[styles.recipeTime, { color: colors.text }]}
                          >
                            Prep: {message.generatedRecipe.prepTime}
                          </ThemedText>
                        )}
                        {message.generatedRecipe.cookTime && (
                          <ThemedText
                            style={[styles.recipeTime, { color: colors.text }]}
                          >
                            Cook: {message.generatedRecipe.cookTime}
                          </ThemedText>
                        )}
                        {message.generatedRecipe.course && (
                          <ThemedText
                            style={[styles.recipeTime, { color: colors.text }]}
                          >
                            {message.generatedRecipe.course}
                          </ThemedText>
                        )}
                      </View>
                    </View>

                    {/* Ingredients */}
                    <View style={styles.recipeSection}>
                      <ThemedText
                        style={[styles.sectionTitle, { color: colors.primary }]}
                      >
                        Ingredients:
                      </ThemedText>
                      {Array.isArray(message.generatedRecipe.ingredients) &&
                        message.generatedRecipe.ingredients.map(
                          (ingredient, index) => {
                            // Handle both old string format and new object format
                            let displayText: string;

                            if (typeof ingredient === 'string') {
                              displayText = ingredient;
                            } else if (
                              ingredient &&
                              typeof ingredient === 'object' &&
                              'name' in ingredient
                            ) {
                              // Handle object format: {name, unit, amount, emoji}
                              const amount = ingredient.amount || '';
                              const unit = ingredient.unit || '';
                              const name = ingredient.name || '';
                              displayText = `${amount} ${unit} ${name}`.trim();
                            } else {
                              // Fallback for any unexpected format
                              displayText = String(ingredient);
                            }

                            return (
                              <ThemedText
                                key={index}
                                style={[
                                  styles.ingredientItem,
                                  { color: colors.text },
                                ]}
                              >
                                • {displayText}
                              </ThemedText>
                            );
                          }
                        )}
                    </View>

                    {/* Instructions */}
                    <View style={styles.recipeSection}>
                      <ThemedText
                        style={[styles.sectionTitle, { color: colors.primary }]}
                      >
                        Instructions:
                      </ThemedText>
                      {(() => {
                        const instructions =
                          message.generatedRecipe.instructions;
                        if (Array.isArray(instructions)) {
                          return instructions.map((step, index) => (
                            <ThemedText
                              key={index}
                              style={[
                                styles.instructionText,
                                { color: colors.text },
                              ]}
                            >
                              {index + 1}. {step}
                            </ThemedText>
                          ));
                        } else {
                          return (
                            <ThemedText
                              style={[
                                styles.instructionText,
                                { color: colors.text },
                              ]}
                            >
                              {String(instructions)}
                            </ThemedText>
                          );
                        }
                      })()}
                    </View>

                    {/* Add to Cart Button */}
                    <View style={[styles.mealFooter, { marginTop: 12 }]}>
                      <TouchableOpacity
                        style={[
                          styles.recipeButton,
                          {
                            borderColor: colors.primary,
                            opacity:
                              addingToCart === message.generatedRecipe.id
                                ? 0.7
                                : 1,
                          },
                        ]}
                        onPress={() =>
                          handleAddGeneratedRecipeToCart(
                            message.generatedRecipe!
                          )
                        }
                        disabled={addingToCart === message.generatedRecipe.id}
                      >
                        {addingToCart === message.generatedRecipe.id ? (
                          <ActivityIndicator
                            size="small"
                            color={colors.primary}
                          />
                        ) : (
                          <Ionicons
                            name="cart-outline"
                            size={16}
                            color={colors.primary}
                          />
                        )}
                        <ThemedText
                          style={[
                            styles.recipeButtonText,
                            { color: colors.primary },
                          ]}
                        >
                          {addingToCart === message.generatedRecipe.id
                            ? 'Adding...'
                            : 'Add to Cart'}
                        </ThemedText>
                      </TouchableOpacity>
                    </View>
                  </View>
                </View>
              )}
            </React.Fragment>
          ))}

          {isLoading && (
            <View style={[styles.messageContainer, styles.assistantMessage]}>
              <View
                style={[
                  styles.messageBubble,
                  styles.assistantBubble,
                  {
                    backgroundColor: colors.cardBackground,
                    borderColor: colors.border,
                  },
                ]}
              >
                <ActivityIndicator size="small" color={colors.primary} />
                <ThemedText style={styles.messageText}>
                  Assistant is typing...
                </ThemedText>
              </View>
            </View>
          )}
        </ScrollView>

        <View
          style={[
            styles.inputContainer,
            {
              backgroundColor: colors.cardBackground,
              borderColor: colors.border,
            },
          ]}
        >
          <TextInput
            style={[styles.textInput, { color: colors.text }]}
            value={inputText}
            onChangeText={setInputText}
            placeholder="Ask me anything about cooking..."
            placeholderTextColor={colors.tabIconDefault}
            multiline={true}
            maxLength={500}
            onSubmitEditing={sendMessage}
            returnKeyType="send"
            blurOnSubmit={false}
            textAlignVertical="center"
            numberOfLines={1}
            scrollEnabled={false}
          />
          <TouchableOpacity
            style={[
              styles.sendButton,
              {
                backgroundColor:
                  activeTheme === 'light'
                    ? 'transparent'
                    : inputText.trim()
                      ? colors.primary
                      : colors.tabIconDefault,
              },
            ]}
            onPress={sendMessage}
            disabled={!inputText.trim() || isLoading}
          >
            <IconSymbol
              name="send"
              size={20}
              color={
                activeTheme === 'light'
                  ? inputText.trim()
                    ? colors.primary
                    : colors.tabIconDefault
                  : inputText.trim()
                    ? colors.background
                    : colors.text
              }
            />
          </TouchableOpacity>
        </View>

        {/* Day Picker Modal */}
        <Modal
          visible={showDayPicker}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setShowDayPicker(false)}
        >
          <Pressable
            style={styles.modalOverlay}
            onPress={() => setShowDayPicker(false)}
          >
            <View
              style={[
                styles.modalContent,
                { backgroundColor: colors.cardBackground },
              ]}
            >
              <ThemedText style={styles.modalTitle}>
                Choose a day for {selectedMeal?.name}
              </ThemedText>

              {[
                'Sunday',
                'Monday',
                'Tuesday',
                'Wednesday',
                'Thursday',
                'Friday',
                'Saturday',
              ].map((day) => (
                <TouchableOpacity
                  key={day}
                  style={[
                    styles.dayOption,
                    { borderBottomColor: colors.border },
                  ]}
                  onPress={() => handleDaySelect(day)}
                >
                  <ThemedText style={styles.dayText}>{day}</ThemedText>
                  <Ionicons
                    name="chevron-forward"
                    size={20}
                    color={colors.text}
                  />
                </TouchableOpacity>
              ))}

              <TouchableOpacity
                style={styles.cancelButton}
                onPress={() => setShowDayPicker(false)}
              >
                <ThemedText style={[styles.cancelText, { color: colors.text }]}>
                  Cancel
                </ThemedText>
              </TouchableOpacity>
            </View>
          </Pressable>
        </Modal>

        {/* Custom Alert */}
        <CustomAlert
          visible={alertConfig.visible}
          title={alertConfig.title}
          message={alertConfig.message}
          onConfirm={closeAlert}
          confirmText="OK"
        />
      </ThemedView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingBottom: 8,
    borderBottomWidth: 1,
  },
  headerTitle: {
    textAlign: 'center',
    fontSize: 18,
    fontWeight: '600',
  },
  disclaimerText: {
    textAlign: 'center',
    fontSize: 11,
    opacity: 0.5,
    margin: 0,
    padding: 0,
    fontStyle: 'italic',
  },
  usageContainer: {
    alignItems: 'center',
    margin: 0,
    padding: 0,
  },
  usageText: {
    fontSize: 10,
    opacity: 0.7,
  },
  messagesContainer: {
    flex: 1,
  },
  messagesContent: {
    padding: 16,
    gap: 12,
    paddingBottom: Platform.OS === 'ios' ? 180 : 90, // Position of bottom chat window spacing
  },
  messageContainer: {
    maxWidth: '80%',
  },
  userMessage: {
    alignSelf: 'flex-end',
  },
  assistantMessage: {
    alignSelf: 'flex-start',
  },
  messageBubble: {
    padding: 12,
    borderRadius: 16,
  },
  userBubble: {
    borderBottomRightRadius: 4,
  },
  assistantBubble: {
    borderBottomLeftRadius: 4,
    borderWidth: 1,
  },
  messageText: {
    fontSize: 16,
    lineHeight: 22,
  },
  inputContainer: {
    position: 'absolute',
    bottom: Platform.OS === 'ios' ? 110 : 20, // Tab bar height - adjusted for Android
    left: 0,
    right: 0,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginHorizontal: 16,
    borderRadius: 25,
    borderWidth: 1,
    gap: 12,
    minHeight: 50,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    paddingVertical: Platform.OS === 'ios' ? 8 : 4,
    paddingHorizontal: 4,
    minHeight: Platform.OS === 'ios' ? 36 : 26,
    maxHeight: 80,
    lineHeight: 20,
    textAlignVertical: 'center',
  },
  sendButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  mealsContainer: {
    marginTop: 12,
    gap: 8,
  },
  mealCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 12,
    borderWidth: 1,
    gap: 12,
  },
  fullWidthMealsContainer: {
    marginTop: 8,
    marginBottom: 8,
    gap: 8,
  },
  fullWidthMealCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    marginHorizontal: 0,
    borderRadius: 12,
    gap: 12,
    borderWidth: 1,
    borderColor: 'rgba(255, 229, 152, 0.2)',
  },
  compactMealCard: {
    flexDirection: 'column',
    marginHorizontal: 0,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(255, 229, 152, 0.2)',
    overflow: 'hidden',
  },
  // Side dish layout (single column, no image)
  sideDishContent: {
    padding: 16,
    paddingBottom: 8,
  },
  // Main course layout (two columns: image left, content right)
  mainCourseContent: {
    flexDirection: 'row',
    padding: 16,
    paddingBottom: 8,
    gap: 12,
  },
  compactImageContainer: {
    width: 60,
    height: 60,
    borderRadius: 8,
    overflow: 'hidden',
  },
  compactImage: {
    width: '100%',
    height: '100%',
  },
  compactImagePlaceholder: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
  },
  compactContentContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  mealFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: 12,
    paddingBottom: 16,
    gap: 12,
  },
  mealInfo: {
    flex: 1,
  },
  mealName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  mealDetails: {
    flexDirection: 'row',
    gap: 8,
    flexWrap: 'wrap',
  },
  mealDetail: {
    fontSize: 12,
    opacity: 0.7,
    backgroundColor: 'rgba(255, 229, 152, 0.1)',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 8,
  },
  mealActions: {
    flexDirection: 'row',
    gap: 8,
  },
  recipeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 8,
    borderWidth: 1,
    gap: 4,
    flex: 1,
    justifyContent: 'center',
  },
  recipeButtonText: {
    fontSize: 13,
    fontWeight: '600',
  },
  individualSwapButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 8,
    gap: 4,
    flex: 1,
    justifyContent: 'center',
  },
  swapButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 24,
    paddingBottom: 40,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 20,
    textAlign: 'center',
  },
  dayOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  dayText: {
    fontSize: 18,
  },
  cancelButton: {
    marginTop: 20,
    paddingVertical: 12,
    alignItems: 'center',
  },
  cancelText: {
    fontSize: 16,
    opacity: 0.7,
  },
  generatedRecipeCard: {
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(255, 229, 152, 0.2)',
    marginHorizontal: 0,
  },
  recipeHeader: {
    marginBottom: 12,
  },
  recipeTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 4,
  },
  recipeTimeInfo: {
    flexDirection: 'row',
    gap: 12,
    flexWrap: 'wrap',
  },
  recipeTime: {
    fontSize: 13,
    opacity: 0.7,
  },
  recipeSection: {
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 6,
  },
  ingredientItem: {
    fontSize: 14,
    opacity: 0.9,
    marginBottom: 2,
    lineHeight: 18,
  },
  instructionText: {
    fontSize: 14,
    opacity: 0.9,
    lineHeight: 20,
  },
});
