import { Tabs } from 'expo-router';
import React from 'react';
import { Platform, TouchableOpacity, View } from 'react-native';

import { HapticTab } from '@/components/HapticTab';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { TabBarBackground } from '@/components/ui/TabBarBackground';
import { Colors } from '@/constants/Colors';
import { useTheme } from '@/contexts/ThemeContext';
import { useTabBarHeight } from '@/hooks/useTabBarHeight';

// Custom tab button component for the Assistant
function AssistantButton({ children, onPress }: any) {
  const { activeTheme } = useTheme();
  const colors = Colors[activeTheme];

  return (
    <TouchableOpacity
      onPress={onPress}
      style={{
        top: -10,
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 10,
      }}
    >
      <View
        style={{
          width: 60,
          height: 60,
          borderRadius: 30,
          backgroundColor: colors.primary,
          justifyContent: 'center',
          alignItems: 'center',
          shadowColor: '#000',
          shadowOffset: {
            width: 0,
            height: 4,
          },
          shadowOpacity: 0.3,
          shadowRadius: 8,
          elevation: 8,
          borderWidth: 3,
          borderColor: colors.background,
        }}
      >
        <IconSymbol
          size={34}
          name="chat-processing"
          color="#FFFFFF"
        />
      </View>
    </TouchableOpacity>
  );
}

export default function TabLayout() {
  const { activeTheme } = useTheme();
  const colors = Colors[activeTheme];
  const { tabBarHeight, hasAndroidNavBar, androidNavBarHeight } =
    useTabBarHeight();

  // Calculate tab bar padding bottom
  const tabBarPaddingBottom = hasAndroidNavBar ? androidNavBarHeight + 8 : 34;

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: colors.tabIconSelected,
        tabBarInactiveTintColor: colors.tabIconDefault,
        tabBarStyle: {
          backgroundColor:
            activeTheme === 'light' ? '#FFFFFF' : colors.background,
          borderTopWidth: 0,
          height: tabBarHeight,
          paddingBottom: tabBarPaddingBottom,
          paddingTop: 8,
          elevation: 0,
          // Add subtle shadow for light mode
          ...(activeTheme === 'light' && {
            shadowColor: '#000',
            shadowOffset: {
              width: 0,
              height: -2,
            },
            shadowOpacity: 0.1,
            shadowRadius: 4,
          }),
          ...Platform.select({
            ios: {
              position: 'absolute',
            },
            default: {},
          }),
        },
        headerShown: false,
        tabBarButton: HapticTab,
        tabBarBackground: TabBarBackground,
        tabBarShowLabel: false,
        animation: 'shift',
        sceneStyle: { backgroundColor: colors.background },
      }}
    >
      <Tabs.Screen
        name="meal-plan"
        options={{
          title: '',
          tabBarIcon: ({ color }) => (
            <IconSymbol size={26} name="silverware-fork-knife" color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="shopping-list"
        options={{
          title: '',
          tabBarIcon: ({ color }) => (
            <IconSymbol size={26} name="cart-outline" color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="ai-chat"
        options={{
          title: '',
          tabBarIcon: () => null,
          tabBarButton: (props) => <AssistantButton {...props} />,
        }}
      />
      <Tabs.Screen
        name="favorites"
        options={{
          title: '',
          tabBarIcon: ({ color }) => (
            <IconSymbol size={26} name="heart-outline" color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="my-recipes"
        options={{
          title: '',
          tabBarIcon: ({ color }) => (
            <IconSymbol size={26} name="book-open-outline" color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="past-meals"
        options={{
          href: null, // This hides it from the tab bar
        }}
      />
    </Tabs>
  );
}
