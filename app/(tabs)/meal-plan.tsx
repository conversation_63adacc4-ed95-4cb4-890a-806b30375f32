import { CustomAlert } from '@/components/CustomAlert';
import { EditWeekModal } from '@/components/EditWeekModal';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { LoadingIndicator } from '@/components/ui';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useAuth } from '@/contexts/AuthContext';
import { useTheme } from '@/contexts/ThemeContext';
import { useTabBarHeight } from '@/hooks/useTabBarHeight';
import { useStaggeredAnimation } from '@/hooks/useStaggeredAnimation';
import type { Database, Json } from '@/lib/database.types';
import {
  getMealsByIds,
  getMealSwapSuggestions,
  getPersonalizedMealRecommendations,
} from '@/lib/mealFiltering';
import { SecureStorage } from '@/lib/secureStorage';
import { supabase } from '@/lib/supabase';
import { getUserPreferences } from '@/lib/userPreferences';
import {
  getGlobalWeeklyCycleStartDay,
  getWeekStartDate,
} from '@/lib/weekCalculation';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Image } from 'expo-image';
import { router, useFocusEffect, useLocalSearchParams } from 'expo-router';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
  ActivityIndicator,
  Animated,
  Dimensions,
  Easing,
  Pressable,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

// Type guard functions
function ensureUserCustomizedIngredients(
  value: Json | null
): Record<string, any> {
  if (value !== null && typeof value === 'object' && !Array.isArray(value)) {
    return value as Record<string, any>;
  }
  return {};
}

interface MealData {
  id: string;
  name: string;
  prepTimeHour: number | null;
  prepTimeMin: number | null;
  cookTimeHour: number | null;
  cookTimeMin: number | null;
  servingSize: number | null;
  image: string | null;
  rating?: number;
  calories?: number | null;
  protein?: number | null;
  carbs?: number | null;
  fats?: number | null;
  course?: string | null;
  cuisine_type?: string | null;
  prep_method?: string | null;
  required_equipment?: string[] | null;
}

type UserPreferences = Database['public']['Tables']['user_preferences']['Row'];

const { width: screenWidth } = Dimensions.get('window');

export default function MealPlanScreen() {
  const { activeTheme } = useTheme();
  const colors = Colors[activeTheme];
  const insets = useSafeAreaInsets();
  const { session, getUserDisplayName } = useAuth();
  const params = useLocalSearchParams();
  const { contentBottomPadding } = useTabBarHeight();
  const [favoriteIds, setFavoriteIds] = useState<string[]>([]);
  const [weekMeals, setWeekMeals] = useState<MealData[]>([]);
  const [selectedSideDishes, setSelectedSideDishes] = useState<any[]>([]);
  const [aiGeneratedMeals, setAiGeneratedMeals] = useState<any[]>([]);
  const [loadingMeals, setLoadingMeals] = useState(true);
  const [swappingMealIndex, setSwappingMealIndex] = useState<number | null>(
    null
  );
  const [showAlert, setShowAlert] = useState(false);
  const [alertConfig, setAlertConfig] = useState({ title: '', message: '' });
  const [userPreferences, setUserPreferences] =
    useState<UserPreferences | null>(null);
  const [showEditWeekModal, setShowEditWeekModal] = useState(false);
  const [disabledDays, setDisabledDays] = useState<number[]>([]);
  const [hasPastMeals, setHasPastMeals] = useState(false);
  const loadingRef = useRef(false);
  const scrollViewRef = useRef<ScrollView>(null);
  const hasAutoScrolled = useRef(false);
  const mainScrollViewRef = useRef<ScrollView>(null);
  const [shouldAutoScrollToToday, setShouldAutoScrollToToday] = useState(false);

  // Get today's day (0=Sunday, 1=Monday, ..., 6=Saturday)
  const today = new Date().getDay();
  const [selectedCalendarDay, setSelectedCalendarDay] = useState(today);
  const isManualScrolling = useRef(false);
  
  // Use staggered animation hook for 4 sections: header, calendar, meals, nutrition, sideDishes
  const { sections: animatedSections } = useStaggeredAnimation(5);
  const [headerSection, calendarSection, mealsSection, nutritionSection, sideDishesSection] = animatedSections;
  const weekDaysFull = [
    'Sunday',
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
    'Saturday',
  ];

  // Use shared week calculation utility
  // Week resets globally on Friday at midnight UTC for ALL users

  const getImageSource = (image: string | null) => {
    if (!image || image === 'default.jpg') {
      return require('@/assets/default.png');
    }

    // If it's already a full URL, use it as-is
    if (image.startsWith('http')) {
      return { uri: image };
    }

    // Construct Supabase Storage URL for meal images with cache busting
    const timestamp = Date.now();
    return {
      uri: `${process.env.EXPO_PUBLIC_SUPABASE_URL}/storage/v1/object/public/meals/${image}?t=${timestamp}`,
    };
  };

  useEffect(() => {
    if (session?.user?.id) {
      checkShouldAutoScroll();
      loadFavorites();
      loadWeeklyMeals();
      loadUserPreferences();
      checkForPastMeals();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [session]);

  // Check if this is the first visit to meal plan after app open or login
  const checkShouldAutoScroll = async () => {
    if (!session?.user?.id) return;

    try {
      const lastAutoScrollKey = `lastAutoScroll_${session.user.id}`;
      const lastAutoScroll = await AsyncStorage.getItem(lastAutoScrollKey);
      const now = Date.now();

      // Auto-scroll if:
      // 1. Never auto-scrolled for this user, OR
      // 2. Last auto-scroll was more than 1 hour ago (indicates new app session)
      if (!lastAutoScroll || now - parseInt(lastAutoScroll) > 60 * 60 * 1000) {
        setShouldAutoScrollToToday(true);
        await AsyncStorage.setItem(lastAutoScrollKey, now.toString());
      }
    } catch {
      // On error, default to auto-scroll
      setShouldAutoScrollToToday(true);
    }
  };

  // Auto-scroll to today's meal when meals are loaded
  useEffect(() => {
    if (weekMeals.length > 0 && !hasAutoScrolled.current) {
      hasAutoScrolled.current = true;
      // Ensure calendar is set to today first
      setSelectedCalendarDay(today);
      
      // Position meal cards after a short delay
      setTimeout(() => {
        const scrollPosition = today * (screenWidth * 0.85 + 16);
        console.log(`Auto-scrolling to day ${today} (${weekDaysFull[today]}), position: ${scrollPosition}`);
        scrollViewRef.current?.scrollTo({ 
          x: scrollPosition, 
          animated: false 
        });
      }, 300);
    }
  }, [weekMeals.length, today, screenWidth]);

  // Reset auto-scroll flag when meals change or component remounts
  useEffect(() => {
    hasAutoScrolled.current = false;
  }, [session?.user?.id]);

  // Reload meals when screen comes into focus if refresh is requested or no meals loaded
  useFocusEffect(
    useCallback(() => {
      const checkForUpdates = async () => {
        if (!session?.user?.id) return;

        // Check if meal plan was updated (including side dish changes)
        const lastUpdate = await AsyncStorage.getItem('mealPlanUpdated');
        const lastCheck = await SecureStorage.getItem(
          `mealPlanLastCheck_${session.user.id}`
        );

        // Check if favorites were updated
        const favoritesUpdate = await AsyncStorage.getItem('favoritesUpdated');
        const favoritesLastCheck = await SecureStorage.getItem(
          `favoritesLastCheck_${session.user.id}`
        );

        const shouldReloadMeals =
          weekMeals.length === 0 ||
          params.refresh === 'true' ||
          (lastUpdate &&
            lastCheck &&
            parseInt(lastUpdate) > parseInt(lastCheck));

        const shouldReloadFavorites =
          favoritesUpdate &&
          (!favoritesLastCheck ||
            parseInt(favoritesUpdate) > parseInt(favoritesLastCheck));

        if (shouldReloadMeals) {
          loadWeeklyMeals();
          // Update last check timestamp
          await SecureStorage.setItem(
            `mealPlanLastCheck_${session.user.id}`,
            Date.now().toString()
          );
          // Clear the refresh param after loading
          if (params.refresh === 'true') {
            router.setParams({ refresh: undefined });
          }
        }

        if (shouldReloadFavorites) {
          loadFavorites();
          // Update favorites last check timestamp
          await SecureStorage.setItem(
            `favoritesLastCheck_${session.user.id}`,
            Date.now().toString()
          );
        }

        // Always check for past meals when returning to the page
        // This ensures the button appears after meal plan regeneration
        checkForPastMeals();

        // Always scroll main page to top when coming into focus
        mainScrollViewRef.current?.scrollTo({ y: 0, animated: false });
      };

      checkForUpdates();
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [session?.user?.id, weekMeals.length, params.refresh])
  );

  const loadFavorites = async () => {
    if (!session?.user?.id) return;

    try {
      const { data, error } = await supabase
        .from('user_favorites')
        .select('meal_id')
        .eq('user_id', session.user.id);

      if (error) throw error;

      const ids = data?.map((item) => item.meal_id) || [];
      setFavoriteIds(ids);
    } catch {}
  };

  const loadUserPreferences = async () => {
    if (!session?.user?.id) return;

    try {
      const preferences = await getUserPreferences();
      setUserPreferences(preferences);
    } catch {}
  };

  const checkForPastMeals = async () => {
    if (!session?.user?.id) return;

    try {
      // Check for any archived plans (not just from last 7 days)
      // This ensures users can always see their past meals after regeneration
      const { data: archivedPlans } = await supabase
        .from('weekly_meal_plans')
        .select('id, archived_at')
        .eq('user_id', session.user.id)
        .eq('is_active', false)
        .not('archived_at', 'is', null)
        .order('archived_at', { ascending: false })
        .limit(1);

      if (archivedPlans && archivedPlans.length > 0) {
        setHasPastMeals(true);
      } else {
        setHasPastMeals(false);
      }
    } catch {
      setHasPastMeals(false);
    }
  };

  const toggleFavorite = async (mealId: string) => {
    if (!session?.user?.id) {
      setAlertConfig({
        title: 'Sign In Required',
        message: 'Please sign in to save favorites.',
      });
      setShowAlert(true);
      return;
    }

    try {
      const isFavorite = favoriteIds.includes(mealId);

      if (isFavorite) {
        // Remove from favorites
        const { error } = await supabase
          .from('user_favorites')
          .delete()
          .eq('user_id', session.user.id)
          .eq('meal_id', mealId);

        if (error) throw error;

        setFavoriteIds((prev) => prev.filter((id) => id !== mealId));
      } else {
        // Add to favorites
        const { error } = await supabase.from('user_favorites').insert({
          user_id: session.user.id,
          meal_id: mealId,
        });

        if (error) throw error;

        setFavoriteIds((prev) => [...prev, mealId]);
      }

      // Set a flag to trigger favorites refresh on other pages
      await AsyncStorage.setItem('favoritesUpdated', Date.now().toString());
    } catch {
      setAlertConfig({ title: 'Error', message: 'Failed to update favorites' });
      setShowAlert(true);
    }
  };

  const generateWeeklyMealPlan = async (): Promise<string[]> => {
    // Try to use smart recommendations if user preferences are available
    if (userPreferences) {
      try {
        const personalizedMeals = await getPersonalizedMealRecommendations(
          userPreferences,
          7, // 7 meals for the week
          [], // No meals to exclude for new plan
          'Main Course' // ONLY Main Course meals
        );

        if (personalizedMeals.length >= 7) {
          const mealIds = personalizedMeals.map((meal) => meal.id);

          // Verify no duplicates
          const uniqueIds = new Set(mealIds);
          if (uniqueIds.size !== mealIds.length) {
            // Duplicate meal IDs found in personalized recommendations
          }

          return mealIds.slice(0, 7);
        }
      } catch {
        // Error getting personalized recommendations
      }
    }

    const allMainCourses: { id: string }[] = [];

    // Get main courses from the main meals table
    const { data: mainCourses, error: mainError } = await supabase
      .from('meals')
      .select('id')
      .eq('course', 'Main Course')
      .limit(1000);

    if (mainError) {
      throw mainError;
    }

    if (mainCourses && mainCourses.length > 0) {
      allMainCourses.push(...mainCourses);
    }

    // Also get main course user recipes if user is authenticated
    if (session?.user?.id) {
      const { data: userMainCourses, error: userMainError } = await supabase
        .from('user_meals')
        .select('id')
        .eq('user_id', session.user.id)
        .eq('course', 'Main Course');

      if (!userMainError && userMainCourses && userMainCourses.length > 0) {
        allMainCourses.push(...userMainCourses);
      }
    }

    if (allMainCourses.length === 0) {
      throw new Error('No main courses available for meal plan generation');
    }

    const selectedMeals: string[] = [];
    const selectedIds = new Set<string>();

    // Randomly shuffle and select 7 unique main courses
    const shuffledMains = allMainCourses.sort(() => Math.random() - 0.5);

    for (const meal of shuffledMains) {
      if (selectedMeals.length >= 7) break;
      if (!selectedIds.has(meal.id)) {
        selectedMeals.push(meal.id);
        selectedIds.add(meal.id);
      }
    }

    // Final verification - no duplicates
    const uniqueCheck = new Set(selectedMeals);
    if (uniqueCheck.size !== selectedMeals.length) {
      // Duplicate meal IDs detected in final selection
    }

    const finalMeals = selectedMeals.slice(0, 7);
    return finalMeals;
  };

  const createOrUpdateWeeklyMealPlan = async (mealIds: string[]) => {
    if (!session?.user?.id) throw new Error('User not authenticated');

    // Get user's personalized weekly cycle start day
    const userCycleStartDay = getGlobalWeeklyCycleStartDay();
    const weekStartString = getWeekStartDate(userCycleStartDay);

    // First, check if a meal plan already exists for this week
    const { data: existingPlan, error: checkError } = await supabase
      .from('weekly_meal_plans')
      .select('id, meal_ids, created_at, user_customized_ingredients')
      .eq('user_id', session.user.id)
      .eq('week_start_date', weekStartString)
      .single();

    if (checkError && checkError.code !== 'PGRST116') {
      // Error other than "not found"
      throw checkError;
    }

    // Prepare serving size customizations if user has household size preferences
    let servingSizeCustomizations: Record<string, any> = {};
    if (userPreferences?.household_size) {
      // Get meal details to compare serving sizes
      const meals = await getMealsByIds(mealIds, session.user.id);

      meals.forEach((meal) => {
        if (
          meal.servingSize &&
          meal.servingSize !== userPreferences.household_size
        ) {
          servingSizeCustomizations[meal.id] = {
            originalMealId: meal.id,
            originalServingSize: meal.servingSize,
            adjustedServingSize: userPreferences.household_size,
            updatedAt: new Date().toISOString(),
            ingredients: [], // Will be populated when user customizes ingredients
          };
        }
      });
    }

    if (existingPlan) {
      // Update existing meal plan
      const { error: updateError } = await supabase
        .from('weekly_meal_plans')
        .update({
          meal_ids: mealIds,
          is_active: true,
          updated_at: new Date().toISOString(),
          user_customized_ingredients:
            Object.keys(servingSizeCustomizations).length > 0
              ? servingSizeCustomizations
              : existingPlan.user_customized_ingredients || null, // Preserve existing customizations if no new ones
        })
        .eq('id', existingPlan.id);

      if (updateError) {
        throw updateError;
      }
    } else {
      // Create new meal plan only if one doesn't exist
      const { error: insertError } = await supabase
        .from('weekly_meal_plans')
        .insert({
          user_id: session.user.id,
          week_start_date: weekStartString,
          meal_ids: mealIds,
          is_active: true,
          user_customized_ingredients:
            Object.keys(servingSizeCustomizations).length > 0
              ? servingSizeCustomizations
              : null,
        });

      if (insertError) {
        throw insertError;
      }
    }

    const { data: oldPlans } = await supabase
      .from('weekly_meal_plans')
      .select('id, week_start_date')
      .eq('user_id', session.user.id)
      .eq('is_active', true)
      .neq('week_start_date', weekStartString);

    if (oldPlans && oldPlans.length > 0) {
      const { error: deactivateError } = await supabase
        .from('weekly_meal_plans')
        .update({
          is_active: false,
          archived_at: new Date().toISOString(),
        })
        .eq('user_id', session.user.id)
        .eq('is_active', true)
        .neq('week_start_date', weekStartString);

      if (deactivateError) {
        throw deactivateError;
      }

      // Clean up old archived plans (older than 7 days)
      await supabase.rpc('archive_previous_meal_plans', {
        p_user_id: session.user.id,
        p_new_week_start: weekStartString,
      });
    }
  };

  const loadWeeklyMeals = async () => {
    if (!session?.user?.id) {
      return;
    }

    if (loadingRef.current) {
      return; // Prevent concurrent executions
    }

    try {
      loadingRef.current = true;
      setLoadingMeals(true);

      // Get user's personalized weekly cycle start day
      const userCycleStartDay = getGlobalWeeklyCycleStartDay();
      const weekStartString = getWeekStartDate(userCycleStartDay);

      // First check if user has a weekly meal plan for this week
      const { data: mealPlan } = await supabase
        .from('weekly_meal_plans')
        .select(
          'meal_ids, week_start_date, disabled_days, selected_side_dishes, additional_ingredients, ai_generated_meals, user_customized_ingredients, created_at, updated_at'
        )
        .eq('user_id', session.user.id)
        .eq('week_start_date', weekStartString)
        .eq('is_active', true)
        .single();

      let mealIds: string[] = [];

      if (!mealPlan || !mealPlan.meal_ids || mealPlan.meal_ids.length === 0) {
        // No meal plan exists for this week, generate a new one
        try {
          // Set user's weekly cycle start day if this is their first meal plan
          if (userCycleStartDay === 5) {
            // Default value means not set yet
            // User cycle start day setting removed - using global Friday cycle
          }

          mealIds = await generateWeeklyMealPlan();
          await createOrUpdateWeeklyMealPlan(mealIds);
        } catch {
          // Fallback to empty meal plan
          setWeekMeals([]);
          loadingRef.current = false;
          setLoadingMeals(false);
          return;
        }
      } else {
        // Use existing meal plan
        mealIds = mealPlan.meal_ids.filter((id: string | null) => id !== null); // Remove any null values
        // Load disabled days from the meal plan
        setDisabledDays(mealPlan.disabled_days || []);
      }

      // Fetch the actual meal details (from both meals and user_meals tables)
      if (mealIds.length > 0) {
        const meals = await getMealsByIds(
          mealIds,
          session?.user?.id,
          'id, name, "prepTimeHour", "prepTimeMin", "cookTimeHour", "cookTimeMin", "servingSize", image, calories, protein, carbs, fats, course, cuisine_type, prep_method, required_equipment'
        );

        // Map meals in order of meal_ids, maintaining the weekly structure
        const orderedMeals = mealIds
          .map((id) => {
            const meal = meals?.find((m) => m.id === id);
            return meal
              ? {
                  ...meal,
                  rating: 4.2 + Math.random() * 0.6,
                }
              : null;
          })
          .filter(Boolean) as MealData[];

        setWeekMeals(orderedMeals);
      } else {
        setWeekMeals([]);
      }

      // Load selected side dishes and AI-generated meals from the meal plan
      const sideDishes: any[] = [];
      const aiMeals: any[] = [];
      const sideDishIds = new Set<string>(); // Track side dish IDs to prevent duplicates

      // Load from selected_side_dishes
      if (
        mealPlan?.selected_side_dishes &&
        Array.isArray(mealPlan.selected_side_dishes)
      ) {
        mealPlan.selected_side_dishes.forEach((dish: any) => {
          if (dish.id && !sideDishIds.has(dish.id)) {
            sideDishes.push(dish);
            sideDishIds.add(dish.id);
          }
        });
      }

      // Load from additional_ingredients for side dishes only
      if (
        mealPlan?.additional_ingredients &&
        Array.isArray(mealPlan.additional_ingredients)
      ) {
        mealPlan.additional_ingredients.forEach((item: any) => {
          if (item.source === 'side_dish') {
            // Only add side dishes that aren't already in the list
            if (item.source_id && !sideDishIds.has(item.source_id)) {
              sideDishes.push({
                id: item.source_id,
                name: item.source_name,
              });
              sideDishIds.add(item.source_id);
            }
          }
        });
      }

      // Fetch image data for side dishes that don't have it
      if (sideDishes.length > 0) {
        const sideDishesToUpdate = sideDishes.filter((dish) => !dish.image);
        if (sideDishesToUpdate.length > 0) {
          const sideDishIds = sideDishesToUpdate.map((dish) => dish.id);
          const { data: sideDishImages } = await supabase
            .from('meals')
            .select('id, image')
            .in('id', sideDishIds);

          if (sideDishImages) {
            // Update side dishes with image data
            sideDishes.forEach((dish) => {
              const imageData = sideDishImages.find(
                (img) => img.id === dish.id
              );
              if (imageData) {
                dish.image = imageData.image;
              }
            });
          }
        }
      }

      // Load AI-generated meals from dedicated field
      if (
        mealPlan?.ai_generated_meals &&
        Array.isArray(mealPlan.ai_generated_meals)
      ) {
        const validAiMeals: any[] = [];

        for (const aiMeal of mealPlan.ai_generated_meals) {
          // Type assertion for AI meal data
          const aiMealData = aiMeal as any;
          if (!aiMealData || typeof aiMealData !== 'object' || !aiMealData.id)
            continue;

          // Verify the AI-generated meal still exists in the database
          const { data: aiMealExists } = await supabase
            .from('ai_generated_meals')
            .select('id')
            .eq('id', aiMealData.id)
            .single();

          if (aiMealExists) {
            aiMeals.push({
              id: aiMealData.id,
              name: aiMealData.name,
              prepTime: aiMealData.prep_time || '',
              cookTime: aiMealData.cook_time || '',
              course: aiMealData.course || 'Main Course',
              isAIGenerated: true,
            });
            validAiMeals.push(aiMeal);
          }
        }

        // Update the meal plan if we removed any deleted AI meals
        if (validAiMeals.length !== mealPlan.ai_generated_meals.length) {
          await supabase
            .from('weekly_meal_plans')
            .update({
              ai_generated_meals: validAiMeals,
              updated_at: new Date().toISOString(),
            })
            .eq('user_id', session.user.id)
            .eq('week_start_date', weekStartString)
            .eq('is_active', true);
        }
      }

      setSelectedSideDishes(sideDishes);
      setAiGeneratedMeals(aiMeals);

      // Check for past meals after loading current meals
      // This ensures the button shows up after meal plan regeneration
      await checkForPastMeals();
    } catch {
      // Fallback to empty array
      setWeekMeals([]);
    } finally {
      loadingRef.current = false;
      setLoadingMeals(false);
    }
  };

  const saveDisabledDays = async (newDisabledDays: number[]) => {
    if (!session?.user?.id) return;

    try {
      // Get user's personalized weekly cycle start day
      const userCycleStartDay = getGlobalWeeklyCycleStartDay();
      const weekStartString = getWeekStartDate(userCycleStartDay);

      // Update the meal plan with new disabled days
      const { error: updateError } = await supabase
        .from('weekly_meal_plans')
        .update({
          disabled_days: newDisabledDays,
          updated_at: new Date().toISOString(),
        })
        .eq('user_id', session.user.id)
        .eq('week_start_date', weekStartString)
        .eq('is_active', true);

      if (updateError) {
        throw updateError;
      }

      // Update local state
      setDisabledDays(newDisabledDays);

      // Set a flag to trigger shopping list refresh since disabled days affect shopping list
      await AsyncStorage.setItem('mealPlanUpdated', Date.now().toString());

      setAlertConfig({
        title: 'Success',
        message:
          'Week schedule updated! Shopping list will reflect your changes.',
      });
      setShowAlert(true);
    } catch {
      setAlertConfig({
        title: 'Error',
        message: 'Failed to update week schedule. Please try again.',
      });
      setShowAlert(true);
    }
  };

  const swapMeal = async (mealIndex: number) => {
    if (!session?.user?.id) return;

    // Always use Main Course for now
    const currentMealCourse = 'Main Course';

    try {
      setSwappingMealIndex(mealIndex);

      // Use smart filtering if user preferences are available
      let suggestedMeals: any[] = [];

      if (userPreferences) {
        // Get all current meal IDs to exclude from suggestions
        const currentMealIds = weekMeals.map((meal) => meal.id);

        try {
          // Get intelligent meal suggestions based on user preferences
          const smartSuggestions = await getMealSwapSuggestions(
            weekMeals[mealIndex]?.id,
            userPreferences,
            currentMealIds,
            currentMealCourse // Pass the course to get similar meal types
          );

          suggestedMeals = smartSuggestions;
        } catch {
          // Continue to fallback
        }
      }

      // Fallback to random selection if no smart suggestions or no preferences
      if (suggestedMeals.length === 0) {
        try {
          const { data: meals, error } = await supabase
            .from('meals')
            .select(
              'id, name, "prepTimeHour", "prepTimeMin", "cookTimeHour", "cookTimeMin", "servingSize", image, calories, course, cuisine_type, prep_method, required_equipment'
            )
            .neq('id', weekMeals[mealIndex]?.id) // Exclude current meal
            .limit(100); // Limit to prevent too large response

          if (error) throw error;
          suggestedMeals = meals || [];
        } catch {}
      }

      if (suggestedMeals.length === 0) {
        setAlertConfig({
          title: 'Error',
          message: 'No alternative meals available',
        });
        setShowAlert(true);
        return;
      }

      // Pick a random meal from ALL compatible suggestions
      let selectedMeal;
      if (suggestedMeals.length > 0) {
        // Randomly select from the ENTIRE pool of compatible meals
        const randomIndex = Math.floor(Math.random() * suggestedMeals.length);
        selectedMeal = suggestedMeals[randomIndex];
      }

      // Get user's personalized weekly cycle start day and calculate week start date
      const userCycleStartDay = getGlobalWeeklyCycleStartDay();
      const weekStartString = getWeekStartDate(userCycleStartDay);

      // Get current meal plan with week_start_date
      const { data: mealPlan, error: planError } = await supabase
        .from('weekly_meal_plans')
        .select('meal_ids')
        .eq('user_id', session.user.id)
        .eq('week_start_date', weekStartString)
        .eq('is_active', true)
        .single();

      if (planError || !mealPlan) {
        setAlertConfig({
          title: 'Error',
          message: 'Could not find your meal plan',
        });
        setShowAlert(true);
        return;
      }

      // Update the meal at the specific index
      const updatedMealIds = [...(mealPlan.meal_ids || [])];
      updatedMealIds[mealIndex] = selectedMeal.id;

      const { error: updateError } = await supabase
        .from('weekly_meal_plans')
        .update({ meal_ids: updatedMealIds })
        .eq('user_id', session.user.id)
        .eq('week_start_date', weekStartString)
        .eq('is_active', true);

      if (updateError) throw updateError;

      // Update serving size information for household scaling if needed
      if (
        userPreferences?.household_size &&
        selectedMeal.servingSize &&
        selectedMeal.servingSize !== userPreferences.household_size
      ) {
        // Get current customized ingredients
        const { data: currentPlan } = await supabase
          .from('weekly_meal_plans')
          .select('user_customized_ingredients')
          .eq('user_id', session.user.id)
          .eq('week_start_date', weekStartString)
          .eq('is_active', true)
          .single();

        const currentCustomizations = ensureUserCustomizedIngredients(
          currentPlan?.user_customized_ingredients || null
        );

        // Add serving size customization for this meal
        const mealCustomization = {
          originalMealId: selectedMeal.id,
          originalServingSize: selectedMeal.servingSize,
          adjustedServingSize: userPreferences.household_size,
          updatedAt: new Date().toISOString(),
          ingredients: [], // Will be populated when user customizes ingredients
        };

        const updatedCustomizations = {
          ...currentCustomizations,
          [selectedMeal.id]: mealCustomization,
        } as Record<string, any>;

        // Update the weekly meal plan with serving size information
        await supabase
          .from('weekly_meal_plans')
          .update({
            user_customized_ingredients: updatedCustomizations,
            updated_at: new Date().toISOString(),
          })
          .eq('user_id', session.user.id)
          .eq('week_start_date', weekStartString)
          .eq('is_active', true);
      }

      // Update local state immediately without full reload
      const newMeal: MealData = {
        id: selectedMeal.id,
        name: selectedMeal.name,
        prepTimeHour: selectedMeal.prepTimeHour,
        prepTimeMin: selectedMeal.prepTimeMin,
        cookTimeHour: selectedMeal.cookTimeHour,
        cookTimeMin: selectedMeal.cookTimeMin,
        servingSize: selectedMeal.servingSize,
        image: selectedMeal.image,
        calories: selectedMeal.calories || undefined,
        course: selectedMeal.course,
        cuisine_type: selectedMeal.cuisine_type,
        prep_method: selectedMeal.prep_method,
        required_equipment: selectedMeal.required_equipment,
      };

      setWeekMeals((prevMeals) => {
        const updatedMeals = [...prevMeals];
        updatedMeals[mealIndex] = newMeal;
        return updatedMeals;
      });

      // Set a flag to trigger shopping list refresh
      await AsyncStorage.setItem('mealPlanUpdated', Date.now().toString());
    } catch {
      setAlertConfig({
        title: 'Error',
        message: 'Failed to swap meal. Please try again.',
      });
      setShowAlert(true);
    } finally {
      setSwappingMealIndex(null);
    }
  };

  const formatTime = (hours: number | null, minutes: number | null) => {
    if (!hours && !minutes) return '0 min';
    let totalMinutes = (hours || 0) * 60 + (minutes || 0);

    if (totalMinutes >= 60) {
      const h = Math.floor(totalMinutes / 60);
      const m = totalMinutes % 60;
      return m > 0 ? `${h}h ${m}m` : `${h}h`;
    }

    return `${totalMinutes} min`;
  };

  const getEquipmentIcon = (equipment: string) => {
    const equipmentMap: Record<string, any> = {
      oven: require('@/assets/icons/icon-oven.png'),
      skillet: require('@/assets/icons/icon-skillet.png'),
      pot: require('@/assets/icons/icon-pot.png'),
      grill: require('@/assets/icons/icon-grill.png'),
      microwave: require('@/assets/icons/icon-microwave.png'),
      crockpot: require('@/assets/icons/icon-crockpot.png'),
      'slow cooker': require('@/assets/icons/icon-crockpot.png'),
      bowl: require('@/assets/icons/icon-bowl.png'),
      'mixing bowl': require('@/assets/icons/icon-bowl.png'),
    };

    const equipmentLower = equipment.toLowerCase();
    return equipmentMap[equipmentLower] || null;
  };

  // Daily inspirational quotes
  const quotes = [
    'We hope you are in good mood for cooking',
    "Today's a great day for delicious meals",
    "Let's create something amazing together",
    'Good food brings people together',
    'Every meal is a chance to nourish yourself',
    'The kitchen is the heart of the home',
    'Cooking is love made visible',
    'Life is too short for boring food',
    'A recipe has no soul, you must bring soul to it',
    'Food tastes better when you eat it with family',
    'The secret ingredient is always love',
    'Happiness is homemade',
    'Cooking is like painting or writing a song',
    'Food is memories on a plate',
    'Great cooking is about being inspired',
    'Every plate tells a story',
    'The best meals are made with passion',
    'Food is the ingredient that binds us together',
    'Cooking is therapy for the soul',
    'A balanced diet is a cookie in each hand',
    "Food is not just eating energy, it's an experience",
    'The fondest memories are made gathered around the table',
    'Cooking is an art, but all art requires knowing something about the techniques',
    'Food brings people together on many different levels',
    'There is no sincerer love than the love of food',
    "Cooking is at once child's play and adult joy",
    'The kitchen is where we deal with the elements of the universe',
    'A good meal is a moment of pure happiness',
  ];

  const todaysQuote = quotes[today % quotes.length];

  const handleMealPress = (meal: MealData) => {
    // Navigate to detail page with just the meal ID
    // The detail page will fetch the meal data itself
    router.push(`/meal-detail?mealId=${meal.id}`);
  };

  if (loadingMeals || weekMeals.length === 0) {
    return (
      <ThemedView style={styles.container}>
        <View
          style={[
            styles.safeAreaBackground,
            { height: insets.top, backgroundColor: colors.background },
          ]}
        />
        <LoadingIndicator fullScreen text="Loading your meal plan..." />
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      {/* Safe area background */}
      <View
        style={[
          styles.safeAreaBackground,
          { height: insets.top, backgroundColor: colors.background },
        ]}
      />

      <ScrollView
        ref={mainScrollViewRef}
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={[
          styles.scrollContent,
          { paddingTop: insets.top, paddingBottom: contentBottomPadding },
        ]}
      >
        <View
          style={[styles.introSection, { borderBottomColor: colors.border }]}
        >
          <View style={styles.introContent}>
            <View style={styles.introText}>
              <ThemedText style={[styles.greeting, { color: colors.text }]}>
                {`Hi ${getUserDisplayName()?.split(' ')[0] || 'there'}`}
              </ThemedText>
              <ThemedText style={[styles.quote, { color: colors.icon }]}>
                {todaysQuote}
              </ThemedText>
            </View>
            <TouchableOpacity
              style={[
                styles.profileButton,
                {
                  backgroundColor: `${colors.primary}10`,
                },
              ]}
              onPress={() => router.push('/profile')}
            >
              <Ionicons name="settings-outline" size={24} color={colors.text} />
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.weeklyMealsSection}>
          <Animated.View 
            style={[
              styles.weeklyMealsSectionHeader,
              {
                opacity: headerSection.opacity,
                transform: [{ translateY: headerSection.translateY }]
              }
            ]}
          >
            <ThemedText
              type="subtitle"
              style={[
                styles.sectionTitle,
                { color: colors.text, marginBottom: 0 },
              ]}
            >
              Weekly Meals
            </ThemedText>
            <Pressable
              style={[
                styles.editWeekCompactButton,
                {
                  backgroundColor: `${colors.primary}10`,
                },
              ]}
              onPress={() => setShowEditWeekModal(true)}
            >
              <Ionicons
                name="calendar-outline"
                size={16}
                color={colors.text}
                style={{ opacity: 0.7 }}
              />
              <ThemedText
                style={[styles.editWeekCompactText, { color: colors.text }]}
              >
                Edit
              </ThemedText>
            </Pressable>
          </Animated.View>

          {/* Calendar Slider */}
          <Animated.View 
            style={[
              styles.calendarContainer,
              {
                opacity: calendarSection.opacity,
                transform: [{ translateY: calendarSection.translateY }]
              }
            ]}
          >
            {weekDaysFull.map((day, index) => {
              const isSelected = index === selectedCalendarDay;
              const isToday = index === today;
              const isDayDisabled = disabledDays.includes(index);
              const dayDate = new Date();
              dayDate.setDate(dayDate.getDate() + (index - today));

              return (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.calendarDay,
                    {
                      backgroundColor: isSelected
                        ? `${colors.primary}10`
                        : 'transparent',
                      opacity: isDayDisabled ? 0.4 : 1,
                    },
                  ]}
                  onPress={() => {
                    isManualScrolling.current = true;
                    setSelectedCalendarDay(index);
                    // Auto-scroll meal cards to selected day
                    const scrollPosition = index * (screenWidth * 0.85 + 16);
                    scrollViewRef.current?.scrollTo({
                      x: scrollPosition,
                      animated: true,
                    });
                    // Reset manual scrolling flag after scroll completes
                    setTimeout(() => {
                      isManualScrolling.current = false;
                    }, 500);
                  }}
                  disabled={isDayDisabled}
                >
                  <ThemedText
                    style={[
                      styles.calendarDayName,
                      {
                        color: colors.text,
                        opacity: isDayDisabled ? 0.5 : 0.7,
                      },
                    ]}
                  >
                    {day.slice(0, 3)}
                  </ThemedText>
                  <View style={styles.calendarDateContainer}>
                    {isSelected && (
                      <View style={styles.calendarSelectedCircle} />
                    )}
                    <ThemedText
                      style={[
                        styles.calendarDayDate,
                        {
                          color: colors.text,
                          fontWeight: isToday ? '700' : '500',
                        },
                      ]}
                    >
                      {dayDate.getDate()}
                    </ThemedText>
                  </View>
                  {isDayDisabled && (
                    <View style={styles.calendarDisabledIndicator} />
                  )}
                </TouchableOpacity>
              );
            })}
          </Animated.View>

          <Animated.View 
            style={{ 
              opacity: mealsSection.opacity,
              transform: [{ translateY: mealsSection.translateY }]
            }}
          >
            <ScrollView
              ref={scrollViewRef}
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.weeklyMealsScrollContent}
              style={styles.weeklyMealsScroll}
              snapToInterval={screenWidth * 0.85 + 16}
              decelerationRate="fast"
              snapToAlignment="start"
            onScroll={(event) => {
              if (!isManualScrolling.current) {
                const scrollX = event.nativeEvent.contentOffset.x;
                const currentIndex = Math.round(
                  scrollX / (screenWidth * 0.85 + 16)
                );
                if (
                  currentIndex !== selectedCalendarDay &&
                  currentIndex >= 0 &&
                  currentIndex < weekMeals.length
                ) {
                  setSelectedCalendarDay(currentIndex);
                }
              }
            }}
            onMomentumScrollEnd={(event) => {
              isManualScrolling.current = false;
              const scrollX = event.nativeEvent.contentOffset.x;
              const currentIndex = Math.round(
                scrollX / (screenWidth * 0.85 + 16)
              );
              if (
                currentIndex !== selectedCalendarDay &&
                currentIndex >= 0 &&
                currentIndex < weekMeals.length
              ) {
                setSelectedCalendarDay(currentIndex);
              }
            }}
          >
            {weekMeals.map((meal, index) => {
              const isDayDisabled = disabledDays.includes(index);

              return (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.weeklyMealCard,
                    {
                      backgroundColor: colors.cardBackground,
                      width: screenWidth * 0.85,
                      opacity: isDayDisabled ? 0.4 : 1,
                    },
                    isDayDisabled
                      ? [styles.disabledMealCard, { borderColor: '#FF6B6B' }]
                      : null,
                  ].filter(Boolean)}
                  onPress={() => !isDayDisabled && handleMealPress(meal)}
                  activeOpacity={isDayDisabled ? 1 : 0.7}
                  disabled={isDayDisabled}
                >
                  <View style={styles.weeklyMealImageContainer}>
                    <Image
                      source={getImageSource(meal.image)}
                      style={styles.weeklyMealImage}
                    />

                    {isDayDisabled && (
                      <View style={styles.disabledOverlay}>
                        <Ionicons name="ban" size={32} color="#FF6B6B" />
                        <ThemedText
                          style={[styles.disabledText, { color: '#FF6B6B' }]}
                        >
                          Day Disabled
                        </ThemedText>
                      </View>
                    )}

                    {!isDayDisabled && (
                      <View style={styles.imageOverlayButtons}>
                        {/* Swap Button - Top Left */}
                        <Pressable
                          style={[
                            styles.overlayButton,
                            styles.swapOverlayButton,
                            {
                              backgroundColor:
                                activeTheme === 'light'
                                  ? 'rgba(255, 255, 255, 0.9)'
                                  : 'rgba(255, 255, 255, 0.9)',
                              borderColor:
                                activeTheme === 'light'
                                  ? 'rgba(0, 0, 0, 0.1)'
                                  : 'rgba(255, 255, 255, 0.2)',
                              shadowOpacity:
                                activeTheme === 'light' ? 0.25 : 0.15,
                            },
                          ]}
                          onPress={() => swapMeal(index)}
                          disabled={swappingMealIndex === index}
                        >
                          {swappingMealIndex === index ? (
                            <ActivityIndicator size={14} color={colors.text} />
                          ) : (
                            <Ionicons
                              name="refresh"
                              size={18}
                              color={colors.text}
                            />
                          )}
                        </Pressable>

                        {/* Favorite Button - Top Right */}
                        <Pressable
                          style={[
                            styles.overlayButton,
                            {
                              backgroundColor:
                                activeTheme === 'light'
                                  ? 'rgba(255, 255, 255, 0.9)'
                                  : 'rgba(255, 255, 255, 0.9)',
                              borderColor:
                                activeTheme === 'light'
                                  ? 'rgba(0, 0, 0, 0.1)'
                                  : 'rgba(255, 255, 255, 0.2)',
                              shadowOpacity:
                                activeTheme === 'light' ? 0.25 : 0.15,
                            },
                          ]}
                          onPress={() => toggleFavorite(meal.id)}
                        >
                          <Ionicons
                            name={
                              favoriteIds.includes(meal.id)
                                ? 'heart'
                                : 'heart-outline'
                            }
                            size={18}
                            color={
                              favoriteIds.includes(meal.id)
                                ? colors.primary
                                : colors.text
                            }
                          />
                        </Pressable>
                      </View>
                    )}
                  </View>

                  <View style={styles.weeklyMealCardContent}>
                    <ThemedText
                      style={[styles.weeklyMealName, { color: colors.text }]}
                      numberOfLines={2}
                    >
                      {meal.name}
                    </ThemedText>

                    <View style={styles.mealInfoContainer}>
                      <View style={styles.mealInfoItem}>
                        <Ionicons
                          name="time-outline"
                          size={14}
                          color={colors.text}
                          style={{ opacity: 0.7 }}
                        />
                        <ThemedText
                          style={[styles.mealInfoText, { color: colors.text }]}
                        >
                          {formatTime(meal.prepTimeHour, meal.prepTimeMin)}
                        </ThemedText>
                      </View>
                      <View style={styles.mealInfoItem}>
                        <Ionicons
                          name="flame-outline"
                          size={14}
                          color={colors.text}
                          style={{ opacity: 0.7 }}
                        />
                        <ThemedText
                          style={[styles.mealInfoText, { color: colors.text }]}
                        >
                          {formatTime(meal.cookTimeHour, meal.cookTimeMin)}
                        </ThemedText>
                      </View>
                      <View style={styles.mealInfoItem}>
                        <Ionicons
                          name="people-outline"
                          size={14}
                          color={colors.text}
                          style={{ opacity: 0.7 }}
                        />
                        <ThemedText
                          style={[styles.mealInfoText, { color: colors.text }]}
                        >
                          {meal.servingSize || 4}
                        </ThemedText>
                      </View>
                    </View>
                  </View>
                </TouchableOpacity>
              );
            })}
            </ScrollView>
          </Animated.View>
        </View>

        {/* Weekly Nutrition */}
        <Animated.View 
          style={[
            styles.analyticsSection,
            {
              opacity: nutritionSection.opacity,
              transform: [{ translateY: nutritionSection.translateY }]
            }
          ]}
        >
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.analyticsScrollContent}
            style={styles.analyticsScroll}
          >
            {/* Calories Card */}
            <View
              style={[
                styles.analyticsCard,
                { backgroundColor: colors.cardBackground },
              ]}
            >
              <View style={styles.analyticsHeader}>
                <ThemedText
                  style={[styles.analyticsLabel, { color: colors.text }]}
                >
                  Calories
                </ThemedText>
                <Ionicons name="flame" size={20} color="#FF6B35" />
              </View>
              <ThemedText
                style={[styles.analyticsValue, { color: colors.text }]}
              >
                {weekMeals
                  .reduce((total, meal) => total + (meal.calories || 0), 0)
                  .toLocaleString()}
              </ThemedText>
              <ThemedText
                style={[styles.analyticsSubtext, { color: colors.text }]}
              >
                weekly total
              </ThemedText>
            </View>

            {/* Protein Card */}
            <View
              style={[
                styles.analyticsCard,
                { backgroundColor: colors.cardBackground },
              ]}
            >
              <View style={styles.analyticsHeader}>
                <ThemedText
                  style={[styles.analyticsLabel, { color: colors.text }]}
                >
                  Protein
                </ThemedText>
                <Ionicons name="fitness" size={20} color="#2196F3" />
              </View>
              <ThemedText
                style={[styles.analyticsValue, { color: colors.text }]}
              >
                {weekMeals.reduce(
                  (total, meal) => total + (meal.protein || 0),
                  0
                )}
              </ThemedText>
              <ThemedText
                style={[styles.analyticsSubtext, { color: colors.text }]}
              >
                weekly total
              </ThemedText>
            </View>

            {/* Carbs Card */}
            <View
              style={[
                styles.analyticsCard,
                { backgroundColor: colors.cardBackground },
              ]}
            >
              <View style={styles.analyticsHeader}>
                <ThemedText
                  style={[styles.analyticsLabel, { color: colors.text }]}
                >
                  Carbs
                </ThemedText>
                <Ionicons name="leaf" size={20} color={colors.primary} />
              </View>
              <ThemedText
                style={[styles.analyticsValue, { color: colors.text }]}
              >
                {weekMeals.reduce(
                  (total, meal) => total + (meal.carbs || 0),
                  0
                )}
              </ThemedText>
              <ThemedText
                style={[styles.analyticsSubtext, { color: colors.text }]}
              >
                weekly total
              </ThemedText>
            </View>

            {/* Fat Card */}
            <View
              style={[
                styles.analyticsCard,
                { backgroundColor: colors.cardBackground },
              ]}
            >
              <View style={styles.analyticsHeader}>
                <ThemedText
                  style={[styles.analyticsLabel, { color: colors.text }]}
                >
                  Fat
                </ThemedText>
                <Ionicons name="water" size={20} color="#FF9800" />
              </View>
              <ThemedText
                style={[styles.analyticsValue, { color: colors.text }]}
              >
                {weekMeals.reduce((total, meal) => total + (meal.fats || 0), 0)}
              </ThemedText>
              <ThemedText
                style={[styles.analyticsSubtext, { color: colors.text }]}
              >
                weekly total
              </ThemedText>
            </View>
          </ScrollView>
        </Animated.View>

        {/* Selected Side Dishes Section - Replace AI Prompt */}
        <Animated.View 
          style={[
            styles.sideDishesSection,
            {
              opacity: sideDishesSection.opacity,
              transform: [{ translateY: sideDishesSection.translateY }]
            }
          ]}
        >
          <View style={styles.sectionTitleContainer}>
            <IconSymbol name="food" size={20} color={colors.primary} />
            <ThemedText
              style={[styles.sideDishesTitle, { color: colors.text }]}
            >
              Your Side Dishes
            </ThemedText>
          </View>
          {selectedSideDishes.length > 0 ? (
            <View style={styles.sideDishesGrid}>
              {selectedSideDishes.map((dish: any) => (
                <TouchableOpacity
                  key={dish.id}
                  style={[
                    styles.sideDishCard,
                    {
                      backgroundColor: colors.cardBackground,
                      borderColor: colors.border,
                    },
                  ]}
                  onPress={() =>
                    router.push({
                      pathname: '/side-dish-detail',
                      params: {
                        mealId: dish.id,
                      },
                    })
                  }
                >
                  <Image
                    source={getImageSource(dish.image)}
                    style={styles.sideDishCardImage}
                    resizeMode="cover"
                  />
                  <View style={styles.sideDishCardContent}>
                    <View style={styles.sideDishCardHeader}>
                      <ThemedText
                        style={[
                          styles.sideDishCardName,
                          { color: colors.text },
                        ]}
                        numberOfLines={2}
                      >
                        {dish.name}
                      </ThemedText>
                    </View>
                    <View style={styles.sideDishCardTimes}>
                      <ThemedText
                        style={[
                          styles.sideDishCardTime,
                          { color: colors.text },
                        ]}
                      >
                        Prep: {dish.prep_time}
                      </ThemedText>
                      <ThemedText
                        style={[
                          styles.sideDishCardTime,
                          { color: colors.text },
                        ]}
                      >
                        Cook: {dish.cook_time}
                      </ThemedText>
                    </View>
                  </View>
                </TouchableOpacity>
              ))}
            </View>
          ) : (
            <View
              style={[
                styles.noSideDishesContainer,
                {
                  backgroundColor: `${colors.primary}0D`,
                  borderColor: `${colors.primary}33`,
                },
              ]}
            >
              <IconSymbol
                name="food"
                size={32}
                color={colors.primary}
                style={styles.noSideDishesIcon}
              />
              <ThemedText
                style={[styles.noSideDishesText, { color: colors.text }]}
              >
                No side dishes added yet
              </ThemedText>
              <ThemedText
                style={[styles.noSideDishesSubtext, { color: colors.text }]}
              >
                Browse meal details to discover and add side dishes to your plan
              </ThemedText>
            </View>
          )}
        </Animated.View>

        {/* AI-Generated Meals Section */}
        <View style={styles.aiGeneratedSection}>
          <View style={styles.sectionTitleContainer}>
            <Ionicons name="sparkles" size={20} color={colors.primary} />
            <ThemedText
              style={[styles.aiGeneratedTitle, { color: colors.text }]}
            >
              AI-Generated Meals
            </ThemedText>
          </View>
          {aiGeneratedMeals.length > 0 ? (
            <View style={styles.aiGeneratedGrid}>
              {aiGeneratedMeals.map((meal: any) => (
                <TouchableOpacity
                  key={meal.id}
                  style={[
                    styles.aiGeneratedCard,
                    {
                      backgroundColor: colors.cardBackground,
                      borderColor: colors.border,
                    },
                  ]}
                  onPress={async () => {
                    // Fetch the full AI-generated recipe from database
                    try {
                      const { data: aiRecipe, error } = await supabase
                        .from('ai_generated_meals')
                        .select('*')
                        .eq('id', meal.id)
                        .single();

                      if (!error && aiRecipe) {
                        const recipeData = {
                          id: aiRecipe.id,
                          name: aiRecipe.name,
                          ingredients: aiRecipe.ingredients,
                          instructions: aiRecipe.instructions,
                          prep_time: aiRecipe.prep_time,
                          cook_time: aiRecipe.cook_time,
                          course: aiRecipe.course || 'Main Course',
                        };
                        const encodedRecipe = encodeURIComponent(
                          JSON.stringify(recipeData)
                        );
                        router.push(
                          `/ai-meal-detail?mealId=${meal.id}&generatedRecipe=${encodedRecipe}`
                        );
                      } else {
                        // Fallback: just navigate with meal ID to AI detail page
                        router.push(`/ai-meal-detail?mealId=${meal.id}`);
                      }
                    } catch {
                      // Fallback: just navigate with meal ID to AI detail page
                      router.push(`/ai-meal-detail?mealId=${meal.id}`);
                    }
                  }}
                >
                  <View style={styles.aiGeneratedCardContent}>
                    <View style={styles.aiGeneratedHeader}>
                      <ThemedText
                        style={[
                          styles.aiGeneratedCardTitle,
                          { color: colors.text },
                        ]}
                      >
                        {meal.name}
                      </ThemedText>
                    </View>
                    <View style={styles.aiGeneratedInfo}>
                      <ThemedText
                        style={[
                          styles.aiGeneratedCourse,
                          { color: colors.primary },
                        ]}
                      >
                        {meal.course}
                      </ThemedText>
                      {(meal.prepTime || meal.cookTime) && (
                        <View style={styles.aiGeneratedTimes}>
                          {meal.prepTime && (
                            <ThemedText
                              style={[
                                styles.aiGeneratedTime,
                                { color: colors.text },
                              ]}
                            >
                              Prep: {meal.prepTime}
                            </ThemedText>
                          )}
                          {meal.cookTime && (
                            <ThemedText
                              style={[
                                styles.aiGeneratedTime,
                                { color: colors.text },
                              ]}
                            >
                              Cook: {meal.cookTime}
                            </ThemedText>
                          )}
                        </View>
                      )}
                    </View>
                  </View>
                </TouchableOpacity>
              ))}
            </View>
          ) : (
            <View
              style={[
                styles.noAiMealsContainer,
                {
                  backgroundColor: `${colors.primary}0D`,
                  borderColor: `${colors.primary}33`,
                },
              ]}
            >
              <Ionicons
                name="sparkles-outline"
                size={32}
                color={colors.tabIconDefault}
              />
              <ThemedText
                style={[styles.noAiMealsText, { color: colors.text }]}
              >
                No AI-generated meals yet
              </ThemedText>
              <ThemedText
                style={[styles.noAiMealsSubtext, { color: colors.text }]}
              >
                Chat with the AI assistant to create custom recipes!
              </ThemedText>
            </View>
          )}
        </View>

        {/* Past Meals Button - Show only if user has past meals */}
        {hasPastMeals && (
          <View style={styles.pastMealsButtonContainer}>
            <TouchableOpacity
              style={[
                styles.pastMealsButton,
                {
                  backgroundColor: 'transparent',
                  borderColor: colors.primary,
                  borderWidth: 1,
                },
              ]}
              onPress={() => router.push('/(tabs)/past-meals')}
            >
              <Ionicons name="time-outline" size={20} color={colors.primary} />
              <ThemedText
                style={[styles.pastMealsButtonText, { color: colors.primary }]}
              >
                View Past Week&apos;s Meals
              </ThemedText>
              <Ionicons
                name="chevron-forward"
                size={20}
                color={colors.primary}
              />
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>

      <CustomAlert
        visible={showAlert}
        title={alertConfig.title}
        message={alertConfig.message}
        onConfirm={() => setShowAlert(false)}
        onCancel={() => setShowAlert(false)}
      />

      <EditWeekModal
        visible={showEditWeekModal}
        onClose={() => setShowEditWeekModal(false)}
        onSave={saveDisabledDays}
        currentDisabledDays={disabledDays}
      />
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    opacity: 0.7,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    // paddingBottom will be set dynamically based on tab bar height
  },
  safeAreaBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1,
  },
  // Intro Section Styles
  introSection: {
    marginHorizontal: 24,
    marginBottom: 32,
    paddingHorizontal: 0,
    paddingTop: 24,
    paddingBottom: 32,
    borderBottomWidth: 0.2,
  },
  introContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  introText: {
    flex: 1,
  },
  profileButton: {
    padding: 8,
    borderRadius: 8,
  },
  greeting: {
    fontSize: 28,
    fontWeight: '700',
    marginBottom: 8,
    paddingTop: 10,
  },
  quote: {
    fontSize: 16,
    lineHeight: 24,
    flexWrap: 'wrap',
    maxWidth: 200,
  },
  // Analytics Section Styles
  analyticsSection: {
    marginBottom: 32,
    paddingBottom: 4, // Extra space for card shadows
  },
  analyticsScroll: {
    paddingLeft: 24,
  },
  analyticsScrollContent: {
    paddingRight: 24,
    paddingBottom: 8, // Add bottom padding to show card shadows
  },
  analyticsCard: {
    width: 140,
    padding: 16,
    borderRadius: 8,
    marginRight: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  analyticsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  analyticsLabel: {
    fontSize: 12,
    opacity: 0.7,
    fontWeight: '500',
  },
  analyticsValue: {
    fontSize: 28,
    fontWeight: '700',
    marginBottom: 4,
    lineHeight: 34,
  },
  analyticsSubtext: {
    fontSize: 10,
    opacity: 0.5,
  },
  // Meals Section Styles
  mealsSection: {
    paddingHorizontal: 24,
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 15,
  },
  editWeekCompactButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 8,
    gap: 4,
  },
  editWeekCompactText: {
    fontSize: 12,
    fontWeight: '500',
    opacity: 0.7,
  },
  mealsContainer: {
    gap: 16,
  },
  imageOverlayButtons: {
    position: 'absolute',
    top: 12,
    left: 12,
    right: 12,
    bottom: 12,
    zIndex: 1,
  },
  overlayButton: {
    position: 'absolute',
    top: 0,
    right: 0,
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowRadius: 3.84,
    elevation: 5,
  },
  dayLabelContainer: {
    marginBottom: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    flexWrap: 'wrap',
    gap: 8,
  },
  dayLabel: {
    fontSize: 16,
    fontWeight: '700',
    textTransform: 'uppercase',
    letterSpacing: 0.8,
    flex: 1,
  },
  pillsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginTop: 12,
  },
  pill: {
    // No background, no padding
  },
  pillText: {
    fontSize: 12,
    fontWeight: '400',
    opacity: 0.8,
  },
  aiPrompt: {
    flexDirection: 'row',
    alignItems: 'center',
    margin: 24,
    padding: 16,
    borderRadius: 8, // Reduced from 12
    // backgroundColor should be applied dynamically: backgroundColor: `${colors.primary}0D`
    gap: 12,
  },
  aiPromptText: {
    flex: 1,
    fontSize: 14,
  },
  sideDishesSection: {
    marginHorizontal: 24,
    marginBottom: 24,
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 16,
  },
  sideDishesTitle: {
    fontSize: 20,
    fontWeight: '700',
  },
  sideDishesGrid: {
    gap: 12,
  },
  sideDishCard: {
    width: '100%',
    flexDirection: 'row',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    gap: 12,
  },
  sideDishCardImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
  },
  sideDishCardContent: {
    flex: 1,
    justifyContent: 'center',
  },
  sideDishCardHeader: {
    marginBottom: 8,
  },
  sideDishCardName: {
    fontSize: 16,
    fontWeight: '600',
  },
  sideDishCardTimes: {
    flexDirection: 'row',
    gap: 12,
  },
  sideDishCardTime: {
    fontSize: 12,
    opacity: 0.8,
  },
  noSideDishesContainer: {
    alignItems: 'center',
    padding: 24,
    borderRadius: 8,
    borderWidth: 1,
  },
  noSideDishesIcon: {
    opacity: 0.6,
    marginBottom: 12,
  },
  noSideDishesText: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
    textAlign: 'center',
  },
  noSideDishesSubtext: {
    fontSize: 14,
    opacity: 0.7,
    textAlign: 'center',
    lineHeight: 20,
  },
  categoryIconsContainer: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 8,
    marginBottom: 4,
  },
  categoryIcon: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
    borderWidth: 1,
  },
  categoryLabel: {
    fontSize: 10,
    fontWeight: '500',
    opacity: 0.9,
  },
  disabledMealCard: {
    borderWidth: 2,
    borderStyle: 'dashed',
  },
  disabledOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 2,
  },
  disabledText: {
    fontSize: 12,
    fontWeight: '600',
    marginTop: 4,
  },
  disabledDayLabel: {
    opacity: 0.8,
  },
  // AI-Generated Meals Section Styles
  aiGeneratedSection: {
    marginHorizontal: 24,
    marginBottom: 24,
  },
  aiGeneratedTitle: {
    fontSize: 20,
    fontWeight: '700',
  },
  aiGeneratedGrid: {
    gap: 12,
  },
  aiGeneratedCard: {
    width: '100%',
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderStyle: 'solid',
  },
  aiGeneratedCardContent: {
    gap: 8,
  },
  aiGeneratedHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 4,
  },
  aiGeneratedCardTitle: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  aiGeneratedInfo: {
    gap: 6,
  },
  aiGeneratedCourse: {
    fontSize: 12,
    fontWeight: '500',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  aiGeneratedTimes: {
    flexDirection: 'row',
    gap: 12,
  },
  aiGeneratedTime: {
    fontSize: 12,
    opacity: 0.8,
  },
  noAiMealsContainer: {
    alignItems: 'center',
    padding: 24,
    borderRadius: 8,
    borderWidth: 1,
  },
  noAiMealsText: {
    fontSize: 16,
    fontWeight: '600',
    marginTop: 12,
    marginBottom: 8,
    textAlign: 'center',
  },
  noAiMealsSubtext: {
    fontSize: 14,
    opacity: 0.7,
    textAlign: 'center',
    lineHeight: 20,
  },
  // Weekly Meals Styles
  weeklyMealsSection: {
    marginBottom: 15,
    paddingHorizontal: 0, // Remove horizontal padding to allow full-width scrolling
  },
  weeklyMealsSectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginHorizontal: 24,
    marginBottom: 16,
  },
  weeklyMealsScroll: {
    paddingLeft: 24,
  },
  weeklyMealsScrollContent: {
    paddingRight: 24,
    paddingBottom: 20, // Add bottom padding to prevent cutoff
  },
  weeklyMealCard: {
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 4,
    marginRight: 16,
    minHeight: 280, // Further reduced since we removed buttons and badges
  },
  weeklyMealImageContainer: {
    position: 'relative',
    width: '100%',
    height: 200,
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
    overflow: 'hidden',
  },
  weeklyMealImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  weeklyMealCardContent: {
    padding: 12,
    paddingBottom: 8,
    flex: 1,
    flexDirection: 'column',
  },
  weeklyMealName: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 8,
    lineHeight: 22,
  },
  todayLabel: {
    fontWeight: '800',
  },
  bottomButtonsContainer: {
    flexDirection: 'row',
    gap: 8,
    marginTop: 'auto', // Push buttons to bottom of card
    paddingTop: 16,
  },
  bottomButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    gap: 6,
  },
  recipeButton: {
    backgroundColor: 'transparent',
  },
  swapButton: {
    backgroundColor: 'transparent',
  },
  bottomButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  pastMealsButtonContainer: {
    marginHorizontal: 24,
    marginTop: 0,
    marginBottom: 40,
  },
  pastMealsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 8,
    gap: 8,
  },
  pastMealsButtonText: {
    fontSize: 16,
    fontWeight: '500',
    flex: 1,
    textAlign: 'center',
  },
  equipmentContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 4,
  },
  equipmentItem: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#F5F5F5',
    borderWidth: 1,
    borderColor: '#E0E0E0',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 4,
  },
  equipmentIcon: {
    width: 26,
    height: 26,
  },
  // Calendar Slider Styles
  calendarContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: 24,
    marginBottom: 20,
    paddingHorizontal: 4,
  },
  calendarDay: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 6,
    borderRadius: 8,
    flex: 1,
    position: 'relative',
    marginHorizontal: 1,
  },
  calendarDayName: {
    fontSize: 10,
    fontWeight: '500',
    marginBottom: 2,
    textTransform: 'uppercase',
    letterSpacing: 0.3,
  },
  calendarDateContainer: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
  },
  calendarSelectedCircle: {
    position: 'absolute',
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#fff',
    zIndex: -1,
  },
  calendarDayDate: {
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
    minWidth: 20,
  },
  calendarDisabledIndicator: {
    position: 'absolute',
    top: 2,
    right: 2,
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#FF6B6B',
    zIndex: 1,
  },
  swapOverlayButton: {
    position: 'absolute',
    top: 0,
    left: 0,
  },
  mealInfoContainer: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 6,
  },
  mealInfoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 3,
  },
  mealInfoText: {
    fontSize: 11,
    fontWeight: '500',
    opacity: 0.8,
  },
});
