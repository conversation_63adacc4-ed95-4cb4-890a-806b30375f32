import { CustomAlert } from '@/components/CustomAlert';
import { EmojiPicker } from '@/components/EmojiPicker';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { useAuth } from '@/contexts/AuthContext';
import { useTheme } from '@/contexts/ThemeContext';
import type { Database } from '@/lib/database.types';
import { supabase } from '@/lib/supabase';
import {
  callOpenAI,
  createUserVisionMessage,
  OpenAIProxyError,
} from '@/lib/openaiProxy';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { router, useLocalSearchParams } from 'expo-router';
import React, { useCallback, useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  Animated,
  FlatList,
  Modal,
  Pressable,
  ScrollView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import { Image } from 'expo-image';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useTabBarHeight } from '@/hooks/useTabBarHeight';
import { useStaggeredAnimation } from '@/hooks/useStaggeredAnimation';

type UserRecipe = Database['public']['Tables']['user_meals']['Row'];

type NewRecipe = {
  name: string;
  description: string;
  ingredients: {
    name: string;
    amount: string;
    unit: string;
    emoji: string;
  }[];
  instructions: string[];
  prepTimeHour: number;
  prepTimeMin: number;
  cookTimeHour: number;
  cookTimeMin: number;
  servingSize: number;
  course: string;
  cuisine_type: string;
  dietary_tags: string[];
  allergen_contains: string[];
  calories: number;
  protein: number;
  carbs: number;
  fats: number;
  image: string | null;
};

function MyRecipesScreen() {
  const { activeTheme } = useTheme();
  const colors = Colors[activeTheme];
  const styles = createStyles(colors);
  const insets = useSafeAreaInsets();
  const { session } = useAuth();
  const { contentBottomPadding } = useTabBarHeight();
  const params = useLocalSearchParams();

  const [recipes, setRecipes] = useState<UserRecipe[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddModal, setShowAddModal] = useState(false);
  const [saving, setSaving] = useState(false);
  const [showAlert, setShowAlert] = useState(false);
  const [alertConfig, setAlertConfig] = useState({ title: '', message: '' });
  const [analyzing, setAnalyzing] = useState(false);

  // Use staggered animation hook for 2 sections: title, content
  const { sections: animatedSections } = useStaggeredAnimation(2);
  const [titleSection, contentSection] = animatedSections;
  const [analysisProgress, setAnalysisProgress] = useState('');
  const [editingRecipeId, setEditingRecipeId] = useState<string | null>(null);
  const [aiUsageToday, setAiUsageToday] = useState(0);
  const [loadingUsage, setLoadingUsage] = useState(false);

  // Dropdown state management
  const [editingField, setEditingField] = useState<string | null>(null);
  const [tempValue, setTempValue] = useState<any>(null);

  // New recipe form state
  const [newRecipe, setNewRecipe] = useState<NewRecipe>({
    name: '',
    description: '',
    ingredients: [{ name: '', amount: '', unit: '', emoji: '🥗' }],
    instructions: [''],
    prepTimeHour: 0,
    prepTimeMin: 0,
    cookTimeHour: 0,
    cookTimeMin: 0,
    servingSize: 4,
    course: 'Main Course',
    cuisine_type: 'American',
    dietary_tags: [],
    allergen_contains: [],
    calories: 0,
    protein: 0,
    carbs: 0,
    fats: 0,
    image: null,
  });

  const loadMyRecipes = useCallback(async () => {
    if (!session?.user?.id) return;

    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('user_meals')
        .select('*')
        .eq('user_id', session.user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setRecipes(data || []);
    } catch {
      setAlertConfig({
        title: 'Error',
        message: 'Failed to load your recipes',
      });
      setShowAlert(true);
    } finally {
      setLoading(false);
    }
  }, [session?.user?.id]);

  const loadAiUsageToday = useCallback(async () => {
    if (!session?.user?.id) return;

    try {
      setLoadingUsage(true);
      const { data, error } = await supabase.rpc('get_ai_usage_today', {
        p_user_id: session.user.id,
        p_feature_type: 'recipe_analysis',
      });

      if (error) throw error;
      setAiUsageToday(data || 0);
    } catch {
    } finally {
      setLoadingUsage(false);
    }
  }, [session?.user?.id]);

  useEffect(() => {
    loadMyRecipes();
    loadAiUsageToday();
  }, [loadMyRecipes, loadAiUsageToday]);

  // Handle edit mode when navigating from user-meal-detail
  useEffect(() => {
    if (params.editMode === 'true' && params.editData) {
      try {
        const editData = JSON.parse(params.editData as string);
        setEditingRecipeId(editData.id);

        // Parse ingredients from database format
        const parsedIngredients = editData.ingredients
          ? Array.isArray(editData.ingredients)
            ? editData.ingredients.map((ing: any) => ({
                name: ing.name || '',
                amount: ing.amount || '',
                unit: ing.unit || '',
                emoji: ing.emoji || '🥗',
              }))
            : [{ name: '', amount: '', unit: '', emoji: '🥗' }]
          : [{ name: '', amount: '', unit: '', emoji: '🥗' }];

        // Parse instructions from database format
        const parsedInstructions = editData.instructions
          ? Array.isArray(editData.instructions)
            ? editData.instructions.filter(Boolean).map(String)
            : typeof editData.instructions === 'string'
              ? editData.instructions.split('\n').filter(Boolean)
              : ['']
          : [''];

        setNewRecipe({
          name: editData.name || '',
          description: editData.description || '',
          ingredients: parsedIngredients,
          instructions: parsedInstructions,
          prepTimeHour: editData.prep_time_hour || 0,
          prepTimeMin: editData.prep_time_min || 0,
          cookTimeHour: editData.cook_time_hour || 0,
          cookTimeMin: editData.cook_time_min || 0,
          servingSize: editData.serving_size || 4,
          course: editData.course || 'Main Course',
          cuisine_type: editData.cuisine_type || 'American',
          dietary_tags: editData.dietary_tags || [],
          allergen_contains: editData.allergen_contains || [],
          calories: editData.calories || 0,
          protein: editData.protein || 0,
          carbs: editData.carbs || 0,
          fats: editData.fats || 0,
          image: editData.image ? getImageDisplayUrl(editData.image) : null,
        });
        setShowAddModal(true);
      } catch {}
    }
  }, [params.editMode, params.editData]);

  // Helper function to convert database image path to display URL
  const getImageDisplayUrl = (imagePath: string | null): string | null => {
    if (!imagePath || imagePath === 'default.jpg') {
      return null;
    }

    if (imagePath.startsWith('http') || imagePath.startsWith('file://')) {
      return imagePath;
    }

    // Convert database path to Supabase Storage URL
    return `${process.env.EXPO_PUBLIC_SUPABASE_URL}/storage/v1/object/public/meals/${imagePath}`;
  };

  const resetForm = () => {
    setNewRecipe({
      name: '',
      description: '',
      ingredients: [{ name: '', amount: '', unit: '', emoji: '🥗' }],
      instructions: [''],
      prepTimeHour: 0,
      prepTimeMin: 15,
      cookTimeHour: 0,
      cookTimeMin: 30,
      servingSize: 4,
      course: 'Main Course',
      cuisine_type: 'American',
      dietary_tags: [],
      allergen_contains: [],
      calories: 0,
      protein: 0,
      carbs: 0,
      fats: 0,
      image: null,
    });
    setEditingRecipeId(null);
    setEditingField(null);
    setTempValue(null);
  };

  // Dropdown field component similar to profile page
  const DropdownField = ({
    label,
    value,
    fieldName,
    options = [],
    type = 'select',
  }: {
    label: string;
    value: string | string[];
    fieldName: string;
    options: (string | { label: string; value: string })[];
    type?: 'select' | 'multiselect';
  }) => {
    const isEditing = editingField === fieldName;

    const handleFieldPress = () => {
      if (!isEditing) {
        setEditingField(fieldName);
        setTempValue(value);
      }
    };

    const handleSave = () => {
      setNewRecipe((prev) => ({
        ...prev,
        [fieldName]: tempValue,
      }));
      setEditingField(null);
      setTempValue(null);
    };

    const handleCancel = () => {
      setEditingField(null);
      setTempValue(null);
    };

    return (
      <View style={styles.inputGroup}>
        <ThemedText style={styles.inputLabel}>{label}</ThemedText>

        {isEditing ? (
          <View style={styles.dropdownEditContainer}>
            {type === 'select' && (
              <View style={styles.selectContainer}>
                {options.map((option) => {
                  const optionValue =
                    typeof option === 'string' ? option : option.value;
                  const optionLabel =
                    typeof option === 'string' ? option : option.label;
                  return (
                    <TouchableOpacity
                      key={optionValue}
                      style={[
                        styles.selectOption,
                        {
                          backgroundColor: colors.cardBackground,
                          borderColor: colors.border,
                        },
                        tempValue === optionValue && [
                          styles.selectOptionActive,
                          {
                            backgroundColor: colors.primary,
                            borderColor: colors.primary,
                          },
                        ],
                      ]}
                      onPress={() => setTempValue(optionValue)}
                    >
                      <ThemedText
                        style={[
                          styles.selectOptionText,
                          {
                            color:
                              tempValue === optionValue
                                ? '#000000'
                                : colors.text,
                          },
                          tempValue === optionValue &&
                            styles.selectOptionTextActive,
                        ]}
                      >
                        {optionLabel}
                      </ThemedText>
                    </TouchableOpacity>
                  );
                })}
              </View>
            )}

            {type === 'multiselect' && (
              <View style={styles.multiselectContainer}>
                {options.map((option) => {
                  const optionValue =
                    typeof option === 'string' ? option : option.value;
                  const optionLabel =
                    typeof option === 'string' ? option : option.label;
                  const isSelected = tempValue?.includes(optionValue);
                  return (
                    <TouchableOpacity
                      key={optionValue}
                      style={[
                        styles.multiselectOption,
                        {
                          backgroundColor: colors.cardBackground,
                          borderColor: colors.border,
                        },
                        isSelected && [
                          styles.multiselectOptionActive,
                          {
                            backgroundColor: colors.primary,
                            borderColor: colors.primary,
                          },
                        ],
                      ]}
                      onPress={() => {
                        const currentArray = tempValue || [];
                        if (isSelected) {
                          setTempValue(
                            currentArray.filter(
                              (item: string) => item !== optionValue
                            )
                          );
                        } else {
                          setTempValue([...currentArray, optionValue]);
                        }
                      }}
                    >
                      <ThemedText
                        style={[
                          styles.multiselectOptionText,
                          { color: isSelected ? '#000000' : colors.text },
                          isSelected && styles.multiselectOptionTextActive,
                        ]}
                      >
                        {optionLabel}
                      </ThemedText>
                    </TouchableOpacity>
                  );
                })}
              </View>
            )}

            <View style={styles.dropdownActions}>
              <TouchableOpacity
                style={[
                  styles.cancelButton,
                  {
                    backgroundColor: colors.cardBackground,
                    borderColor: colors.border,
                  },
                ]}
                onPress={handleCancel}
              >
                <ThemedText
                  style={[styles.cancelButtonText, { color: colors.text }]}
                >
                  Cancel
                </ThemedText>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.saveButton, { borderColor: colors.primary }]}
                onPress={handleSave}
              >
                <ThemedText style={[styles.saveButtonText, { color: colors.primary }]}>Save</ThemedText>
              </TouchableOpacity>
            </View>
          </View>
        ) : (
          <TouchableOpacity
            style={[
              styles.dropdownButton,
              {
                borderColor: colors.border,
                backgroundColor: colors.background,
              },
            ]}
            onPress={handleFieldPress}
          >
            <ThemedText style={[styles.dropdownText, { color: colors.text }]}>
              {Array.isArray(value)
                ? value.length > 0
                  ? value.join(', ')
                  : 'None selected'
                : value}
            </ThemedText>
            <Ionicons name="chevron-down" size={16} color={colors.text} />
          </TouchableOpacity>
        )}
      </View>
    );
  };

  const handleAddRecipe = () => {
    setShowAddModal(true);
  };

  const uploadImage = async (imageUri: string): Promise<string | null> => {
    try {
      // Create a unique filename
      const fileExt = imageUri.split('.').pop()?.toLowerCase() || 'jpg';
      const fileName = `user_recipe_${Date.now()}_${Math.random()
        .toString(36)
        .substring(2)}.${fileExt}`;

      // For Expo Go, we need to fetch the image as a blob first
      const response = await fetch(imageUri);
      const blob = await response.blob();

      // Convert blob to base64
      const reader = new FileReader();
      const base64Promise = new Promise<string>((resolve, reject) => {
        reader.onloadend = () => {
          if (reader.result && typeof reader.result === 'string') {
            // Extract base64 string without the data URL prefix
            const base64 = reader.result.split(',')[1];
            resolve(base64);
          } else {
            reject(new Error('Failed to convert to base64'));
          }
        };
        reader.onerror = reject;
      });

      reader.readAsDataURL(blob);
      const base64Data = await base64Promise;

      // Decode base64 to binary for Supabase
      const binaryString = atob(base64Data);
      const bytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }

      // Upload using Supabase client with the binary data
      const { error } = await supabase.storage
        .from('meals')
        .upload(fileName, bytes.buffer, {
          contentType: `image/${fileExt}`,
          upsert: false,
        });

      if (error) {
        throw error;
      }

      return fileName;
    } catch {
      setAlertConfig({
        title: 'Image Upload Error',
        message: 'Failed to upload image. Recipe will be saved without image.',
      });
      setShowAlert(true);
      return null;
    }
  };

  const handleSaveRecipe = async () => {
    if (!session?.user?.id) {
      setAlertConfig({
        title: 'Error',
        message: 'You must be signed in to save recipes',
      });
      setShowAlert(true);
      return;
    }

    if (!newRecipe.name.trim()) {
      setAlertConfig({ title: 'Error', message: 'Recipe name is required' });
      setShowAlert(true);
      return;
    }

    if (
      newRecipe.ingredients.length === 0 ||
      !newRecipe.ingredients[0].name.trim()
    ) {
      setAlertConfig({
        title: 'Error',
        message: 'At least one ingredient is required',
      });
      setShowAlert(true);
      return;
    }

    if (
      newRecipe.instructions.length === 0 ||
      !newRecipe.instructions[0].trim()
    ) {
      setAlertConfig({
        title: 'Error',
        message: 'At least one instruction is required',
      });
      setShowAlert(true);
      return;
    }

    try {
      setSaving(true);

      // Handle image - upload new one or preserve existing
      let imagePath = null;
      if (newRecipe.image) {
        if (newRecipe.image.startsWith('file://')) {
          // New image selected - upload it
          imagePath = await uploadImage(newRecipe.image);
        } else if (
          newRecipe.image.includes('/storage/v1/object/public/meals/')
        ) {
          // Existing image from Supabase - extract just the filename
          const urlParts = newRecipe.image.split('/meals/');
          imagePath = urlParts.length > 1 ? urlParts[1] : null;
        } else if (!newRecipe.image.startsWith('http')) {
          // Already a filename/path, use as-is
          imagePath = newRecipe.image;
        }
      }

      // Helper function to safely convert to integer or null
      const safeInt = (value: any): number | null => {
        if (value === null || value === undefined || value === '') return null;
        const parsed = parseInt(String(value));
        return isNaN(parsed) ? null : parsed;
      };

      const recipeData = {
        name: newRecipe.name.trim(),
        description: newRecipe.description.trim() || null,
        ingredients: newRecipe.ingredients.filter((ing) => ing.name.trim()),
        instructions: newRecipe.instructions.filter((inst) => inst.trim()),
        prep_time_hour: safeInt(newRecipe.prepTimeHour),
        prep_time_min: safeInt(newRecipe.prepTimeMin),
        cook_time_hour: safeInt(newRecipe.cookTimeHour),
        cook_time_min: safeInt(newRecipe.cookTimeMin),
        serving_size: safeInt(newRecipe.servingSize) || 1,
        course: newRecipe.course,
        cuisine_type: newRecipe.cuisine_type,
        dietary_tags:
          newRecipe.dietary_tags.length > 0 ? newRecipe.dietary_tags : null,
        allergen_contains:
          newRecipe.allergen_contains.length > 0
            ? newRecipe.allergen_contains
            : null,
        calories: safeInt(newRecipe.calories),
        protein: safeInt(newRecipe.protein),
        carbs: safeInt(newRecipe.carbs),
        fats: safeInt(newRecipe.fats),
        image: imagePath,
        user_id: session.user.id,
      };

      if (editingRecipeId) {
        // Update existing recipe
        const { error } = await supabase
          .from('user_meals')
          .update(recipeData)
          .eq('id', editingRecipeId)
          .eq('user_id', session.user.id);

        if (error) throw error;

        setAlertConfig({
          title: 'Success',
          message: 'Recipe updated successfully!',
        });
      } else {
        // Create new recipe
        const { error } = await supabase.from('user_meals').insert(recipeData);

        if (error) throw error;

        setAlertConfig({
          title: 'Success',
          message: 'Recipe added successfully!',
        });
      }

      setShowAlert(true);
      setShowAddModal(false);
      resetForm();
      setEditingRecipeId(null);
      loadMyRecipes();
    } catch {
      setAlertConfig({ title: 'Error', message: 'Failed to save recipe' });
      setShowAlert(true);
    } finally {
      setSaving(false);
    }
  };

  const addIngredient = () => {
    setNewRecipe((prev) => ({
      ...prev,
      ingredients: [
        ...prev.ingredients,
        { name: '', amount: '', unit: '', emoji: '🥗' },
      ],
    }));
  };

  const removeIngredient = (index: number) => {
    setNewRecipe((prev) => ({
      ...prev,
      ingredients: prev.ingredients.filter((_, i) => i !== index),
    }));
  };

  const updateIngredient = (index: number, field: string, value: string) => {
    setNewRecipe((prev) => ({
      ...prev,
      ingredients: prev.ingredients.map((ing, i) => {
        if (i === index) {
          const updatedIng = { ...ing, [field]: value };
          // Auto-update emoji when ingredient name changes (and it's currently the default)
          if (
            field === 'name' &&
            value.trim() &&
            (ing.emoji === '🥗' || !ing.emoji)
          ) {
            updatedIng.emoji = getDefaultEmoji(value);
          }
          return updatedIng;
        }
        return ing;
      }),
    }));
  };

  const addInstruction = () => {
    setNewRecipe((prev) => ({
      ...prev,
      instructions: [...prev.instructions, ''],
    }));
  };

  const removeInstruction = (index: number) => {
    setNewRecipe((prev) => ({
      ...prev,
      instructions: prev.instructions.filter((_, i) => i !== index),
    }));
  };

  const updateInstruction = (index: number, value: string) => {
    setNewRecipe((prev) => ({
      ...prev,
      instructions: prev.instructions.map((inst, i) =>
        i === index ? value : inst
      ),
    }));
  };

  const pickImage = async () => {
    // Request permission
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      setAlertConfig({
        title: 'Permission Required',
        message: 'Sorry, we need camera roll permissions to select photos.',
      });
      setShowAlert(true);
      return;
    }

    // Present options to user
    Alert.alert('Add Photo', 'Choose how you want to add a photo', [
      { text: 'Cancel', style: 'cancel' },
      { text: 'Camera', onPress: () => openCamera() },
      { text: 'Photo Library', onPress: () => openImagePicker() },
    ]);
  };

  const openCamera = async () => {
    const { status } = await ImagePicker.requestCameraPermissionsAsync();
    if (status !== 'granted') {
      setAlertConfig({
        title: 'Permission Required',
        message: 'Sorry, we need camera permissions to take photos.',
      });
      setShowAlert(true);
      return;
    }

    const result = await ImagePicker.launchCameraAsync({
      mediaTypes: ['images'],
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.8,
    });

    if (!result.canceled && result.assets[0]) {
      setNewRecipe((prev) => ({ ...prev, image: result.assets[0].uri }));
    }
  };

  const openImagePicker = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ['images'],
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.8,
    });

    if (!result.canceled && result.assets[0]) {
      setNewRecipe((prev) => ({ ...prev, image: result.assets[0].uri }));
    }
  };

  const removeImage = () => {
    setNewRecipe((prev) => ({ ...prev, image: null }));
  };

  // Function to get default emoji based on ingredient name
  const getDefaultEmoji = (ingredientName: string): string => {
    const name = ingredientName.toLowerCase().trim();

    // Meat & Protein
    if (name.includes('chicken') || name.includes('poultry')) return '🍗';
    if (name.includes('beef') || name.includes('steak')) return '🥩';
    if (name.includes('pork') || name.includes('bacon') || name.includes('ham'))
      return '🥓';
    if (
      name.includes('fish') ||
      name.includes('salmon') ||
      name.includes('tuna')
    )
      return '🐟';
    if (name.includes('shrimp') || name.includes('prawn')) return '🦐';
    if (name.includes('egg')) return '🥚';

    // Vegetables
    if (name.includes('tomato')) return '🍅';
    if (name.includes('carrot')) return '🥕';
    if (name.includes('onion')) return '🧅';
    if (name.includes('garlic')) return '🧄';
    if (name.includes('potato')) return '🥔';
    if (name.includes('pepper') || name.includes('bell pepper')) return '🫑';
    if (name.includes('broccoli')) return '🥦';
    if (name.includes('corn')) return '🌽';
    if (name.includes('mushroom')) return '🍄';
    if (name.includes('avocado')) return '🥑';
    if (name.includes('cucumber')) return '🥒';
    if (
      name.includes('lettuce') ||
      name.includes('spinach') ||
      name.includes('greens')
    )
      return '🥬';

    // Fruits
    if (name.includes('apple')) return '🍎';
    if (name.includes('banana')) return '🍌';
    if (name.includes('orange')) return '🍊';
    if (name.includes('lemon')) return '🍋';
    if (name.includes('strawberry')) return '🍓';
    if (name.includes('blueberry')) return '🫐';
    if (name.includes('grape')) return '🍇';

    // Dairy
    if (name.includes('milk')) return '🥛';
    if (name.includes('cheese')) return '🧀';
    if (name.includes('butter')) return '🧈';
    if (name.includes('yogurt') || name.includes('cream')) return '🥛';

    // Grains & Carbs
    if (name.includes('bread') || name.includes('loaf')) return '🍞';
    if (name.includes('rice')) return '🍚';
    if (name.includes('pasta') || name.includes('noodle')) return '🍝';
    if (name.includes('flour') || name.includes('wheat')) return '🌾';
    if (name.includes('oat')) return '🌾';

    // Herbs & Seasonings
    if (
      name.includes('herb') ||
      name.includes('basil') ||
      name.includes('oregano') ||
      name.includes('thyme')
    )
      return '🌿';
    if (name.includes('salt')) return '🧂';
    if (name.includes('pepper') && name.includes('black')) return '🌶️';
    if (name.includes('honey')) return '🍯';

    // Nuts & Seeds
    if (
      name.includes('nut') ||
      name.includes('almond') ||
      name.includes('walnut')
    )
      return '🥜';
    if (name.includes('coconut')) return '🥥';

    // Oils & Liquids
    if (name.includes('oil') || name.includes('olive')) return '🫒';
    if (name.includes('water')) return '💧';
    if (name.includes('wine')) return '🍷';

    // Default fallback
    return '🥗';
  };

  const takeRecipePhoto = async () => {
    try {
      // Request camera permission
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        setAlertConfig({
          title: 'Permission Required',
          message: 'Camera access is needed to take a photo of the recipe.',
        });
        setShowAlert(true);
        return;
      }

      // Present options to user
      Alert.alert(
        'Take Recipe Photo',
        'Take a photo of a recipe card, cookbook page, or printed recipe',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Camera', onPress: () => takeRecipePhotoFromCamera() },
          { text: 'Photo Library', onPress: () => selectRecipeFromLibrary() },
        ]
      );
    } catch {
      setAlertConfig({
        title: 'Error',
        message: 'Failed to access camera. Please try again.',
      });
      setShowAlert(true);
    }
  };

  const takeRecipePhotoFromCamera = async () => {
    try {
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ['images'],
        allowsEditing: false, // Remove cropping to capture full recipe
        quality: 0.9, // Higher quality for better text recognition
      });

      if (!result.canceled && result.assets[0]) {
        await analyzeRecipeImage(result.assets[0].uri);
      }
    } catch {
      setAlertConfig({
        title: 'Error',
        message: 'Failed to take photo. Please try again.',
      });
      setShowAlert(true);
    }
  };

  const selectRecipeFromLibrary = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['images'],
        allowsEditing: false, // Remove cropping to capture full recipe
        quality: 0.9, // Higher quality for better text recognition
      });

      if (!result.canceled && result.assets[0]) {
        await analyzeRecipeImage(result.assets[0].uri);
      }
    } catch {
      setAlertConfig({
        title: 'Error',
        message: 'Failed to select photo. Please try again.',
      });
      setShowAlert(true);
    }
  };

  const analyzeRecipeImage = async (imageUri: string) => {
    if (!imageUri) return;

    // Check usage limit
    if (aiUsageToday >= 5) {
      setAlertConfig({
        title: 'Daily Limit Reached',
        message:
          "You've reached your daily limit of 5 AI recipe analyses. Try again tomorrow!",
      });
      setShowAlert(true);
      return;
    }

    try {
      setAnalyzing(true);
      setAnalysisProgress('Preparing image...');

      // Convert image to base64
      const response = await fetch(imageUri);
      const blob = await response.blob();

      const reader = new FileReader();
      const base64Promise = new Promise<string>((resolve, reject) => {
        reader.onloadend = () => {
          if (reader.result && typeof reader.result === 'string') {
            // Keep the full data URL format for OpenAI
            resolve(reader.result);
          } else {
            reject(new Error('Failed to convert to base64'));
          }
        };
        reader.onerror = reject;
      });

      reader.readAsDataURL(blob);
      const base64Image = await base64Promise;

      setAnalysisProgress('Analyzing recipe with AI...');

      // Create vision message for the OpenAI proxy
      const visionMessage = createUserVisionMessage(
        `You are an expert recipe extraction assistant. Carefully analyze this recipe image and extract ONLY what you can clearly read. Pay special attention to precise ingredient amounts, units, and step-by-step instructions.

Return valid JSON with this exact structure:

{
  "name": "Recipe Name Here",
  "description": "Brief description if visible",
  "ingredients": [
    {"name": "sweet potato", "amount": "3", "unit": "oz", "emoji": "🍠"},
    {"name": "egg whites", "amount": "2", "unit": "large", "emoji": "🥚"}
  ],
  "instructions": ["Step 1 exactly as written", "Step 2 exactly as written"],
  "prepTimeMin": null,
  "cookTimeMin": null,
  "servingSize": 1,
  "cuisine_type": "",
  "calories": null,
  "protein": null,
  "carbs": null,
  "fats": null
}

INGREDIENT EXTRACTION RULES:
- Extract measurements EXACTLY as written (e.g. "3 oz. grated sweet potato" → amount: "3", unit: "oz", name: "grated sweet potato")
- For fractional amounts, preserve fractions (e.g. "¼" → "1/4", "½" → "1/2")
- Standardize units using these forms:
  * tablespoon/tablespoons/tbsp/T/Tbs → "tbs"
  * teaspoon/teaspoons/tsp/t → "tsp"
  * cup/cups/c → "cup"
  * ounce/ounces/oz → "oz"
  * pound/pounds/lb/lbs → "lb"
  * gram/grams/g → "g"
  * large/medium/small (for eggs) → "large"/"medium"/"small"
  * can/jar/package/bottle → "can"/"jar"/"pkg"/"bottle"
  * slice/slices → "slice"
  * clove/cloves → "cloves"
  * dash/pinch → "dash"/"pinch"
- If no unit is specified, leave unit as empty string ""
- Include descriptive words in the ingredient name (e.g. "grated sweet potato", "liquid egg whites", "chopped baby kale")

NUTRITION EXTRACTION:
- Look for patterns like "345 calories / 13F / 32C / 25P" or "C: 32, P: 25, F: 13"
- Extract ONLY the numbers:
  * calories → extract the calorie number
  * carbs/C → extract carb number
  * protein/P → extract protein number  
  * fats/F → extract fat number
- If format is "345 calories / 13F / 32C / 25P", parse as: calories=345, fats=13, carbs=32, protein=25

INSTRUCTION EXTRACTION:
- Copy each numbered step EXACTLY as written, preserving all details
- Do NOT paraphrase, summarize, or rewrite
- Maintain original capitalization and punctuation
- Include specific temperatures, times, and techniques as written
- If steps are numbered, extract them in order

SERVING SIZE:
- Look for "Makes X servings" or "Serves X" at the top
- Extract only the number

STRICT REQUIREMENTS:
- Return ONLY valid JSON, no markdown formatting, no explanations
- If you cannot clearly read a value, use null for numbers or empty string "" for text
- Do not guess or estimate any values
- Preserve exact spelling and formatting from the original recipe`,
        base64Image,
        'high'
      );

      // Call OpenAI through secure proxy
      const openAIResponse = await callOpenAI({
        messages: [visionMessage],
        model: 'gpt-4o-mini',
        max_tokens: 2000,
        temperature: 0.1,
      });

      const aiResponse = openAIResponse.choices[0]?.message?.content;

      if (!aiResponse) {
        throw new Error('No response from AI');
      }

      setAnalysisProgress('Processing recipe data...');

      // Parse the JSON response with improved error handling
      let recipeData: any;
      try {
        // Try to clean the response first
        let cleanedResponse = aiResponse.trim();

        // Remove any markdown code blocks if present
        if (cleanedResponse.startsWith('```json')) {
          cleanedResponse = cleanedResponse
            .replace(/^```json\s*/i, '')
            .replace(/\s*```$/, '');
        } else if (cleanedResponse.startsWith('```')) {
          cleanedResponse = cleanedResponse
            .replace(/^```\s*/, '')
            .replace(/\s*```$/, '');
        }

        // Remove any text before the first { or after the last }
        const firstBrace = cleanedResponse.indexOf('{');
        const lastBrace = cleanedResponse.lastIndexOf('}');

        if (firstBrace !== -1 && lastBrace !== -1) {
          cleanedResponse = cleanedResponse.substring(
            firstBrace,
            lastBrace + 1
          );
        }

        recipeData = JSON.parse(cleanedResponse);

        // Helper function to safely parse nutritional values
        const parseNutritionValue = (value: any): number => {
          if (value === null || value === undefined || value === '') return 0;
          if (typeof value === 'number') return value;
          if (typeof value === 'string') {
            // Extract numbers from strings like "25g", "34C", "11F", "26P"
            const numMatch = value.match(/(\d+(?:\.\d+)?)/);
            return numMatch ? parseFloat(numMatch[1]) : 0;
          }
          return 0;
        };

        // Validate and count extracted data
        const extractedCount = {
          name: recipeData.name?.trim() ? 1 : 0,
          ingredients:
            recipeData.ingredients?.length > 0
              ? recipeData.ingredients.length
              : 0,
          instructions:
            recipeData.instructions?.length > 0
              ? recipeData.instructions.filter((inst: any) => inst?.trim())
                  .length
              : 0,
          nutrition: [
            recipeData.calories,
            recipeData.protein,
            recipeData.carbs,
            recipeData.fats,
          ].filter((val) => val !== null && val !== undefined).length,
        };

        // Update the form with extracted data - only fill fields that have actual values
        setNewRecipe((prev) => ({
          ...prev,
          name:
            recipeData.name && recipeData.name.trim()
              ? recipeData.name.trim()
              : prev.name,
          description:
            recipeData.description && recipeData.description.trim()
              ? recipeData.description.trim()
              : prev.description,
          ingredients:
            recipeData.ingredients?.length > 0
              ? recipeData.ingredients
                  .map((ing: any) => ({
                    name: ing.name || '',
                    amount: ing.amount || '',
                    unit: ing.unit || '',
                    emoji: ing.emoji || getDefaultEmoji(ing.name || ''),
                  }))
                  .filter((ing: any) => ing.name.trim()) // Remove empty ingredients
              : prev.ingredients,
          instructions:
            recipeData.instructions?.length > 0
              ? recipeData.instructions.filter(
                  (inst: any) => inst && inst.trim()
                )
              : prev.instructions,
          prepTimeHour: recipeData.prepTimeMin
            ? Math.floor(recipeData.prepTimeMin / 60)
            : prev.prepTimeHour,
          prepTimeMin: recipeData.prepTimeMin
            ? recipeData.prepTimeMin % 60
            : prev.prepTimeMin,
          cookTimeHour: recipeData.cookTimeMin
            ? Math.floor(recipeData.cookTimeMin / 60)
            : prev.cookTimeHour,
          cookTimeMin: recipeData.cookTimeMin
            ? recipeData.cookTimeMin % 60
            : prev.cookTimeMin,
          servingSize: recipeData.servingSize || prev.servingSize,
          cuisine_type:
            recipeData.cuisine_type && recipeData.cuisine_type.trim()
              ? recipeData.cuisine_type.trim()
              : prev.cuisine_type,
          calories: recipeData.calories
            ? parseNutritionValue(recipeData.calories)
            : prev.calories,
          protein: recipeData.protein
            ? parseNutritionValue(recipeData.protein)
            : prev.protein,
          carbs: recipeData.carbs
            ? parseNutritionValue(recipeData.carbs)
            : prev.carbs,
          fats: recipeData.fats
            ? parseNutritionValue(recipeData.fats)
            : prev.fats,
        }));

        // Provide detailed feedback about what was extracted
        const successParts = [];
        if (extractedCount.name) successParts.push('recipe name');
        if (extractedCount.ingredients)
          successParts.push(`${extractedCount.ingredients} ingredients`);
        if (extractedCount.instructions)
          successParts.push(`${extractedCount.instructions} instruction steps`);
        if (extractedCount.nutrition)
          successParts.push(`${extractedCount.nutrition} nutrition values`);

        const successMessage =
          successParts.length > 0
            ? `Successfully extracted: ${successParts.join(', ')}. Please review all fields carefully and fill in any missing details.`
            : 'AI analysis completed, but some recipe details may need manual entry. Please review all fields carefully.';

        // Increment AI usage count
        try {
          const { data, error } = await supabase.rpc('increment_ai_usage', {
            p_user_id: session?.user?.id || '',
            p_feature_type: 'recipe_analysis',
          });
          if (!error && data) {
            setAiUsageToday(data);
          }
        } catch {}

        setAlertConfig({
          title: 'Recipe Analyzed!',
          message: successMessage,
        });
        setShowAlert(true);
      } catch {
        // Show the user what went wrong for debugging
        setAlertConfig({
          title: 'Analysis Error',
          message: `Failed to parse recipe data. The AI response was: "${aiResponse.substring(
            0,
            200
          )}..."`,
        });
        setShowAlert(true);
        return;
      }
    } catch (error) {
      if (error instanceof OpenAIProxyError) {
        setAlertConfig({
          title: 'Analysis Failed',
          message: error.message,
        });
      } else {
        setAlertConfig({
          title: 'Analysis Failed',
          message:
            'Unable to analyze the recipe image. Please try again or enter the details manually.',
        });
      }
      setShowAlert(true);
    } finally {
      setAnalyzing(false);
      setAnalysisProgress('');
    }
  };

  const deleteRecipe = async (recipeId: string) => {
    Alert.alert(
      'Delete Recipe',
      'Are you sure you want to delete this recipe? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              const { error } = await supabase
                .from('user_meals')
                .delete()
                .eq('id', recipeId);

              if (error) throw error;

              setAlertConfig({
                title: 'Success',
                message: 'Recipe deleted successfully!',
              });
              setShowAlert(true);
              loadMyRecipes();
            } catch {
              setAlertConfig({
                title: 'Error',
                message: 'Failed to delete recipe',
              });
              setShowAlert(true);
            }
          },
        },
      ]
    );
  };

  const formatTime = (hours: number | null, minutes: number | null) => {
    if (!hours && !minutes) return '0 min';
    const h = hours || 0;
    const m = minutes || 0;
    return h > 0 ? `${h}h ${m}m` : `${m} min`;
  };

  const getImageSource = (image: string | null) => {
    if (!image || image === 'default.jpg') {
      return require('@/assets/default.png');
    }

    if (image.startsWith('http')) {
      return { uri: image };
    }

    // Construct Supabase Storage URL for meal images with cache busting
    const timestamp = Date.now();
    return {
      uri: `${process.env.EXPO_PUBLIC_SUPABASE_URL}/storage/v1/object/public/meals/${image}?t=${timestamp}`,
    };
  };

  const renderRecipeItem = (item: UserRecipe) => (
    <TouchableOpacity
      key={item.id}
      style={styles.newMealCard}
      onPress={() =>
        router.push({
          pathname: '/user-meal-detail',
          params: { userMealId: item.id },
        })
      }
      activeOpacity={1}
    >
      {/* Image with overlay buttons */}
      <View style={styles.imageContainer}>
        <Image 
          source={getImageSource(item.image)} 
          style={styles.mealImage}
          contentFit="cover"
        />

        {/* Delete button overlaying the image */}
        <View style={styles.imageOverlayButtons}>
          <Pressable
            style={styles.overlayButton}
            onPress={(e) => {
              e.stopPropagation();
              deleteRecipe(item.id);
            }}
          >
            <Ionicons name="trash-outline" size={18} color={colors.primary} />
          </Pressable>
        </View>
      </View>

      {/* Content below image */}
      <View style={styles.cardContent}>
        <ThemedText style={styles.mealName} numberOfLines={2}>
          {item.name}
        </ThemedText>

        {/* Description */}
        {item.description && (
          <ThemedText style={styles.recipeDescription} numberOfLines={2}>
            {item.description}
          </ThemedText>
        )}


        <View style={styles.mealInfoContainer}>
          <View style={styles.mealInfoItem}>
            <Ionicons name="time-outline" size={12} color={colors.text} style={{ opacity: 0.7 }} />
            <ThemedText style={styles.mealInfoText}>
              {formatTime(item.prep_time_hour, item.prep_time_min)}
            </ThemedText>
          </View>
          <View style={styles.mealInfoItem}>
            <Ionicons name="flame-outline" size={12} color={colors.text} style={{ opacity: 0.7 }} />
            <ThemedText style={styles.mealInfoText}>
              {formatTime(item.cook_time_hour, item.cook_time_min)}
            </ThemedText>
          </View>
          <View style={styles.mealInfoItem}>
            <Ionicons name="people-outline" size={12} color={colors.text} style={{ opacity: 0.7 }} />
            <ThemedText style={styles.mealInfoText}>
              {item.serving_size || 4}
            </ThemedText>
          </View>
        </View>

        <Pressable
          style={styles.editButton}
          onPress={(e) => {
            e.stopPropagation();
            router.push({
              pathname: '/user-meal-detail',
              params: { 
                userMealId: item.id,
                showEditButton: 'true'
              },
            });
          }}
        >
          <Ionicons name="create-outline" size={14} color={colors.primary} />
          <ThemedText style={styles.editButtonText}>Edit</ThemedText>
        </Pressable>
      </View>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <ThemedView style={styles.container}>
        <View style={[styles.header, { paddingTop: insets.top + 10 }]}>
          <View style={styles.headerSpacer} />
          <ThemedText type="title" style={styles.headerTitle}>
            My Recipes
          </ThemedText>
          <View style={styles.headerActions} />
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <ThemedText style={styles.loadingText}>
            Loading your recipes...
          </ThemedText>
        </View>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      {/* Title */}
      <Animated.View
        style={{
          opacity: titleSection.opacity,
          transform: [{ translateY: titleSection.translateY }]
        }}
      >
        <View style={styles.titleContainer}>
          <ThemedText style={styles.title}>My Recipes</ThemedText>
          <TouchableOpacity style={styles.addButton} onPress={handleAddRecipe}>
            <Ionicons name="add" size={24} color={colors.primary} />
          </TouchableOpacity>
        </View>
      </Animated.View>

      {/* Content */}
      <Animated.View
        style={{
          flex: 1,
          opacity: contentSection.opacity,
          transform: [{ translateY: contentSection.translateY }]
        }}
      >
        {recipes.length === 0 ? (
          <View style={styles.emptyState}>
            <Ionicons
              name="book-outline"
              size={64}
              color={colors.text}
              style={{ opacity: 0.3 }}
            />
            <ThemedText style={styles.emptyStateTitle}>No Recipes Yet</ThemedText>
            <ThemedText style={styles.emptyStateText}>
              Add your first recipe to get started!
            </ThemedText>
            <TouchableOpacity
              style={styles.addFirstRecipeButton}
              onPress={handleAddRecipe}
            >
              <Ionicons
                name="add-circle-outline"
                size={20}
                color={colors.primary}
              />
              <ThemedText style={styles.addFirstRecipeButtonText}>
                Add Recipe
              </ThemedText>
            </TouchableOpacity>
          </View>
        ) : (
          <ScrollView
            showsVerticalScrollIndicator={false}
            contentContainerStyle={[
              styles.scrollContent,
              { paddingBottom: contentBottomPadding },
            ]}
          >
            <View style={styles.gridContainer}>
              {recipes.map(renderRecipeItem)}
            </View>
          </ScrollView>
        )}
      </Animated.View>

      {/* Add Recipe Modal */}
      <Modal
        visible={showAddModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <View
          style={[
            styles.modalContainer,
            { backgroundColor: colors.background },
          ]}
        >
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowAddModal(false)}>
              <ThemedText style={styles.modalCancelText}>Cancel</ThemedText>
            </TouchableOpacity>
            <ThemedText type="subtitle" style={styles.modalTitle}>
              {editingRecipeId ? 'Edit Recipe' : 'Add Recipe'}
            </ThemedText>
            <TouchableOpacity onPress={handleSaveRecipe} disabled={saving}>
              {saving ? (
                <ActivityIndicator size="small" color={colors.primary} />
              ) : (
                <ThemedText style={styles.modalSaveText}>Save</ThemedText>
              )}
            </TouchableOpacity>
          </View>

          <ScrollView
            style={styles.modalContent}
            showsVerticalScrollIndicator={false}
          >
            {/* AI Recipe Analysis Section - First thing in form */}
            <View style={[styles.formSection, styles.aiAnalysisSection]}>
              <View style={styles.sectionTitleContainer}>
                <ThemedText style={styles.sectionTitle}>
                  AI Recipe Analysis
                </ThemedText>
                <ThemedText style={styles.usageCounter}>
                  {loadingUsage ? '...' : `${aiUsageToday}/5 today`}
                </ThemedText>
              </View>
              <TouchableOpacity
                style={[
                  styles.analyzeButton,
                  {
                    backgroundColor:
                      aiUsageToday >= 5 ? colors.text + '40' : colors.primary,
                    opacity: analyzing || aiUsageToday >= 5 ? 0.7 : 1,
                  },
                ]}
                onPress={takeRecipePhoto}
                disabled={analyzing || aiUsageToday >= 5}
              >
                <Ionicons name="camera" size={20} color={colors.background} />
                <ThemedText
                  style={[
                    styles.analyzeButtonText,
                    { color: colors.background },
                  ]}
                >
                  {aiUsageToday >= 5
                    ? 'Daily Limit Reached'
                    : 'Take Photo of Recipe to Auto-Fill Form'}
                </ThemedText>
              </TouchableOpacity>
              <ThemedText style={styles.aiHintText}>
                {aiUsageToday >= 5
                  ? "You've used all 5 AI analyses for today. Try again tomorrow!"
                  : 'Take a photo of a recipe card, cookbook page, or printed recipe to automatically fill the form fields below'}
              </ThemedText>
            </View>

            {/* Meal Photo Section */}
            <View style={styles.formSection}>
              <ThemedText style={styles.sectionTitle}>
                Meal Photo (Optional)
              </ThemedText>
              {newRecipe.image ? (
                <View style={styles.selectedImageContainer}>
                  <Image
                    source={{ uri: newRecipe.image }}
                    style={styles.selectedImage}
                  />
                  <TouchableOpacity
                    style={styles.removeImageButton}
                    onPress={removeImage}
                  >
                    <Ionicons
                      name="close-circle"
                      size={24}
                      color={
                        colors.background === '#FFFFFF' ? '#000000' : '#FF6B6B'
                      }
                    />
                  </TouchableOpacity>
                </View>
              ) : (
                <TouchableOpacity
                  style={[styles.photoButton, { borderColor: colors.border }]}
                  onPress={pickImage}
                >
                  <Ionicons
                    name="camera-outline"
                    size={24}
                    color={colors.primary}
                  />
                  <ThemedText
                    style={[styles.photoButtonText, { color: colors.primary }]}
                  >
                    Add Meal Photo
                  </ThemedText>
                  <ThemedText style={styles.photoButtonSubtext}>
                    Take a photo of the finished dish
                  </ThemedText>
                </TouchableOpacity>
              )}
            </View>

            {/* Basic Info */}
            <View style={styles.formSection}>
              <ThemedText style={styles.sectionTitle}>
                Basic Information
              </ThemedText>

              <View style={styles.inputGroup}>
                <ThemedText style={styles.inputLabel}>Recipe Name *</ThemedText>
                <TextInput
                  style={[
                    styles.textInput,
                    { color: colors.text, borderColor: colors.border },
                  ]}
                  value={newRecipe.name}
                  onChangeText={(text) =>
                    setNewRecipe((prev) => ({ ...prev, name: text }))
                  }
                  placeholder="Enter recipe name"
                  placeholderTextColor={colors.text + '80'}
                />
              </View>

              <View style={styles.inputGroup}>
                <ThemedText style={styles.inputLabel}>Description</ThemedText>
                <TextInput
                  style={[
                    styles.textInput,
                    styles.textArea,
                    { color: colors.text, borderColor: colors.border },
                  ]}
                  value={newRecipe.description}
                  onChangeText={(text) =>
                    setNewRecipe((prev) => ({ ...prev, description: text }))
                  }
                  placeholder="Describe your recipe..."
                  placeholderTextColor={colors.text + '80'}
                  multiline
                  numberOfLines={3}
                />
              </View>

              {/* Time and Servings */}
              <View style={styles.timeRow}>
                <View style={styles.timeGroup}>
                  <ThemedText style={styles.inputLabel}>Prep Time</ThemedText>
                  <View style={styles.timeInputs}>
                    <TextInput
                      style={[
                        styles.timeInput,
                        { color: colors.text, borderColor: colors.border },
                      ]}
                      value={newRecipe.prepTimeHour.toString()}
                      onChangeText={(text) =>
                        setNewRecipe((prev) => ({
                          ...prev,
                          prepTimeHour: parseInt(text) || 0,
                        }))
                      }
                      placeholder="0"
                      placeholderTextColor={colors.text + '80'}
                      keyboardType="numeric"
                    />
                    <ThemedText style={styles.timeLabel}>h</ThemedText>
                    <TextInput
                      style={[
                        styles.timeInput,
                        { color: colors.text, borderColor: colors.border },
                      ]}
                      value={newRecipe.prepTimeMin.toString()}
                      onChangeText={(text) =>
                        setNewRecipe((prev) => ({
                          ...prev,
                          prepTimeMin: parseInt(text) || 0,
                        }))
                      }
                      placeholder="0"
                      placeholderTextColor={colors.text + '80'}
                      keyboardType="numeric"
                    />
                    <ThemedText style={styles.timeLabel}>m</ThemedText>
                  </View>
                </View>

                <View style={styles.timeGroup}>
                  <ThemedText style={styles.inputLabel}>Cook Time</ThemedText>
                  <View style={styles.timeInputs}>
                    <TextInput
                      style={[
                        styles.timeInput,
                        { color: colors.text, borderColor: colors.border },
                      ]}
                      value={newRecipe.cookTimeHour.toString()}
                      onChangeText={(text) =>
                        setNewRecipe((prev) => ({
                          ...prev,
                          cookTimeHour: parseInt(text) || 0,
                        }))
                      }
                      placeholder="0"
                      placeholderTextColor={colors.text + '80'}
                      keyboardType="numeric"
                    />
                    <ThemedText style={styles.timeLabel}>h</ThemedText>
                    <TextInput
                      style={[
                        styles.timeInput,
                        { color: colors.text, borderColor: colors.border },
                      ]}
                      value={newRecipe.cookTimeMin.toString()}
                      onChangeText={(text) =>
                        setNewRecipe((prev) => ({
                          ...prev,
                          cookTimeMin: parseInt(text) || 0,
                        }))
                      }
                      placeholder="0"
                      placeholderTextColor={colors.text + '80'}
                      keyboardType="numeric"
                    />
                    <ThemedText style={styles.timeLabel}>m</ThemedText>
                  </View>
                </View>
              </View>

              <View style={styles.inputGroup}>
                <ThemedText style={styles.inputLabel}>Servings</ThemedText>
                <TextInput
                  style={[
                    styles.textInput,
                    { color: colors.text, borderColor: colors.border },
                  ]}
                  value={newRecipe.servingSize.toString()}
                  onChangeText={(text) =>
                    setNewRecipe((prev) => ({
                      ...prev,
                      servingSize: parseInt(text) || 1,
                    }))
                  }
                  placeholder="4"
                  placeholderTextColor={colors.text + '80'}
                  keyboardType="numeric"
                />
              </View>
            </View>

            {/* Category Information */}
            <View style={styles.formSection}>
              <ThemedText style={styles.sectionTitle}>
                Category Information
              </ThemedText>

              <DropdownField
                label="Course"
                value={newRecipe.course}
                fieldName="course"
                options={[
                  'Breakfast',
                  'Lunch',
                  'Dinner',
                  'Main Course',
                  'Side Dish',
                  'Appetizer',
                  'Snack',
                  'Dessert',
                  'Beverage',
                ]}
                type="select"
              />

              <DropdownField
                label="Cuisine Type"
                value={newRecipe.cuisine_type}
                fieldName="cuisine_type"
                options={[
                  'American',
                  'Italian',
                  'Mexican',
                  'Asian',
                  'Indian',
                  'Mediterranean',
                  'French',
                  'Thai',
                  'Japanese',
                  'Greek',
                  'Middle Eastern',
                  'Chinese',
                ]}
                type="select"
              />
            </View>

            {/* Dietary Information */}
            <View style={styles.formSection}>
              <ThemedText style={styles.sectionTitle}>
                Dietary Information
              </ThemedText>

              <DropdownField
                label="Dietary Restrictions"
                value={newRecipe.dietary_tags}
                fieldName="dietary_tags"
                options={[
                  'Vegetarian',
                  'Vegan',
                  'Gluten-Free',
                  'Dairy-Free',
                  'Keto',
                  'Paleo',
                  'Low-Carb',
                  'High-Protein',
                  'Nut-Free',
                  'Soy-Free',
                ]}
                type="multiselect"
              />

              <DropdownField
                label="Contains Allergens"
                value={newRecipe.allergen_contains}
                fieldName="allergen_contains"
                options={[
                  'Dairy',
                  'Eggs',
                  'Fish',
                  'Shellfish',
                  'Tree Nuts',
                  'Peanuts',
                  'Wheat',
                  'Soy',
                  'Sesame',
                ]}
                type="multiselect"
              />
            </View>

            {/* Nutrition Information */}
            <View style={styles.formSection}>
              <ThemedText style={styles.sectionTitle}>
                Nutrition (per serving)
              </ThemedText>

              <View style={styles.nutritionRow}>
                <View style={styles.nutritionGroup}>
                  <ThemedText style={styles.inputLabel}>Calories</ThemedText>
                  <TextInput
                    style={[
                      styles.nutritionInput,
                      { color: colors.text, borderColor: colors.border },
                    ]}
                    value={newRecipe.calories.toString()}
                    onChangeText={(text) =>
                      setNewRecipe((prev) => ({
                        ...prev,
                        calories: parseInt(text) || 0,
                      }))
                    }
                    placeholder="0"
                    placeholderTextColor={colors.text + '80'}
                    keyboardType="numeric"
                  />
                </View>

                <View style={styles.nutritionGroup}>
                  <ThemedText style={styles.inputLabel}>Protein (g)</ThemedText>
                  <TextInput
                    style={[
                      styles.nutritionInput,
                      { color: colors.text, borderColor: colors.border },
                    ]}
                    value={newRecipe.protein.toString()}
                    onChangeText={(text) =>
                      setNewRecipe((prev) => ({
                        ...prev,
                        protein: parseInt(text) || 0,
                      }))
                    }
                    placeholder="0"
                    placeholderTextColor={colors.text + '80'}
                    keyboardType="numeric"
                  />
                </View>
              </View>

              <View style={styles.nutritionRow}>
                <View style={styles.nutritionGroup}>
                  <ThemedText style={styles.inputLabel}>Carbs (g)</ThemedText>
                  <TextInput
                    style={[
                      styles.nutritionInput,
                      { color: colors.text, borderColor: colors.border },
                    ]}
                    value={newRecipe.carbs.toString()}
                    onChangeText={(text) =>
                      setNewRecipe((prev) => ({
                        ...prev,
                        carbs: parseInt(text) || 0,
                      }))
                    }
                    placeholder="0"
                    placeholderTextColor={colors.text + '80'}
                    keyboardType="numeric"
                  />
                </View>

                <View style={styles.nutritionGroup}>
                  <ThemedText style={styles.inputLabel}>Fats (g)</ThemedText>
                  <TextInput
                    style={[
                      styles.nutritionInput,
                      { color: colors.text, borderColor: colors.border },
                    ]}
                    value={newRecipe.fats.toString()}
                    onChangeText={(text) =>
                      setNewRecipe((prev) => ({
                        ...prev,
                        fats: parseInt(text) || 0,
                      }))
                    }
                    placeholder="0"
                    placeholderTextColor={colors.text + '80'}
                    keyboardType="numeric"
                  />
                </View>
              </View>
            </View>

            {/* Ingredients */}
            <View style={styles.formSection}>
              <ThemedText style={styles.sectionTitle}>Ingredients *</ThemedText>

              {/* Legend Headers */}
              <View style={styles.ingredientLegend}>
                <View style={styles.emojiLegendSpace} />
                <ThemedText style={styles.legendText}>Amount</ThemedText>
                <ThemedText style={styles.legendText}>Unit</ThemedText>
                <ThemedText style={styles.legendText}>Name</ThemedText>
                <View style={styles.deleteLegendSpace} />
              </View>

              {newRecipe.ingredients.map((ingredient, index) => (
                <View key={index} style={styles.ingredientRow}>
                  <EmojiPicker
                    value={ingredient.emoji}
                    onChange={(emoji) =>
                      updateIngredient(index, 'emoji', emoji)
                    }
                  />
                  <TextInput
                    style={[
                      styles.ingredientInput,
                      { color: colors.text, borderColor: colors.border },
                    ]}
                    value={ingredient.amount}
                    onChangeText={(text) =>
                      updateIngredient(index, 'amount', text)
                    }
                  />
                  <TextInput
                    style={[
                      styles.ingredientInput,
                      { color: colors.text, borderColor: colors.border },
                    ]}
                    value={ingredient.unit}
                    onChangeText={(text) =>
                      updateIngredient(index, 'unit', text)
                    }
                    autoCapitalize="none"
                  />
                  <TextInput
                    style={[
                      styles.ingredientInput,
                      styles.ingredientNameInput,
                      { color: colors.text, borderColor: colors.border },
                    ]}
                    value={ingredient.name}
                    onChangeText={(text) =>
                      updateIngredient(index, 'name', text)
                    }
                  />
                  {newRecipe.ingredients.length > 1 && (
                    <TouchableOpacity onPress={() => removeIngredient(index)}>
                      <Ionicons
                        name="remove-circle-outline"
                        size={20}
                        color={
                          colors.background === '#FFFFFF'
                            ? '#000000'
                            : '#FF6B6B'
                        }
                      />
                    </TouchableOpacity>
                  )}
                </View>
              ))}

              <TouchableOpacity
                onPress={addIngredient}
                style={[styles.outlineButton, { borderColor: colors.primary }]}
              >
                <Ionicons
                  name="add-circle-outline"
                  size={16}
                  color={colors.primary}
                />
                <ThemedText
                  style={[styles.outlineButtonText, { color: colors.primary }]}
                >
                  Add Ingredient
                </ThemedText>
              </TouchableOpacity>
            </View>

            {/* Instructions */}
            <View style={styles.formSection}>
              <ThemedText style={styles.sectionTitle}>
                Instructions *
              </ThemedText>

              {newRecipe.instructions.map((instruction, index) => (
                <View key={index} style={styles.instructionRow}>
                  <View style={styles.stepNumber}>
                    <ThemedText style={styles.stepNumberText}>
                      {index + 1}
                    </ThemedText>
                  </View>
                  <TextInput
                    style={[
                      styles.instructionInput,
                      { color: colors.text, borderColor: colors.border },
                    ]}
                    value={instruction}
                    onChangeText={(text) => updateInstruction(index, text)}
                    placeholder="Add instruction step..."
                    placeholderTextColor={colors.text + '80'}
                    multiline
                  />
                  {newRecipe.instructions.length > 1 && (
                    <TouchableOpacity onPress={() => removeInstruction(index)}>
                      <Ionicons
                        name="remove-circle-outline"
                        size={20}
                        color={
                          colors.background === '#FFFFFF'
                            ? '#000000'
                            : '#FF6B6B'
                        }
                      />
                    </TouchableOpacity>
                  )}
                </View>
              ))}

              <TouchableOpacity
                onPress={addInstruction}
                style={[styles.outlineButton, { borderColor: colors.primary }]}
              >
                <Ionicons
                  name="add-circle-outline"
                  size={16}
                  color={colors.primary}
                />
                <ThemedText
                  style={[styles.outlineButtonText, { color: colors.primary }]}
                >
                  Add Instruction
                </ThemedText>
              </TouchableOpacity>
            </View>

            <View style={styles.modalBottomPadding} />
          </ScrollView>

          {/* Full Screen Loading Overlay */}
          {analyzing && (
            <View style={styles.analysisOverlay}>
              <View
                style={[
                  styles.analysisModal,
                  { backgroundColor: colors.cardBackground },
                ]}
              >
                <ActivityIndicator size="large" color={colors.primary} />
                <ThemedText
                  style={[styles.analysisTitle, { color: colors.text }]}
                >
                  Analyzing Recipe
                </ThemedText>
                <ThemedText
                  style={[styles.analysisProgress, { color: colors.text }]}
                >
                  {analysisProgress}
                </ThemedText>
                <ThemedText
                  style={[styles.analysisHint, { color: colors.text }]}
                >
                  Please wait while AI reads your recipe...
                </ThemedText>
                <ThemedText
                  style={[styles.analysisDisclaimer, { color: colors.text }]}
                >
                  <ThemedText style={{ fontWeight: 'bold' }}>
                    AI can make mistakes.
                  </ThemedText>{' '}
                  Always review and verify all extracted information.
                </ThemedText>
              </View>
            </View>
          )}
        </View>
      </Modal>

      <CustomAlert
        visible={showAlert}
        title={alertConfig.title}
        message={alertConfig.message}
        onConfirm={() => setShowAlert(false)}
        onCancel={() => setShowAlert(false)}
      />
    </ThemedView>
  );
}

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    titleContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    title: {
      fontSize: 28,
      fontWeight: 'bold',
      color: colors.text,
      paddingHorizontal: 24,
      paddingTop: 80,
      paddingBottom: 20,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 24,
      paddingBottom: 16,
      justifyContent: 'space-between',
      backgroundColor: colors.background,
    },
    headerSpacer: {
      width: 40,
    },
    headerTitle: {
      flex: 1,
      textAlign: 'center',
      marginHorizontal: 16,
    },
    headerActions: {
      width: 40,
    },
    addButton: {
      padding: 8,
      marginTop: 80,
      marginBottom: 20,
      marginRight: 24,
      backgroundColor: `${colors.primary}10`,
      borderRadius: 8,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      gap: 16,
    },
    loadingText: {
      fontSize: 16,
      opacity: 0.7,
    },
    emptyState: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 48,
      gap: 16,
    },
    emptyStateTitle: {
      fontSize: 24,
      fontWeight: 'bold',
      textAlign: 'center',
    },
    emptyStateText: {
      fontSize: 16,
      opacity: 0.7,
      textAlign: 'center',
      lineHeight: 24,
    },
    addFirstRecipeButton: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 8,
      backgroundColor: 'transparent',
      borderWidth: 1,
      borderColor: colors.primary,
      paddingVertical: 12,
      paddingHorizontal: 24,
      borderRadius: 8,
      marginTop: 16,
    },
    addFirstRecipeButtonText: {
      color: colors.primary,
      fontSize: 16,
      fontWeight: '600',
    },
    scrollContent: {
      paddingHorizontal: 16,
      // paddingBottom will be set dynamically based on tab bar height
    },
    gridContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'space-between',
    },
    newMealCard: {
      width: '48%',
      borderRadius: 8,
      overflow: 'hidden',
      backgroundColor: colors.cardBackground,
      marginBottom: 16,
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 8,
      },
      shadowOpacity: 0.25,
      shadowRadius: 16,
      elevation: 16,
    },
    imageContainer: {
      position: 'relative',
      width: '100%',
      height: 140,
    },
    mealImage: {
      width: '100%',
      height: 140,
    },
    imageOverlayButtons: {
      position: 'absolute',
      top: 12,
      left: 12,
      right: 12,
      bottom: 12,
      zIndex: 1,
    },
    overlayButton: {
      position: 'absolute',
      top: 0,
      right: 0,
      width: 36,
      height: 36,
      borderRadius: 18,
      backgroundColor:
        colors.background === '#f3f5f7'
          ? 'rgba(255, 255, 255, 0.9)'
          : 'rgba(0, 0, 0, 0.6)',
      alignItems: 'center',
      justifyContent: 'center',
      borderWidth: 1,
      borderColor:
        colors.background === '#f3f5f7'
          ? 'rgba(0, 0, 0, 0.1)'
          : 'rgba(255, 255, 255, 0.2)',
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: colors.background === '#f3f5f7' ? 0.25 : 0.15,
      shadowRadius: 3.84,
      elevation: 5,
    },
    cardContent: {
      padding: 12,
      paddingBottom: 8,
      flex: 1,
    },
    mealName: {
      fontSize: 16,
      fontWeight: '600',
      color: colors.text,
      marginBottom: 4,
    },
    recipeDescription: {
      fontSize: 12,
      opacity: 0.7,
      marginBottom: 6,
      lineHeight: 16,
      color: colors.text,
    },
    mealInfoContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 6,
      marginBottom: 12,
    },
    mealInfoItem: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 2,
    },
    mealInfoText: {
      fontSize: 10,
      fontWeight: '400',
      color: colors.text,
      opacity: 0.8,
    },
    editButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      gap: 6,
      borderWidth: 1,
      borderColor: colors.primary,
      paddingVertical: 8,
      paddingHorizontal: 12,
      borderRadius: 6,
      marginTop: 12,
      backgroundColor: 'transparent',
    },
    editButtonText: {
      fontSize: 14,
      fontWeight: '600',
      color: colors.primary,
    },
    modalContainer: {
      flex: 1,
    },
    modalHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 24,
      paddingVertical: 16,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    modalCancelText: {
      color: '#FF6B6B',
      fontSize: 16,
    },
    modalTitle: {
      fontSize: 18,
      fontWeight: '600',
    },
    modalSaveText: {
      color: colors.primary,
      fontSize: 16,
      fontWeight: '600',
    },
    modalContent: {
      flex: 1,
      paddingHorizontal: 24,
    },
    formSection: {
      marginVertical: 20,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: colors.primary,
      marginBottom: 16,
    },
    inputGroup: {
      marginBottom: 16,
    },
    inputLabel: {
      fontSize: 14,
      fontWeight: '500',
      marginBottom: 8,
      opacity: 0.9,
    },
    textInput: {
      borderWidth: 1,
      borderRadius: 8,
      paddingHorizontal: 12,
      paddingVertical: 12,
      fontSize: 16,
      backgroundColor: colors.cardBackground,
    },
    textArea: {
      height: 80,
      textAlignVertical: 'top',
    },
    timeRow: {
      flexDirection: 'row',
      gap: 16,
      marginBottom: 16,
    },
    timeGroup: {
      flex: 1,
    },
    timeInputs: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 8,
    },
    timeInput: {
      borderWidth: 1,
      borderRadius: 8,
      paddingHorizontal: 12,
      paddingVertical: 8,
      fontSize: 16,
      backgroundColor: colors.cardBackground,
      width: 50,
      textAlign: 'center',
    },
    timeLabel: {
      fontSize: 14,
      opacity: 0.7,
    },
    ingredientRow: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 8,
      marginBottom: 12,
    },
    ingredientInput: {
      borderWidth: 1,
      borderRadius: 8,
      paddingHorizontal: 8,
      paddingVertical: 8,
      fontSize: 14,
      backgroundColor: colors.cardBackground,
      width: 60,
    },
    ingredientNameInput: {
      flex: 1,
      width: 'auto',
    },
    ingredientLegend: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 8,
      marginBottom: 8,
      paddingHorizontal: 4,
    },
    emojiLegendSpace: {
      width: 40, // Same width as EmojiPicker
    },
    deleteLegendSpace: {
      width: 20, // Same width as delete button
    },
    legendText: {
      fontSize: 12,
      opacity: 0.6,
      fontWeight: '500',
      width: 60, // Same width as ingredientInput
      textAlign: 'center',
    },
    instructionRow: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      gap: 12,
      marginBottom: 12,
    },
    stepNumber: {
      width: 24,
      height: 24,
      borderRadius: 8,
      backgroundColor: colors.primary,
      alignItems: 'center',
      justifyContent: 'center',
      marginTop: 8,
    },
    stepNumberText: {
      color: colors.background,
      fontSize: 12,
      fontWeight: '600',
    },
    instructionInput: {
      flex: 1,
      borderWidth: 1,
      borderRadius: 8,
      paddingHorizontal: 12,
      paddingVertical: 8,
      fontSize: 14,
      backgroundColor: colors.cardBackground,
      minHeight: 40,
      textAlignVertical: 'top',
    },
    photoButton: {
      borderWidth: 1,
      borderStyle: 'dashed',
      borderRadius: 8,
      paddingVertical: 24,
      alignItems: 'center',
      gap: 8,
    },
    photoButtonText: {
      fontSize: 16,
      fontWeight: '500',
    },
    photoButtonSubtext: {
      fontSize: 12,
      opacity: 0.5,
    },
    selectedImageContainer: {
      position: 'relative',
      alignItems: 'center',
    },
    selectedImage: {
      width: 200,
      height: 150,
      borderRadius: 8,
    },
    removeImageButton: {
      position: 'absolute',
      top: -8,
      right: 50,
      backgroundColor:
        colors.background === '#FFFFFF' ? 'transparent' : 'rgba(0, 0, 0, 0.8)',
      borderRadius: 8,
    },
    analyzeButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      gap: 8,
      paddingVertical: 12,
      paddingHorizontal: 20,
      borderRadius: 8,
      marginTop: 12,
    },
    analyzeButtonText: {
      fontSize: 15,
      fontWeight: '600',
    },
    aiHintText: {
      fontSize: 12,
      opacity: 0.7,
      textAlign: 'center',
      marginTop: 8,
      fontStyle: 'italic',
    },
    aiAnalysisSection: {
      borderWidth: 2,
      borderStyle: 'dashed',
      borderColor: colors.primary + '40',
      borderRadius: 8,
      padding: 16,
      backgroundColor: colors.primary + '08',
    },
    sectionTitleContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 16,
    },
    usageCounter: {
      fontSize: 12,
      fontWeight: '600',
      color: colors.primary,
      backgroundColor: colors.primary + '20',
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 6,
    },
    nutritionRow: {
      flexDirection: 'row',
      gap: 16,
      marginBottom: 16,
    },
    nutritionGroup: {
      flex: 1,
    },
    nutritionInput: {
      borderWidth: 1,
      borderRadius: 8,
      paddingHorizontal: 12,
      paddingVertical: 8,
      fontSize: 16,
      backgroundColor: colors.cardBackground,
      textAlign: 'center',
    },
    outlineButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      gap: 8,
      backgroundColor: 'transparent',
      borderWidth: 1,
      paddingVertical: 12,
      paddingHorizontal: 16,
      borderRadius: 8,
      marginTop: 12,
    },
    outlineButtonText: {
      fontSize: 14,
      fontWeight: '600',
    },
    modalBottomPadding: {
      height: 100,
    },
    dropdownButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      borderWidth: 1,
      borderRadius: 8,
      paddingHorizontal: 12,
      paddingVertical: 12,
      backgroundColor: colors.cardBackground,
    },
    dropdownText: {
      fontSize: 16,
      flex: 1,
    },
    selectedTags: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 8,
      marginTop: 8,
    },
    tag: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 6,
      paddingHorizontal: 10,
      paddingVertical: 6,
      borderRadius: 16,
    },
    tagText: {
      fontSize: 12,
      fontWeight: '500',
      color: colors.background,
    },
    analysisOverlay: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 1000,
    },
    analysisModal: {
      padding: 32,
      borderRadius: 16,
      alignItems: 'center',
      marginHorizontal: 40,
      elevation: 10,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.3,
      shadowRadius: 8,
    },
    analysisTitle: {
      fontSize: 20,
      fontWeight: '600',
      marginTop: 16,
      textAlign: 'center',
    },
    analysisProgress: {
      fontSize: 16,
      marginTop: 8,
      textAlign: 'center',
    },
    analysisHint: {
      fontSize: 14,
      marginTop: 8,
      opacity: 0.7,
      textAlign: 'center',
      fontStyle: 'italic',
    },
    analysisDisclaimer: {
      fontSize: 13,
      marginTop: 12,
      opacity: 0.8,
      textAlign: 'center',
      lineHeight: 18,
    },
    // Dropdown edit styles
    dropdownEditContainer: {
      marginTop: 12,
    },
    selectContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 8,
      marginBottom: 16,
    },
    selectOption: {
      borderRadius: 16,
      paddingVertical: 8,
      paddingHorizontal: 12,
      borderWidth: 1,
    },
    selectOptionActive: {
      // backgroundColor and borderColor will be set dynamically
    },
    selectOptionText: {
      fontSize: 12,
    },
    selectOptionTextActive: {
      color: '#000000',
      fontWeight: '600',
    },
    multiselectContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 8,
      marginBottom: 16,
    },
    multiselectOption: {
      borderRadius: 16,
      paddingVertical: 8,
      paddingHorizontal: 12,
      borderWidth: 1,
    },
    multiselectOptionActive: {
      // backgroundColor and borderColor will be set dynamically
    },
    multiselectOptionText: {
      fontSize: 12,
    },
    multiselectOptionTextActive: {
      color: '#000000',
      fontWeight: '600',
    },
    dropdownActions: {
      flexDirection: 'row',
      gap: 12,
    },
    cancelButton: {
      flex: 1,
      borderRadius: 8,
      paddingVertical: 12,
      alignItems: 'center',
      borderWidth: 1,
    },
    cancelButtonText: {
      fontSize: 14,
      fontWeight: '600',
    },
    saveButton: {
      flex: 1,
      borderRadius: 8,
      paddingVertical: 12,
      alignItems: 'center',
      borderWidth: 1,
    },
    saveButtonText: {
      fontSize: 14,
      fontWeight: '600',
    },
  });

export default function ProtectedMyRecipesScreen() {
  return <MyRecipesScreen />;
}
