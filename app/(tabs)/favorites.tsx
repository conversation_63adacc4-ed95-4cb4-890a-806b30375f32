import { CustomAlert } from '@/components/CustomAlert';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { useAuth } from '@/contexts/AuthContext';
import { useTheme } from '@/contexts/ThemeContext';
import { useTabBarHeight } from '@/hooks/useTabBarHeight';
import { useStaggeredAnimation } from '@/hooks/useStaggeredAnimation';
import { Database } from '@/lib/database.types';
import { supabase } from '@/lib/supabase';
import { getMealsByIds } from '@/lib/mealFiltering';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useFocusEffect, useRouter } from 'expo-router';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
  ActivityIndicator,
  Animated,
  Modal,
  Pressable,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import { Image } from 'expo-image';

type Meal = Database['public']['Tables']['meals']['Row'];

export default function FavoritesScreen() {
  const { activeTheme } = useTheme();
  const colors = Colors[activeTheme];
  const styles = createStyles(colors);
  const { session } = useAuth();
  const router = useRouter();
  const { contentBottomPadding } = useTabBarHeight();
  const scrollViewRef = useRef<ScrollView>(null);
  const [favorites, setFavorites] = useState<Meal[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedMeal, setSelectedMeal] = useState<Meal | null>(null);
  const [showDayPicker, setShowDayPicker] = useState(false);
  const [showDuplicateAlert, setShowDuplicateAlert] = useState(false);

  // Use staggered animation hook for 2 sections: title, content
  const { sections: animatedSections } = useStaggeredAnimation(2);
  const [titleSection, contentSection] = animatedSections;

  useEffect(() => {
    if (session?.user?.id) {
      loadFavorites();
    }
  }, [session]);

  // Reload favorites when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      // Scroll to top when tab comes into focus
      scrollViewRef.current?.scrollTo({ y: 0, animated: false });

      if (session?.user?.id) {
        loadFavorites();
      }
    }, [session?.user?.id])
  );

  const loadFavorites = async () => {
    try {
      // First, get all favorite meal IDs for this user
      if (!session?.user?.id) return;

      const { data: favoriteIds, error: favError } = await supabase
        .from('user_favorites')
        .select('meal_id')
        .eq('user_id', session.user.id)
        .order('created_at', { ascending: false });

      if (favError) throw favError;

      if (!favoriteIds || favoriteIds.length === 0) {
        setFavorites([]);
        return;
      }

      // Then fetch the actual meal details (from both meals and user_meals tables)
      const mealIds = favoriteIds.map((f) => f.meal_id);
      const meals = await getMealsByIds(
        mealIds,
        session?.user?.id,
        'id, name, "prepTimeHour", "prepTimeMin", "cookTimeHour", "cookTimeMin", "servingSize", image, course, cuisine_type, prep_method, required_equipment'
      );

      // Order meals according to the favorite order
      const orderedMeals = favoriteIds
        .map((fav) => meals?.find((meal) => meal.id === fav.meal_id))
        .filter(Boolean) as Meal[];

      setFavorites(orderedMeals);
    } catch {
    } finally {
      setLoading(false);
    }
  };

  const toggleFavorite = async (mealId: string) => {
    if (!session?.user?.id) return;

    try {
      // Remove from favorites (since this is the favorites page, we know it's favorited)
      const { error } = await supabase
        .from('user_favorites')
        .delete()
        .eq('user_id', session.user.id)
        .eq('meal_id', mealId);

      if (error) throw error;

      // Remove from local state immediately
      setFavorites((prev) => prev.filter((meal) => meal.id !== mealId));

      // Set a flag to trigger favorites refresh on meal plan page
      await AsyncStorage.setItem('favoritesUpdated', Date.now().toString());
    } catch {}
  };

  const handleEatThis = (meal: Meal) => {
    setSelectedMeal(meal);
    setShowDayPicker(true);
  };

  const handleDaySelect = async (day: string) => {
    setShowDayPicker(false);
    await confirmMealSwap(day);
  };

  const confirmMealSwap = async (day: string) => {
    if (!selectedMeal || !session?.user?.id || !day) return;

    try {
      // Get current week's meal plan
      const { data: mealPlan, error: planError } = await supabase
        .from('weekly_meal_plans')
        .select('meal_ids')
        .eq('user_id', session.user.id)
        .eq('is_active', true)
        .single();

      const dayIndex = [
        'Sunday',
        'Monday',
        'Tuesday',
        'Wednesday',
        'Thursday',
        'Friday',
        'Saturday',
      ].indexOf(day);

      if (planError || !mealPlan) {
        // Create a new meal plan for the week
        const today = new Date();
        const weekStart = new Date(today);
        weekStart.setDate(today.getDate() - today.getDay()); // Set to Sunday

        const newMealIds = new Array(7).fill(null);
        newMealIds[dayIndex] = selectedMeal.id;

        const { error: insertError } = await supabase
          .from('weekly_meal_plans')
          .insert({
            user_id: session.user.id,
            week_start_date: weekStart.toISOString().split('T')[0],
            meal_ids: newMealIds,
            is_active: true,
          });

        if (insertError) throw insertError;
      } else {
        // Check if this meal is already in the meal plan
        const currentMealIds = mealPlan.meal_ids || [];
        if (currentMealIds.includes(selectedMeal.id)) {
          // Show warning alert
          setShowDuplicateAlert(true);
          return;
        }

        // Update existing meal plan
        const updatedMealIds = [
          ...(mealPlan.meal_ids || new Array(7).fill(null)),
        ];
        updatedMealIds[dayIndex] = selectedMeal.id;

        const { error: updateError } = await supabase
          .from('weekly_meal_plans')
          .update({ meal_ids: updatedMealIds })
          .eq('user_id', session.user.id)
          .eq('is_active', true);

        if (updateError) throw updateError;
      }

      // Navigate to meal plan with refresh flag
      router.push('/(tabs)/meal-plan?refresh=true');
    } catch {
      // Could add error alert here if needed
    }
  };

  const formatTime = (hours: number | null, minutes: number | null) => {
    if (!hours && !minutes) return '0 min';
    const h = hours || 0;
    const m = minutes || 0;
    return h > 0 ? `${h}h ${m}m` : `${m} min`;
  };

  const getImageSource = (image: string | null) => {
    if (!image || image === 'default.jpg') {
      return require('@/assets/default.png');
    }

    // If it's already a full URL, use it as-is
    if (image.startsWith('http')) {
      return { uri: image };
    }

    // Construct Supabase Storage URL for meal images with cache busting
    const timestamp = Date.now();
    return {
      uri: `${process.env.EXPO_PUBLIC_SUPABASE_URL}/storage/v1/object/public/meals/${image}?t=${timestamp}`,
    };
  };


  const renderMealCard = (meal: Meal) => (
    <TouchableOpacity
      key={meal.id}
      style={styles.newMealCard}
      onPress={() =>
        router.push({
          pathname: '/meal-detail',
          params: {
            mealId: meal.id,
            meal: JSON.stringify({
              name: meal.name,
              prepTime: formatTime(meal.prepTimeHour, meal.prepTimeMin),
              cookTime: formatTime(meal.cookTimeHour, meal.cookTimeMin),
              servingSize: meal.servingSize?.toString() || '4',
              rating: 4.7,
              totalTime: formatTime(
                (meal.prepTimeHour || 0) + (meal.cookTimeHour || 0),
                (meal.prepTimeMin || 0) + (meal.cookTimeMin || 0)
              ),
              image: meal.image || '',
            }),
          },
        })
      }
      activeOpacity={1}
    >
      {/* Image with overlay buttons */}
      <View style={styles.imageContainer}>
        <Image source={getImageSource(meal.image)} style={styles.mealImage} />

        {/* Heart button overlaying the image */}
        <View style={styles.imageOverlayButtons}>
          <Pressable
            style={styles.overlayButton}
            onPress={(e) => {
              e.stopPropagation();
              toggleFavorite(meal.id);
            }}
          >
            <Ionicons name="heart" size={18} color={colors.primary} />
          </Pressable>
        </View>
      </View>

      {/* Content below image */}
      <View style={styles.cardContent}>
        <ThemedText style={styles.mealName} numberOfLines={2}>
          {meal.name}
        </ThemedText>

        <View style={styles.mealInfoContainer}>
          <View style={styles.mealInfoItem}>
            <Ionicons name="time-outline" size={12} color={colors.text} style={{ opacity: 0.7 }} />
            <ThemedText style={styles.mealInfoText}>
              {formatTime(meal.prepTimeHour, meal.prepTimeMin)}
            </ThemedText>
          </View>
          <View style={styles.mealInfoItem}>
            <Ionicons name="flame-outline" size={12} color={colors.text} style={{ opacity: 0.7 }} />
            <ThemedText style={styles.mealInfoText}>
              {formatTime(meal.cookTimeHour, meal.cookTimeMin)}
            </ThemedText>
          </View>
          <View style={styles.mealInfoItem}>
            <Ionicons name="people-outline" size={12} color={colors.text} style={{ opacity: 0.7 }} />
            <ThemedText style={styles.mealInfoText}>
              {meal.servingSize || 4}
            </ThemedText>
          </View>
        </View>

        <Pressable
          style={styles.eatThisButton}
          onPress={(e) => {
            e.stopPropagation();
            handleEatThis(meal);
          }}
        >
          <Ionicons name="add" size={16} color={colors.primary} />
          <ThemedText style={styles.eatThisText}>Eat This</ThemedText>
        </Pressable>
      </View>
    </TouchableOpacity>
  );

  const daysOfWeek = [
    'Sunday',
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
    'Saturday',
  ];

  if (loading) {
    return (
      <ThemedView style={styles.container}>
        <ActivityIndicator
          size="large"
          color={colors.primary}
          style={styles.loader}
        />
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <Animated.View
        style={{
          opacity: titleSection.opacity,
          transform: [{ translateY: titleSection.translateY }]
        }}
      >
        <ThemedText style={styles.title}>My Favorites</ThemedText>
      </Animated.View>

      <Animated.View
        style={{
          flex: 1,
          opacity: contentSection.opacity,
          transform: [{ translateY: contentSection.translateY }]
        }}
      >
        {favorites.length === 0 ? (
          <View style={styles.emptyState}>
            <Ionicons
              name="heart-outline"
              size={64}
              color={colors.text}
              style={styles.emptyIcon}
            />
            <ThemedText style={styles.emptyText}>No favorites yet</ThemedText>
            <ThemedText style={styles.emptySubtext}>
              Tap the heart icon on meals you love to save them here
            </ThemedText>
          </View>
        ) : (
          <ScrollView
            ref={scrollViewRef}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={[
              styles.scrollContent,
              { paddingBottom: contentBottomPadding },
            ]}
          >
            <View style={styles.gridContainer}>
              {favorites.map(renderMealCard)}
            </View>
          </ScrollView>
        )}
      </Animated.View>

      <Modal
        visible={showDayPicker}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowDayPicker(false)}
      >
        <Pressable
          style={styles.modalOverlay}
          onPress={() => setShowDayPicker(false)}
        >
          <View style={styles.modalContent}>
            <ThemedText style={styles.modalTitle}>
              Choose a day for {selectedMeal?.name}
            </ThemedText>

            {daysOfWeek.map((day) => (
              <TouchableOpacity
                key={day}
                style={styles.dayOption}
                onPress={() => handleDaySelect(day)}
              >
                <ThemedText style={styles.dayText}>{day}</ThemedText>
                <Ionicons
                  name="chevron-forward"
                  size={20}
                  color={colors.text}
                />
              </TouchableOpacity>
            ))}

            <TouchableOpacity
              style={styles.cancelButton}
              onPress={() => setShowDayPicker(false)}
            >
              <ThemedText style={styles.cancelText}>Cancel</ThemedText>
            </TouchableOpacity>
          </View>
        </Pressable>
      </Modal>

      <CustomAlert
        visible={showDuplicateAlert}
        title="Meal Already Added"
        message={`"${selectedMeal?.name}" is already in your meal plan. Each meal can only be added once per week.`}
        confirmText="OK"
        onConfirm={() => setShowDuplicateAlert(false)}
        onCancel={() => setShowDuplicateAlert(false)}
      />
    </ThemedView>
  );
}

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    title: {
      fontSize: 28,
      fontWeight: 'bold',
      color: colors.text,
      paddingHorizontal: 24,
      paddingTop: 80,
      paddingBottom: 20,
    },
    loader: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    scrollContent: {
      paddingHorizontal: 16,
      // paddingBottom will be set dynamically based on tab bar height
    },
    gridContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'space-between',
    },
    emptyState: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 40,
    },
    emptyIcon: {
      opacity: 0.5,
      marginBottom: 16,
    },
    emptyText: {
      fontSize: 20,
      fontWeight: '600',
      color: colors.text,
      marginBottom: 8,
    },
    emptySubtext: {
      fontSize: 16,
      color: colors.text,
      opacity: 0.7,
      textAlign: 'center',
    },
    newMealCard: {
      width: '48%',
      borderRadius: 8,
      overflow: 'hidden',
      backgroundColor: colors.cardBackground,
      marginBottom: 16,
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 8,
      },
      shadowOpacity: 0.25,
      shadowRadius: 16,
      elevation: 16,
    },
    imageContainer: {
      position: 'relative',
      width: '100%',
      height: 140,
    },
    mealImage: {
      width: '100%',
      height: '100%',
      resizeMode: 'cover',
    },
    imageOverlayButtons: {
      position: 'absolute',
      top: 12,
      left: 12,
      right: 12,
      bottom: 12,
      zIndex: 1,
    },
    overlayButton: {
      position: 'absolute',
      top: 0,
      right: 0,
      width: 36,
      height: 36,
      borderRadius: 18,
      backgroundColor:
        colors.background === '#f3f5f7'
          ? 'rgba(255, 255, 255, 0.9)'
          : 'rgba(0, 0, 0, 0.6)',
      alignItems: 'center',
      justifyContent: 'center',
      borderWidth: 1,
      borderColor:
        colors.background === '#f3f5f7'
          ? 'rgba(0, 0, 0, 0.1)'
          : 'rgba(255, 255, 255, 0.2)',
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: colors.background === '#f3f5f7' ? 0.25 : 0.15,
      shadowRadius: 3.84,
      elevation: 5,
    },
    cardContent: {
      padding: 12,
      paddingBottom: 8,
      flex: 1,
    },
    mealName: {
      fontSize: 16,
      fontWeight: '600',
      color: colors.text,
      marginBottom: 8,
    },
    mealInfoContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 6,
      marginBottom: 12,
    },
    mealInfoItem: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 2,
    },
    mealInfoText: {
      fontSize: 10,
      fontWeight: '400',
      color: colors.text,
      opacity: 0.8,
    },
    eatThisButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      gap: 4,
      borderWidth: 1,
      borderColor: colors.primary,
      paddingVertical: 8,
      paddingHorizontal: 12,
      borderRadius: 6,
      marginTop: 12,
      backgroundColor: 'transparent',
    },
    eatThisText: {
      fontSize: 14,
      fontWeight: '600',
      color: colors.primary,
    },
    modalOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'flex-end',
    },
    modalContent: {
      backgroundColor: colors.cardBackground,
      borderTopLeftRadius: 20,
      borderTopRightRadius: 20,
      padding: 24,
      paddingBottom: 40,
    },
    modalTitle: {
      fontSize: 20,
      fontWeight: '600',
      color: colors.text,
      marginBottom: 20,
      textAlign: 'center',
    },
    dayOption: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: 16,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    dayText: {
      fontSize: 18,
      color: colors.text,
    },
    cancelButton: {
      marginTop: 20,
      paddingVertical: 12,
      alignItems: 'center',
    },
    cancelText: {
      fontSize: 16,
      color: colors.text,
      opacity: 0.7,
    },
  });
