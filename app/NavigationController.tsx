import { getUserPreferences } from '@/lib/userPreferences';
import { supabase } from '@/lib/supabase';
import { router } from 'expo-router';
import React, { useEffect, useRef, useState } from 'react';
import { View, ActivityIndicator } from 'react-native';
import { Colors } from '@/constants/Colors';
import { useAuth } from '@/contexts/AuthContext';
import { useSubscription } from '@/contexts/SubscriptionContext';

type NavigationState = 'pending' | 'navigating' | 'completed';

export default function NavigationController() {
  const { session, loading: authLoading } = useAuth();
  const { isSubscribed, loading: subscriptionLoading } = useSubscription();
  const [navigationState, setNavigationState] =
    useState<NavigationState>('pending');
  const navigationAttempts = useRef(0);
  const maxAttempts = 20; // Give more attempts
  const colors = Colors.light;

  // Reset navigation when session changes (login/logout)
  useEffect(() => {
    setNavigationState('pending');
    navigationAttempts.current = 0;
  }, [session?.user?.id]); // Reset when user ID changes

  useEffect(() => {
    const performNavigation = async () => {
      // Prevent multiple navigation attempts
      if (navigationState === 'navigating' || navigationState === 'completed') {
        return;
      }

      // Safety check for infinite loops
      navigationAttempts.current++;
      if (navigationAttempts.current > maxAttempts) {
        router.replace('/(auth)/auth');
        return;
      }

      // Step 1: Wait for auth to complete
      if (authLoading) {
        return;
      }

      // Step 2: If no session, go to auth
      if (!session) {
        setNavigationState('navigating');
        router.replace('/(auth)/auth');
        setNavigationState('completed');
        return;
      }

      // Step 3: For authenticated users, we DIRECTLY check the database
      // This bypasses any context loading issues
      setNavigationState('navigating');

      try {
        // Direct database check for subscription
        const { data: subscriptionData, error: subError } = await supabase
          .from('user_preferences')
          .select('subscription_status, subscription_tier')
          .eq('user_id', session.user.id)
          .single();

        if (subError) {
          // If we can't check subscription, assume needs onboarding
          router.replace('/(onboarding)/welcome');
          setNavigationState('completed');
          return;
        }

        const isUserSubscribed =
          subscriptionData?.subscription_status === 'active';

        if (!isUserSubscribed) {
          router.replace('/(paywall)/paywall');
        } else {
          // Check onboarding status
          const preferences = await getUserPreferences();

          if (!preferences) {
            router.replace('/(onboarding)/welcome');
          } else {
            router.replace('/(tabs)/meal-plan');
          }
        }

        setNavigationState('completed');
      } catch (error) {
        // On error, go to onboarding as safe fallback
        router.replace('/(onboarding)/welcome');
        setNavigationState('completed');
      }
    };

    // Use a longer delay to ensure all contexts are initialized
    const timer = setTimeout(performNavigation, 200);

    return () => clearTimeout(timer);
  }, [authLoading, session, navigationState]);

  // Show loading screen
  return (
    <View
      style={{
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: colors.background,
      }}
    >
      <ActivityIndicator size="large" color={colors.primary} />
    </View>
  );
}
