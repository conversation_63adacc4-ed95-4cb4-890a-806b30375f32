import { CustomAlert } from '@/components/CustomAlert';
// Removed unused imports: Button, LoadingIndicator
import { Colors } from '@/constants/Colors';
import { useAuth } from '@/contexts/AuthContext';
import { validateEmail, validateName } from '@/lib/inputValidation';
// Removed unused imports: displayError, handleAsync
import { router } from 'expo-router';
import React, { useState } from 'react';
import {
  ActivityIndicator,
  Animated,
  Image,
  ImageBackground,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';

// Comprehensive password validation function
function validatePassword(password: string): string | null {
  if (password.length < 8) {
    return 'Password must be at least 8 characters long';
  }

  if (!/[A-Z]/.test(password)) {
    return 'Password must contain at least one uppercase letter';
  }

  if (!/[a-z]/.test(password)) {
    return 'Password must contain at least one lowercase letter';
  }

  if (!/[0-9]/.test(password)) {
    return 'Password must contain at least one number';
  }

  if (!/[^A-Za-z0-9]/.test(password)) {
    return 'Password must contain at least one special character';
  }

  // Check for common weak patterns
  const commonPatterns = [
    /(.)\1{2,}/, // repeated characters (aaa, 111)
    /123|abc|qwe/i, // sequential patterns
    /password|admin|user|test/i, // common weak passwords
  ];

  for (const pattern of commonPatterns) {
    if (pattern.test(password)) {
      return 'Password contains common patterns and is not secure';
    }
  }

  return null; // Password is valid
}

export default function AuthScreen() {
  const colorScheme = 'light';
  const colors = Colors[colorScheme];
  const styles = createStyles(colors);
  const { signIn, signUp, resetPassword } = useAuth();

  const [mode, setMode] = useState<'login' | 'register' | 'forgot'>('login');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [name, setName] = useState('');
  const [rememberMe, setRememberMe] = useState(false);
  const [loading, setLoading] = useState(false);
  const [showAlert, setShowAlert] = useState(false);
  const [alertConfig, setAlertConfig] = useState({
    title: '',
    message: '',
    onConfirm: () => {},
  });

  // Animation value for sliding between modes
  const slideAnim = new Animated.Value(0);

  const switchMode = (newMode: 'login' | 'register' | 'forgot') => {
    let animValue = 0;
    if (newMode === 'register') animValue = 1;
    if (newMode === 'forgot') animValue = 2;

    Animated.timing(slideAnim, {
      toValue: animValue,
      duration: 300,
      useNativeDriver: true,
    }).start();
    setMode(newMode);
    // Clear form when switching
    setEmail('');
    setPassword('');
    setConfirmPassword('');
    setName('');
  };

  const handleSubmit = async () => {
    if (!email) {
      setAlertConfig({
        title: 'Error',
        message: 'Please enter your email address',
        onConfirm: () => setShowAlert(false),
      });
      setShowAlert(true);
      return;
    }

    // Validate email format
    const emailValidation = validateEmail(email);
    if (!emailValidation.isValid) {
      setAlertConfig({
        title: 'Invalid Email',
        message: emailValidation.error || 'Please enter a valid email address',
        onConfirm: () => setShowAlert(false),
      });
      setShowAlert(true);
      return;
    }

    if (mode === 'forgot') {
      setLoading(true);
      try {
        await resetPassword(email);
        setAlertConfig({
          title: 'Password Reset',
          message:
            'If an account with this email exists, you will receive a password reset link shortly.',
          onConfirm: () => {
            setShowAlert(false);
            switchMode('login');
          },
        });
        setShowAlert(true);
      } catch (error: any) {
        setAlertConfig({
          title: 'Error',
          message: error.message || 'Failed to send reset email',
          onConfirm: () => setShowAlert(false),
        });
        setShowAlert(true);
      } finally {
        setLoading(false);
      }
      return;
    }

    if (!password) {
      setAlertConfig({
        title: 'Error',
        message: 'Please enter your password',
        onConfirm: () => setShowAlert(false),
      });
      setShowAlert(true);
      return;
    }

    if (mode === 'register') {
      // Validate name
      const nameValidation = validateName(name);
      if (!nameValidation.isValid) {
        setAlertConfig({
          title: 'Invalid Name',
          message: nameValidation.error || 'Please enter a valid name',
          onConfirm: () => setShowAlert(false),
        });
        setShowAlert(true);
        return;
      }
      if (!confirmPassword) {
        setAlertConfig({
          title: 'Error',
          message: 'Please confirm your password',
          onConfirm: () => setShowAlert(false),
        });
        setShowAlert(true);
        return;
      }
      if (password !== confirmPassword) {
        setAlertConfig({
          title: 'Error',
          message: 'Passwords do not match',
          onConfirm: () => setShowAlert(false),
        });
        setShowAlert(true);
        return;
      }
      const passwordError = validatePassword(password);
      if (passwordError) {
        setAlertConfig({
          title: 'Password Requirements',
          message: passwordError,
          onConfirm: () => setShowAlert(false),
        });
        setShowAlert(true);
        return;
      }
    }

    setLoading(true);
    try {
      if (mode === 'login') {
        await signIn(email, password);
        // Let index.tsx handle the routing based on subscription status
        router.replace('/');
      } else {
        await signUp(email, password, name.trim());
        // After registration, go to paywall first
        router.replace('/(paywall)/paywall');
      }
    } catch (error: any) {
      setAlertConfig({
        title: mode === 'login' ? 'Login Error' : 'Registration Error',
        message:
          error.message ||
          `Failed to ${mode === 'login' ? 'sign in' : 'create account'}`,
        onConfirm: () => setShowAlert(false),
      });
      setShowAlert(true);
    } finally {
      setLoading(false);
    }
  };

  const handleSocialAuth = (provider: 'google' | 'apple') => {
    setAlertConfig({
      title: 'Coming Soon',
      message: `${provider} ${
        mode === 'login' ? 'login' : 'signup'
      } will be available soon!`,
      onConfirm: () => setShowAlert(false),
    });
    setShowAlert(true);
  };

  return (
    <ImageBackground
      source={require('@/assets/bg-preference.jpg')}
      style={styles.container}
      resizeMode="cover"
    >
      <View style={styles.overlay} />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 0}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.card}>
            <View style={styles.header}>
              <Image
                source={require('@/assets/images/logo.png')}
                style={styles.logo}
                resizeMode="contain"
              />
              <Text style={styles.subtitle}>
                {mode === 'login'
                  ? 'Your personal meal planning assistant'
                  : mode === 'register'
                    ? 'Start your meal planning journey'
                    : 'Reset your password'}
              </Text>
            </View>

            {/* Tab Switcher - Only show for login/register */}
            {mode !== 'forgot' && (
              <View style={styles.tabContainer}>
                <TouchableOpacity
                  style={[styles.tab, mode === 'login' && styles.activeTab]}
                  onPress={() => switchMode('login')}
                >
                  <Text
                    style={[
                      styles.tabText,
                      mode === 'login' && styles.activeTabText,
                    ]}
                  >
                    Login
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.tab, mode === 'register' && styles.activeTab]}
                  onPress={() => switchMode('register')}
                >
                  <Text
                    style={[
                      styles.tabText,
                      mode === 'register' && styles.activeTabText,
                    ]}
                  >
                    Sign Up
                  </Text>
                </TouchableOpacity>
              </View>
            )}

            {/* Back button for forgot password */}
            {mode === 'forgot' && (
              <TouchableOpacity
                style={styles.backButton}
                onPress={() => switchMode('login')}
              >
                <Text style={styles.backButtonText}>← Back to Login</Text>
              </TouchableOpacity>
            )}

            <View style={styles.form}>
              {/* Name Input - Only for Register */}
              {mode === 'register' && (
                <View style={styles.inputContainer}>
                  <TextInput
                    style={[
                      styles.input,
                      {
                        color: colors.text,
                        borderColor: colors.border,
                        backgroundColor: colors.cardBackground,
                      },
                    ]}
                    placeholder="Full Name"
                    placeholderTextColor={colors.icon}
                    value={name}
                    onChangeText={setName}
                    autoCapitalize="words"
                  />
                </View>
              )}

              {/* Email Input */}
              <View style={styles.inputContainer}>
                <TextInput
                  style={[
                    styles.input,
                    {
                      color: colors.text,
                      borderColor: colors.border,
                      backgroundColor: colors.cardBackground,
                    },
                  ]}
                  placeholder="Email"
                  placeholderTextColor={colors.icon}
                  value={email}
                  onChangeText={setEmail}
                  keyboardType="email-address"
                  autoCapitalize="none"
                />
              </View>

              {/* Password Input - Not for forgot password */}
              {mode !== 'forgot' && (
                <View style={styles.inputContainer}>
                  <TextInput
                    style={[
                      styles.input,
                      {
                        color: colors.text,
                        borderColor: colors.border,
                        backgroundColor: colors.cardBackground,
                      },
                    ]}
                    placeholder="Password"
                    placeholderTextColor={colors.icon}
                    value={password}
                    onChangeText={setPassword}
                    secureTextEntry
                    autoCapitalize="none"
                  />
                </View>
              )}

              {/* Confirm Password Input - Only for Register */}
              {mode === 'register' && (
                <View style={styles.inputContainer}>
                  <TextInput
                    style={[
                      styles.input,
                      {
                        color: colors.text,
                        borderColor: colors.border,
                        backgroundColor: colors.cardBackground,
                      },
                    ]}
                    placeholder="Confirm Password"
                    placeholderTextColor={colors.icon}
                    value={confirmPassword}
                    onChangeText={setConfirmPassword}
                    secureTextEntry
                    autoCapitalize="none"
                  />
                </View>
              )}

              {/* Remember Me / Forgot Password - Only for Login */}
              {mode === 'login' && (
                <Animated.View
                  style={[
                    styles.rememberContainer,
                    {
                      opacity: slideAnim.interpolate({
                        inputRange: [0, 1, 2],
                        outputRange: [1, 0, 0],
                      }),
                    },
                  ]}
                >
                  <TouchableOpacity
                    style={styles.checkbox}
                    onPress={() => setRememberMe(!rememberMe)}
                  >
                    <View
                      style={[
                        styles.checkboxInner,
                        {
                          borderColor: colors.border,
                          backgroundColor: rememberMe
                            ? colors.primary
                            : 'transparent',
                        },
                      ]}
                    />
                  </TouchableOpacity>
                  <Text style={styles.rememberText}>Remember me</Text>
                  <View style={{ flex: 1 }} />
                  <TouchableOpacity onPress={() => switchMode('forgot')}>
                    <Text
                      style={[styles.forgotText, { color: colors.primary }]}
                    >
                      Forgot Password?
                    </Text>
                  </TouchableOpacity>
                </Animated.View>
              )}

              {/* Forgot Password Instructions */}
              {mode === 'forgot' && (
                <Animated.View
                  style={[
                    styles.forgotInstructions,
                    {
                      opacity: slideAnim.interpolate({
                        inputRange: [0, 1, 2],
                        outputRange: [0, 0, 1],
                      }),
                    },
                  ]}
                >
                  <Text style={styles.forgotInstructionsText}>
                    Enter your email address and we&apos;ll send you a link to
                    reset your password.
                  </Text>
                </Animated.View>
              )}

              {/* Submit Button */}
              <TouchableOpacity
                style={[
                  styles.submitButton,
                  { backgroundColor: colors.primary },
                ]}
                onPress={handleSubmit}
                disabled={loading}
              >
                {loading ? (
                  <ActivityIndicator color="#FFFFFF" />
                ) : (
                  <Text style={styles.submitButtonText}>
                    {mode === 'login'
                      ? 'Login'
                      : mode === 'register'
                        ? 'Create Account'
                        : 'Send Reset Link'}
                  </Text>
                )}
              </TouchableOpacity>


              {/* Terms - Only for Register */}
              {mode === 'register' && (
                <Animated.View
                  style={[
                    styles.termsContainer,
                    {
                      opacity: slideAnim.interpolate({
                        inputRange: [0, 1, 2],
                        outputRange: [0, 1, 0],
                      }),
                    },
                  ]}
                >
                  <Text style={styles.termsText}>
                    By signing up, you agree to our{' '}
                    <Text style={[styles.termsLink, { color: colors.primary }]}>
                      Terms of Service
                    </Text>{' '}
                    and{' '}
                    <Text style={[styles.termsLink, { color: colors.primary }]}>
                      Privacy Policy
                    </Text>
                  </Text>
                </Animated.View>
              )}
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>

      <CustomAlert
        visible={showAlert}
        title={alertConfig.title}
        message={alertConfig.message}
        onConfirm={alertConfig.onConfirm}
        onCancel={() => setShowAlert(false)}
      />
    </ImageBackground>
  );
}

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    flex: 1,
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.2)',
  },
  keyboardView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 40,
    minHeight: '100%',
  },
  card: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 23,
    padding: 24,
    width: '100%',
    maxWidth: 400,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
  },
  logo: {
    width: 80,
    height: 80,
    marginBottom: 16,
    backgroundColor: '#FFFFFF',
    borderRadius: 6,
    padding: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#636E72',
    textAlign: 'center',
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#F5F5F5',
    borderRadius: 6,
    padding: 4,
    marginBottom: 24,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 4,
  },
  activeTab: {
    backgroundColor: colors.primary,
  },
  tabText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#636E72',
  },
  activeTabText: {
    color: '#FFFFFF',
  },
  form: {
    width: '100%',
  },
  inputContainer: {
    marginBottom: 16,
  },
  input: {
    borderWidth: 1,
    borderRadius: 6,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
  },
  rememberContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
  },
  checkbox: {
    marginRight: 8,
  },
  checkboxInner: {
    width: 20,
    height: 20,
    borderWidth: 2,
    borderRadius: 4,
  },
  rememberText: {
    fontSize: 14,
    color: '#2D3436',
  },
  forgotText: {
    fontSize: 14,
  },
  submitButton: {
    borderRadius: 6,
    paddingVertical: 14,
    alignItems: 'center',
    marginBottom: 24,
  },
  submitButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  dividerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
  },
  divider: {
    flex: 1,
    height: 1,
  },
  dividerText: {
    marginHorizontal: 16,
    fontSize: 14,
    color: '#636E72',
  },
  socialButton: {
    borderWidth: 1,
    borderRadius: 6,
    paddingVertical: 14,
    alignItems: 'center',
    marginBottom: 16,
  },
  socialButtonText: {
    fontSize: 16,
    color: '#2D3436',
  },
  termsContainer: {
    marginTop: 8,
    paddingHorizontal: 16,
  },
  termsText: {
    fontSize: 12,
    textAlign: 'center',
    lineHeight: 18,
    color: '#636E72',
  },
  termsLink: {
    textDecorationLine: 'underline',
  },
  backButton: {
    alignSelf: 'flex-start',
    marginBottom: 24,
    paddingVertical: 8,
  },
  backButtonText: {
    fontSize: 16,
    color: colors.primary,
    fontWeight: '600',
  },
  forgotInstructions: {
    marginBottom: 24,
    paddingHorizontal: 8,
  },
  forgotInstructionsText: {
    fontSize: 14,
    color: '#636E72',
    textAlign: 'center',
    lineHeight: 20,
  },
});
