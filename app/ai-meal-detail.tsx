import { CustomAlert } from '@/components/CustomAlert';
import { EditIngredientsModal } from '@/components/EditIngredientsModal';
import { ThemedText } from '@/components/ThemedText';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { ZenMode } from '@/components/ZenMode';
import { Colors } from '@/constants/Colors';
import { useAuth } from '@/contexts/AuthContext';
import { useTheme } from '@/contexts/ThemeContext';
import { getIngredientEmoji } from '@/lib/ingredientEmoji';
import { getMealCategoryIcons } from '@/lib/mealIcons';
// AI recipes don't support ingredient scaling by serving size
import { supabase } from '@/lib/supabase';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { router, Stack, useLocalSearchParams } from 'expo-router';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import {
  ActivityIndicator,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { Image } from 'expo-image';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

function AiMealDetailScreen() {
  const { activeTheme } = useTheme();
  const colors = Colors[activeTheme];
  const styles = createStyles(colors);
  const insets = useSafeAreaInsets();
  const params = useLocalSearchParams();
  const { session } = useAuth();
  const [showAlert, setShowAlert] = useState(false);
  const [alertConfig, setAlertConfig] = useState({ title: '', message: '' });
  const [mealData, setMealData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [addingToShoppingList, setAddingToShoppingList] = useState(false);
  const [isAlreadyAdded, setIsAlreadyAdded] = useState(false);
  const [showEditIngredientsModal, setShowEditIngredientsModal] =
    useState(false);
  const [hasCustomizedIngredients, setHasCustomizedIngredients] =
    useState(false);
  const [originalIngredients, setOriginalIngredients] = useState<any[]>([]);
  const [showZenMode, setShowZenMode] = useState(false);
  // AI recipes don't have serving size data, so we can't adjust it

  const mealId = params.mealId as string;
  const generatedRecipeParam = params.generatedRecipe as string;

  // Parse AI-generated recipe data if provided
  const aiGeneratedRecipe = useMemo(() => {
    if (!generatedRecipeParam) return null;
    try {
      return JSON.parse(decodeURIComponent(generatedRecipeParam));
    } catch {
      return null;
    }
  }, [generatedRecipeParam]);

  // Parse ingredients from AI format
  const parseIngredients = useCallback((ingredients: any) => {
    if (!ingredients) return [];

    let parsedIngredients: { name: string; amount: string; emoji: string }[] =
      [];

    try {
      if (Array.isArray(ingredients)) {
        parsedIngredients = ingredients
          .map((ingredient: any) => {
            if (typeof ingredient === 'object' && ingredient !== null) {
              // Handle the database format: {name, unit, amount, emoji}
              const name = ingredient.name || '';
              const emoji = ingredient.emoji || getIngredientEmoji(name);

              // Check if amount is already a combined string (from customized ingredients)
              const amountValue = ingredient.amount;
              const unit = ingredient.unit || '';
              let amountText = '';

              // Handle different amount value types from database
              if (typeof amountValue === 'number') {
                // Database format: numeric amount with separate unit field
                if (amountValue === 0 && !unit.trim()) {
                  // Skip ingredients with 0 amount and no unit (like Salt and Black Pepper)
                  amountText = '';
                } else if (amountValue > 0) {
                  // Show amount with unit if available
                  amountText = unit.trim() 
                    ? `${amountValue} ${unit}`.trim()
                    : amountValue.toString();
                }
              } else if (typeof amountValue === 'string' && amountValue.trim()) {
                // Handle string amount values (from customized ingredients)
                if (/[a-zA-Z]/.test(amountValue)) {
                  // Already combined format like "4 lbs" or "0.5 tsp" - use as-is
                  amountText = amountValue.trim();
                } else {
                  // Pure number string with separate unit field
                  const amount = parseFloat(amountValue) || 0;
                  if (amount === 0 && !unit.trim()) {
                    amountText = '';
                  } else if (amount > 0) {
                    amountText = unit.trim() 
                      ? `${amount} ${unit}`.trim()
                      : amount.toString();
                  }
                }
              }

              return { name, amount: amountText, emoji };
            } else {
              // Handle string format (fallback for AI-generated recipes)
              const text =
                typeof ingredient === 'string'
                  ? ingredient
                  : String(ingredient);
              const match = text.match(
                /^(\d+\.?\d*\s*(?:tbsp|tsp|cup|cups|lb|lbs|oz|g|kg|ml|l|slice|slices|clove|cloves|pieces?|whole|half|quarter)?)\s*(.+)$/i
              );
              if (match) {
                return {
                  name: match[2].trim(),
                  amount: match[1].trim(),
                  emoji: getIngredientEmoji(match[2]),
                };
              } else {
                return {
                  name: text,
                  amount: '',
                  emoji: getIngredientEmoji(text),
                };
              }
            }
          })
          .filter((item) => item.name && item.name.length > 0);
      } else if (typeof ingredients === 'string') {
        // Handle string format (split by newlines)
        parsedIngredients = ingredients
          .split('\n')
          .filter(Boolean)
          .map((text) => {
            const match = text.match(
              /^(\d+\.?\d*\s*(?:tbsp|tsp|cup|cups|lb|lbs|oz|g|kg|ml|l|slice|slices|clove|cloves|pieces?|whole|half|quarter)?)\s*(.+)$/i
            );
            if (match) {
              return {
                name: match[2].trim(),
                amount: match[1].trim(),
                emoji: getIngredientEmoji(match[2]),
              };
            } else {
              return {
                name: text,
                amount: '',
                emoji: getIngredientEmoji(text),
              };
            }
          });
      }
    } catch {}

    return parsedIngredients;
  }, []);

  // Use actual AI data or fallback to mock data
  const mealDetails = useMemo(() => {
    if (!mealData) {
      return {
        name: 'Loading...',
        prepTime: '...',
        cookTime: '...',
        servingSize: '...',
        description: 'Loading AI recipe details...',
        ingredients: [] as { name: string; amount: string; emoji: string }[],
        instructions: [],
        course: 'Main Course',
      };
    }

    return {
      name: mealData.name || 'AI Recipe',
      prepTime: mealData.prep_time || mealData.prepTime || 'N/A',
      cookTime: mealData.cook_time || mealData.cookTime || 'N/A',
      servingSize: 4, // Default serving size for AI recipes
      description:
        'A delicious AI-generated recipe tailored to your preferences.',
      ingredients: (() => {
        // Use the meal data ingredients (which might be manually customized)
        return parseIngredients(mealData.ingredients);
      })(),
      instructions: mealData.instructions
        ? typeof mealData.instructions === 'string'
          ? mealData.instructions.split('\n').filter(Boolean)
          : Array.isArray(mealData.instructions)
            ? mealData.instructions.filter(Boolean)
            : ['Follow the recipe instructions.']
        : ['Follow the recipe instructions.'],
      course: mealData.course || 'Main Course',
    };
  }, [mealData, parseIngredients, originalIngredients]);

  const checkIfAlreadyAdded = useCallback(
    async (currentMealData?: any) => {
      if (!session?.user?.id) return;

      const dataToCheck = currentMealData || mealData;
      if (!dataToCheck) return;

      try {
        const { data: mealPlan, error } = await supabase
          .from('weekly_meal_plans')
          .select('ai_generated_meals')
          .eq('user_id', session.user.id)
          .eq('is_active', true)
          .single();

        if (
          !error &&
          mealPlan?.ai_generated_meals &&
          Array.isArray(mealPlan.ai_generated_meals)
        ) {
          const isAdded = (mealPlan.ai_generated_meals as any[]).some(
            (meal: any) =>
              meal.id === dataToCheck.id || meal.name === dataToCheck.name
          );
          setIsAlreadyAdded(isAdded);
        }
      } catch {}
    },
    [session?.user?.id, mealData]
  );

  const getEquipmentIcon = (equipment: string) => {
    const equipmentMap: Record<string, any> = {
      'oven': require('@/assets/icons/icon-oven.png'),
      'skillet': require('@/assets/icons/icon-skillet.png'),
      'pot': require('@/assets/icons/icon-pot.png'),
      'grill': require('@/assets/icons/icon-grill.png'),
      'microwave': require('@/assets/icons/icon-microwave.png'),
      'crockpot': require('@/assets/icons/icon-crockpot.png'),
      'slow cooker': require('@/assets/icons/icon-crockpot.png'),
      'bowl': require('@/assets/icons/icon-bowl.png'),
      'mixing bowl': require('@/assets/icons/icon-bowl.png'),
    };
    
    const equipmentLower = equipment.toLowerCase();
    return equipmentMap[equipmentLower] || null;
  };

  const loadAiMealData = useCallback(async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('ai_generated_meals')
        .select('*')
        .eq('id', mealId)
        .single();

      if (error) throw error;

      // Store original ingredients - ensure proper type casting
      setOriginalIngredients(
        Array.isArray(data.ingredients) ? data.ingredients : []
      );

      // Check for customized ingredients
      let finalData = data;
      let hasCustomizations = false;

      if (session?.user?.id) {
        try {
          const { data: mealPlan, error: planError } = await supabase
            .from('weekly_meal_plans')
            .select('user_customized_ingredients')
            .eq('user_id', session.user.id)
            .eq('is_active', true)
            .single();

          if (!planError && mealPlan?.user_customized_ingredients) {
            const customizations =
              mealPlan.user_customized_ingredients as Record<string, any>;
            const customization = customizations?.[mealId];

            if (customization) {
              if (customization.ingredients) {
                hasCustomizations = true;
                finalData = {
                  ...data,
                  ingredients: customization.ingredients,
                };
              }
              // AI recipes don't support serving size adjustment
            }
          }
        } catch {
          // Ignore errors loading customizations
        }
      }

      setMealData(finalData);
      setHasCustomizedIngredients(hasCustomizations);
      checkIfAlreadyAdded(finalData);
    } catch {
      setAlertConfig({
        title: 'Error',
        message: 'Failed to load AI recipe details',
      });
      setShowAlert(true);
    } finally {
      setLoading(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [mealId, session?.user?.id]);

  useEffect(() => {
    if (aiGeneratedRecipe) {
      // Use the passed recipe data and ensure it has a consistent ID
      const recipeWithId = {
        ...aiGeneratedRecipe,
        id: aiGeneratedRecipe.id || mealId || `ai_${Date.now()}`,
      };
      setOriginalIngredients(recipeWithId.ingredients || []);
      setMealData(recipeWithId);
      setLoading(false);
      checkIfAlreadyAdded(recipeWithId);
    } else if (mealId) {
      // Fetch from database
      loadAiMealData();
    } else {
      setLoading(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [mealId, aiGeneratedRecipe]);

  const addToShoppingListAndMealPlan = async () => {
    if (!session?.user?.id) {
      setAlertConfig({
        title: 'Sign In Required',
        message: 'Please sign in to add ingredients to your shopping list.',
      });
      setShowAlert(true);
      return;
    }

    if (!mealData) {
      setAlertConfig({ title: 'Error', message: 'AI recipe data not loaded' });
      setShowAlert(true);
      return;
    }

    try {
      setAddingToShoppingList(true);

      // Get current week's meal plan
      const { data: mealPlan, error: planError } = await supabase
        .from('weekly_meal_plans')
        .select('id, ai_generated_meals')
        .eq('user_id', session.user.id)
        .eq('is_active', true)
        .single();

      if (planError) {
        setAlertConfig({
          title: 'No Meal Plan Found',
          message: 'You need an active meal plan to add AI recipe ingredients.',
        });
        setShowAlert(true);
        return;
      }

      const existingAiMeals = Array.isArray(mealPlan.ai_generated_meals)
        ? mealPlan.ai_generated_meals
        : [];

      // Check if this AI-generated meal is already added
      const isAlreadyAdded = existingAiMeals.some(
        (meal: any) => meal.id === mealData.id || meal.name === mealData.name
      );
      if (isAlreadyAdded) {
        setAlertConfig({
          title: 'Already Added',
          message: 'This AI recipe is already in your meal plan.',
        });
        setShowAlert(true);
        return;
      }

      // Add AI-generated meal to dedicated field
      const newAiMeal = {
        id: mealData.id || `ai_${Date.now()}`,
        name: mealData.name,
        ingredients: mealData.ingredients,
        instructions: mealData.instructions,
        prep_time: mealData.prep_time || mealData.prepTime || '',
        cook_time: mealData.cook_time || mealData.cookTime || '',
        course: mealData.course || 'Main Course',
        added_at: new Date().toISOString(),
        is_ai_generated: true,
      };

      // Update the meal plan with AI-generated meals in dedicated field
      const { error: updateError } = await supabase
        .from('weekly_meal_plans')
        .update({
          ai_generated_meals: [...existingAiMeals, newAiMeal],
          updated_at: new Date().toISOString(),
        })
        .eq('id', mealPlan.id);

      if (updateError) throw updateError;

      // Trigger shopping list refresh
      await AsyncStorage.setItem('mealPlanUpdated', Date.now().toString());

      setAlertConfig({
        title: 'Added to Meal Plan',
        message: `"${mealData.name}" has been added to your meal plan and shopping list.`,
      });
      setShowAlert(true);

      // Update the status to show it's been added
      setIsAlreadyAdded(true);

      // Go back after a short delay
      setTimeout(() => {
        router.back();
      }, 1500);
    } catch {
      setAlertConfig({
        title: 'Error',
        message: 'Failed to add AI recipe to meal plan',
      });
      setShowAlert(true);
    } finally {
      setAddingToShoppingList(false);
    }
  };

  const removeAiMealFromMealPlan = async () => {
    if (!session?.user?.id || !mealData) {
      setAlertConfig({ title: 'Error', message: 'Unable to remove AI recipe' });
      setShowAlert(true);
      return;
    }

    try {
      setAddingToShoppingList(true);

      // Get current week's meal plan
      const { data: mealPlan, error: planError } = await supabase
        .from('weekly_meal_plans')
        .select('id, ai_generated_meals')
        .eq('user_id', session.user.id)
        .eq('is_active', true)
        .single();

      if (planError) {
        setAlertConfig({
          title: 'No Meal Plan Found',
          message: 'You need an active meal plan to remove AI recipes.',
        });
        setShowAlert(true);
        return;
      }

      const existingAiMeals = (mealPlan.ai_generated_meals as any[]) || [];

      // Remove this AI meal from ai_generated_meals
      const filteredAiMeals = existingAiMeals.filter(
        (meal: any) => meal.id !== mealData.id && meal.name !== mealData.name
      );

      // Update the meal plan
      const { error: updateError } = await supabase
        .from('weekly_meal_plans')
        .update({
          ai_generated_meals: filteredAiMeals,
          updated_at: new Date().toISOString(),
        })
        .eq('id', mealPlan.id);

      if (updateError) throw updateError;

      // Trigger shopping list refresh
      await AsyncStorage.setItem('mealPlanUpdated', Date.now().toString());

      setAlertConfig({
        title: 'Removed from Meal Plan',
        message: `"${mealData.name}" has been removed from your meal plan and shopping list.`,
      });
      setShowAlert(true);

      // Update the status to show it's been removed
      setIsAlreadyAdded(false);

      // Go back after a short delay
      setTimeout(() => {
        router.back();
      }, 1500);
    } catch {
      setAlertConfig({
        title: 'Error',
        message: 'Failed to remove AI recipe from meal plan',
      });
      setShowAlert(true);
    } finally {
      setAddingToShoppingList(false);
    }
  };

  const saveCustomizedIngredients = async (customizedIngredients: any[]) => {
    if (!session?.user?.id || !mealData || !mealData.id) return;

    try {
      // Get current meal plan
      const { data: mealPlan, error: planError } = await supabase
        .from('weekly_meal_plans')
        .select('id, user_customized_ingredients')
        .eq('user_id', session.user.id)
        .eq('is_active', true)
        .single();

      if (planError || !mealPlan) {
        setAlertConfig({
          title: 'Error',
          message: 'No active meal plan found',
        });
        setShowAlert(true);
        return;
      }

      // Update customized ingredients
      const existingCustomizations =
        (mealPlan.user_customized_ingredients as Record<string, any>) || {};
      const updatedCustomizations = {
        ...existingCustomizations,
        [mealData.id]: {
          ...(existingCustomizations[mealData.id] || {}),
          ingredients: customizedIngredients,
          originalMealId: mealData.id,
          // AI recipes don't have serving size adjustment
          updatedAt: new Date().toISOString(),
        },
      };

      const { error: updateError } = await supabase
        .from('weekly_meal_plans')
        .update({
          user_customized_ingredients: updatedCustomizations,
          updated_at: new Date().toISOString(),
        })
        .eq('id', mealPlan.id);

      if (updateError) throw updateError;

      // Update local state
      setHasCustomizedIngredients(true);

      // Update meal data with customized ingredients
      setMealData((prevData: any) => ({
        ...prevData,
        ingredients: customizedIngredients,
      }));

      // Trigger shopping list refresh
      await AsyncStorage.setItem('mealPlanUpdated', Date.now().toString());

      setAlertConfig({
        title: 'Ingredients Updated',
        message:
          'Your ingredient changes have been saved and will be reflected in your shopping list.',
      });
      setShowAlert(true);
    } catch {
      setAlertConfig({
        title: 'Error',
        message: 'Failed to save ingredient changes',
      });
      setShowAlert(true);
    }
  };

  const resetToOriginalIngredients = async () => {
    if (!session?.user?.id || !mealData) return;

    try {
      // Get current meal plan
      const { data: mealPlan, error: planError } = await supabase
        .from('weekly_meal_plans')
        .select('id, user_customized_ingredients')
        .eq('user_id', session.user.id)
        .eq('is_active', true)
        .single();

      if (planError || !mealPlan) {
        setAlertConfig({
          title: 'Error',
          message: 'No active meal plan found',
        });
        setShowAlert(true);
        return;
      }

      // Remove customization for this meal
      const existingCustomizations =
        (mealPlan.user_customized_ingredients as Record<string, any>) || {};
      const { [mealData.id]: removed, ...remainingCustomizations } =
        existingCustomizations;

      const { error: updateError } = await supabase
        .from('weekly_meal_plans')
        .update({
          user_customized_ingredients: remainingCustomizations,
          updated_at: new Date().toISOString(),
        })
        .eq('id', mealPlan.id);

      if (updateError) throw updateError;

      // Update local state
      setHasCustomizedIngredients(false);

      // Restore original ingredients
      setMealData((prevData: any) => ({
        ...prevData,
        ingredients: originalIngredients,
      }));

      // Trigger shopping list refresh
      await AsyncStorage.setItem('mealPlanUpdated', Date.now().toString());

      setAlertConfig({
        title: 'Ingredients Reset',
        message: 'Ingredients have been restored to the original recipe.',
      });
      setShowAlert(true);
    } catch {
      setAlertConfig({
        title: 'Error',
        message: 'Failed to reset ingredients',
      });
      setShowAlert(true);
    }
  };

  // AI recipes don't support serving size adjustment

  const handleBack = () => {
    router.back();
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <View style={[styles.header, { paddingTop: insets.top + 10 }]}>
          <TouchableOpacity style={styles.backButton} onPress={handleBack}>
            <IconSymbol name="chevron-left" size={24} color={colors.text} />
          </TouchableOpacity>
          <ThemedText type="title" style={styles.headerTitle}>
            AI Recipe Details
          </ThemedText>
          <View style={styles.headerActions} />
        </View>
        <View style={styles.loadingContainer}>
          <ThemedText>Loading AI recipe details...</ThemedText>
        </View>
      </View>
    );
  }

  if (!mealData) {
    return (
      <View style={styles.container}>
        <View style={[styles.header, { paddingTop: insets.top + 10 }]}>
          <TouchableOpacity style={styles.backButton} onPress={handleBack}>
            <IconSymbol name="chevron-left" size={24} color={colors.text} />
          </TouchableOpacity>
          <ThemedText type="title" style={styles.headerTitle}>
            AI Recipe Not Found
          </ThemedText>
          <View style={styles.headerActions} />
        </View>
        <View style={styles.loadingContainer}>
          <Ionicons name="warning-outline" size={64} color={colors.text} />
          <ThemedText style={styles.errorText}>AI recipe not found</ThemedText>
          <TouchableOpacity
            style={[styles.errorButton, { backgroundColor: colors.primary }]}
            onPress={() => router.back()}
          >
            <ThemedText
              style={[styles.backButtonText, { color: colors.background }]}
            >
              Go Back
            </ThemedText>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <>
      <Stack.Screen options={{ headerShown: false }} />
      <View style={styles.container}>
        {/* Header */}
        <View style={[styles.header, { paddingTop: insets.top + 10 }]}>
          <TouchableOpacity style={styles.backButton} onPress={handleBack}>
            <IconSymbol name="chevron-left" size={24} color={colors.text} />
          </TouchableOpacity>
          <ThemedText type="title" style={styles.headerTitle}>
            AI Recipe Details
          </ThemedText>
          <View style={styles.headerActions} />
        </View>

        <View style={styles.contentWrapper}>
          <ScrollView
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.scrollContent}
          >
            {/* Meal Info */}
            <View style={styles.content}>
              {/* AI Recipe Header */}
              <View style={styles.aiHeader}>
                <Ionicons name="sparkles" size={24} color={colors.primary} />
                <ThemedText style={styles.aiLabel}>
                  AI-Generated Recipe
                </ThemedText>
              </View>

              {/* Equipment Icons */}
              {mealData?.required_equipment && mealData.required_equipment.length > 0 && (
                <View style={styles.equipmentContainer}>
                  {mealData.required_equipment.map((equipment, idx) => {
                    const icon = getEquipmentIcon(equipment);
                    return icon ? (
                      <View key={idx} style={styles.equipmentItem}>
                        <Image
                          source={icon}
                          style={styles.equipmentIcon}
                          contentFit="contain"
                        />
                      </View>
                    ) : null;
                  })}
                </View>
              )}

              <ThemedText type="title" style={styles.mealName}>
                {mealDetails.name}
              </ThemedText>

              <ThemedText style={styles.description}>
                {mealDetails.description}
              </ThemedText>

              {/* Add to Meal Plan Button - Top */}
              <TouchableOpacity
                style={[
                  styles.addToMealPlanButtonTop,
                  {
                    backgroundColor: 'transparent',
                    borderColor: isAlreadyAdded ? '#ff4444' : colors.primary,
                    borderWidth: 1,
                    opacity: 1,
                  },
                ]}
                onPress={
                  isAlreadyAdded
                    ? removeAiMealFromMealPlan
                    : addToShoppingListAndMealPlan
                }
                disabled={addingToShoppingList}
              >
                {addingToShoppingList ? (
                  <ActivityIndicator
                    size="small"
                    color={isAlreadyAdded ? '#ff4444' : colors.primary}
                  />
                ) : (
                  <>
                    <Ionicons
                      name={isAlreadyAdded ? 'trash' : 'add-circle'}
                      size={20}
                      color={isAlreadyAdded ? '#ff4444' : colors.primary}
                    />
                    <ThemedText
                      style={[
                        styles.addToMealPlanButtonTopText,
                        { color: isAlreadyAdded ? '#ff4444' : colors.primary },
                      ]}
                    >
                      {isAlreadyAdded
                        ? 'Remove from Meal Plan'
                        : 'Add to Meal Plan & Shopping List'}
                    </ThemedText>
                  </>
                )}
              </TouchableOpacity>

              {/* Quick Stats */}
              <View style={styles.statsContainer}>
                <View style={styles.statItem}>
                  <IconSymbol
                    name="clock-outline"
                    size={20}
                    color={colors.primary}
                  />
                  <ThemedText style={styles.statLabel}>Prep</ThemedText>
                  <ThemedText style={styles.statValue}>
                    {mealDetails.prepTime}
                  </ThemedText>
                </View>
                <View style={styles.statItem}>
                  <IconSymbol name="fire" size={20} color={colors.primary} />
                  <ThemedText style={styles.statLabel}>Cook</ThemedText>
                  <ThemedText style={styles.statValue}>
                    {mealDetails.cookTime}
                  </ThemedText>
                </View>
                <View style={styles.statItem}>
                  <IconSymbol
                    name="account-group"
                    size={20}
                    color={colors.primary}
                  />
                  <ThemedText style={styles.statLabel}>Serves</ThemedText>
                  <ThemedText style={styles.statValue}>
                    {mealDetails.servingSize}
                  </ThemedText>
                </View>
              </View>

              {/* Category Icons */}
              {mealData && (
                <View style={styles.categoryIconsContainer}>
                  {(() => {
                    const icons = getMealCategoryIcons({
                      course: mealData.course || 'Main Course',
                      cuisine_type: 'International', // Not needed for AI meals
                      prep_method: 'Mixed', // Not needed for AI meals
                    });
                    const capitalizeFirst = (str: string) => {
                      return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
                    };
                    return (
                      <>
                        <View style={styles.categoryIcon}>
                          <ThemedText style={styles.categoryLabel}>
                            {capitalizeFirst(icons.cuisine.label)}
                          </ThemedText>
                        </View>
                        <View style={styles.categoryIcon}>
                          <ThemedText style={styles.categoryLabel}>
                            {capitalizeFirst(icons.prepMethod.label)}
                          </ThemedText>
                        </View>
                      </>
                    );
                  })()}
                </View>
              )}

              {/* Ingredients */}
              <View style={styles.section}>
                <View style={styles.ingredientsSectionHeader}>
                  <ThemedText type="subtitle" style={styles.sectionTitle}>
                    Ingredients
                  </ThemedText>
                  {hasCustomizedIngredients && (
                    <View
                      style={[
                        styles.customizedIndicator,
                        { backgroundColor: `${colors.primary}10` },
                      ]}
                    >
                      <Ionicons
                        name="pencil"
                        size={14}
                        color={colors.primary}
                      />
                      <ThemedText
                        style={[
                          styles.customizedText,
                          { color: colors.primary },
                        ]}
                      >
                        Customized
                      </ThemedText>
                    </View>
                  )}
                </View>
                <View style={styles.ingredientsColumn}>
                  {mealDetails.ingredients.map(
                    (
                      ingredient: {
                        name: string;
                        amount: string;
                        emoji: string;
                      },
                      index: number
                    ) => (
                      <View
                        key={index}
                        style={[
                          styles.ingredientContainer,
                          {
                            backgroundColor: colors.cardBackground,
                            borderColor: colors.border,
                          },
                        ]}
                      >
                        <Text style={styles.ingredientEmoji}>
                          {ingredient.emoji}
                        </Text>
                        <View style={styles.ingredientTextContainer}>
                          <ThemedText style={styles.ingredientName}>
                            {ingredient.name}
                          </ThemedText>
                          {ingredient.amount && String(ingredient.amount).trim() && (
                            <ThemedText style={styles.ingredientAmount}>
                              {ingredient.amount}
                            </ThemedText>
                          )}
                        </View>
                      </View>
                    )
                  )}
                </View>

                {/* Edit and Reset Buttons */}
                <View style={styles.ingredientActions}>
                  <TouchableOpacity
                    style={[
                      styles.ingredientActionButton,
                      {
                        backgroundColor: 'transparent',
                        borderColor: colors.primary,
                        borderWidth: 1,
                      },
                    ]}
                    onPress={() => setShowEditIngredientsModal(true)}
                  >
                    <Ionicons
                      name="pencil-outline"
                      size={16}
                      color={colors.primary}
                    />
                    <ThemedText
                      style={[
                        styles.ingredientActionText,
                        { color: colors.primary },
                      ]}
                    >
                      Edit Ingredients
                    </ThemedText>
                  </TouchableOpacity>

                  {hasCustomizedIngredients && (
                    <TouchableOpacity
                      style={[
                        styles.ingredientActionButton,
                        {
                          backgroundColor: colors.background,
                          borderColor: colors.border,
                          borderWidth: 1,
                        },
                      ]}
                      onPress={() => {
                        setAlertConfig({
                          title: 'Reset Ingredients',
                          message:
                            'This will restore the ingredients back to the original recipe. Your customizations will be lost. Continue?',
                        });
                        setShowAlert(true);
                      }}
                    >
                      <Ionicons
                        name="refresh-outline"
                        size={16}
                        color={colors.text}
                      />
                      <ThemedText
                        style={[styles.ingredientActionText, { opacity: 0.7 }]}
                      >
                        Reset
                      </ThemedText>
                    </TouchableOpacity>
                  )}
                </View>
              </View>

              {/* Instructions */}
              <View style={styles.section}>
                <View style={styles.instructionsSectionHeader}>
                  <ThemedText type="subtitle" style={styles.sectionTitle}>
                    Instructions
                  </ThemedText>
                  <TouchableOpacity
                    style={styles.zenModeButton}
                    onPress={() => setShowZenMode(true)}
                  >
                    <Ionicons
                      name="expand-outline"
                      size={20}
                      color={colors.primary}
                    />
                    <ThemedText style={styles.zenModeText}>
                      Cook Mode
                    </ThemedText>
                  </TouchableOpacity>
                </View>
                {mealDetails.instructions.map(
                  (instruction: string, index: number) => (
                    <View key={index} style={styles.instructionItem}>
                      <View
                        style={[
                          styles.stepNumber,
                          { backgroundColor: colors.primary },
                        ]}
                      >
                        <Text style={styles.stepNumberText}>{index + 1}</Text>
                      </View>
                      <ThemedText style={styles.instructionText}>
                        {instruction}
                      </ThemedText>
                    </View>
                  )
                )}
              </View>

              {/* Add to Meal Plan Button - Bottom */}
              <TouchableOpacity
                style={[
                  styles.addToMealPlanButton,
                  {
                    backgroundColor: 'transparent',
                    borderColor: isAlreadyAdded ? '#ff4444' : colors.primary,
                    borderWidth: 1,
                    opacity: 1,
                  },
                ]}
                onPress={
                  isAlreadyAdded
                    ? removeAiMealFromMealPlan
                    : addToShoppingListAndMealPlan
                }
                disabled={addingToShoppingList}
              >
                {addingToShoppingList ? (
                  <ActivityIndicator
                    size="small"
                    color={isAlreadyAdded ? '#ff4444' : colors.primary}
                  />
                ) : (
                  <>
                    <Ionicons
                      name={isAlreadyAdded ? 'trash' : 'add-circle'}
                      size={20}
                      color={isAlreadyAdded ? '#ff4444' : colors.primary}
                    />
                    <ThemedText
                      style={[
                        styles.addToMealPlanButtonText,
                        { color: isAlreadyAdded ? '#ff4444' : colors.primary },
                      ]}
                    >
                      {isAlreadyAdded
                        ? 'Remove from Meal Plan'
                        : 'Add to Meal Plan & Shopping List'}
                    </ThemedText>
                  </>
                )}
              </TouchableOpacity>
            </View>
          </ScrollView>
        </View>

        <CustomAlert
          visible={showAlert}
          title={alertConfig.title}
          message={alertConfig.message}
          onConfirm={() => {
            setShowAlert(false);
            if (alertConfig.title === 'Reset Ingredients') {
              resetToOriginalIngredients();
            }
          }}
          onCancel={() => setShowAlert(false)}
        />

        {/* Edit Ingredients Modal */}
        <EditIngredientsModal
          visible={showEditIngredientsModal}
          ingredients={mealDetails.ingredients}
          onClose={() => setShowEditIngredientsModal(false)}
          onSave={saveCustomizedIngredients}
          mealName={mealDetails.name}
        />

        {/* Zen Mode */}
        <ZenMode
          visible={showZenMode}
          instructions={mealDetails.instructions}
          onClose={() => setShowZenMode(false)}
          mealName={mealDetails.name}
        />
      </View>
    </>
  );
}

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    loadingContainer: {
      height: '100%',
      justifyContent: 'center',
      alignItems: 'center',
      padding: 24,
    },
    errorText: {
      fontSize: 18,
      marginTop: 16,
      marginBottom: 24,
      textAlign: 'center',
    },
    errorButton: {
      paddingHorizontal: 24,
      paddingVertical: 12,
      borderRadius: 8,
    },
    backButtonText: {
      fontSize: 16,
      fontWeight: '600',
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 24,
      paddingBottom: 16,
      justifyContent: 'space-between',
      backgroundColor: colors.background,
    },
    backButton: {
      padding: 8,
    },
    headerTitle: {
      flex: 1,
      textAlign: 'center',
      marginHorizontal: 16,
      fontSize: 18,
      fontWeight: '600',
    },
    headerActions: {
      flexDirection: 'row',
      gap: 8,
      width: 40, // Same width as back button for centering
    },
    contentWrapper: {
      flex: 1,
      backgroundColor: colors.background,
    },
    content: {
      paddingHorizontal: 24,
      paddingTop: 24,
    },
    aiHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 8,
      marginBottom: 16,
    },
    aiLabel: {
      fontSize: 14,
      color: colors.primary,
      fontWeight: '600',
      textTransform: 'uppercase',
      letterSpacing: 0.5,
    },
    mealName: {
      marginBottom: 12,
    },
    description: {
      fontSize: 16,
      lineHeight: 24,
      opacity: 0.7,
      marginBottom: 24,
    },
    statsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: 32,
      paddingVertical: 20,
    },
    statItem: {
      alignItems: 'center',
      flex: 1,
    },
    statLabel: {
      fontSize: 16,
      opacity: 0.6,
      marginTop: 4,
    },
    statValue: {
      fontSize: 18,
      fontWeight: '600',
      marginTop: 2,
    },
    section: {
      marginBottom: 32,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: '600',
    },
    ingredientsColumn: {
      gap: 12,
    },
    ingredientContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderRadius: 8,
      borderWidth: 1,
      gap: 12,
    },
    ingredientEmoji: {
      fontSize: 18,
    },
    ingredientTextContainer: {
      flex: 1,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    ingredientName: {
      fontSize: 16,
      fontWeight: '500',
      flex: 1,
    },
    ingredientAmount: {
      fontSize: 16,
      fontWeight: '600',
      color: colors.primary,
      marginLeft: 8,
    },
    instructionItem: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      marginBottom: 16,
    },
    stepNumber: {
      width: 28,
      height: 28,
      borderRadius: 14,
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: 12,
      marginTop: 2,
    },
    stepNumberText: {
      color: colors.background,
      fontSize: 14,
      fontWeight: '600',
    },
    instructionText: {
      flex: 1,
      fontSize: 16,
      lineHeight: 24,
      paddingTop: 3,
    },
    addToMealPlanButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 16,
      paddingHorizontal: 24,
      borderRadius: 12,
      marginBottom: 32,
      gap: 8,
    },
    addToMealPlanButtonText: {
      fontSize: 16,
      fontWeight: '600',
      color: colors.background,
    },
    addToMealPlanButtonTop: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 12,
      paddingHorizontal: 20,
      borderRadius: 8,
      marginBottom: 24,
      gap: 8,
    },
    addToMealPlanButtonTopText: {
      fontSize: 14,
      fontWeight: '600',
    },
    scrollContent: {
      paddingBottom: 24,
    },
    categoryIconsContainer: {
      flexDirection: 'row',
      gap: 12,
      marginBottom: 24,
      flexWrap: 'wrap',
    },
    categoryIcon: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 6,
      backgroundColor: `${colors.primary}10`,
      paddingHorizontal: 12,
      paddingVertical: 8,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: `${colors.primary}30`,
    },
    categoryLabel: {
      fontSize: 12,
      fontWeight: '500',
      color: colors.text,
      opacity: 0.9,
    },
    ingredientsSectionHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 16,
    },
    customizedIndicator: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 4,
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12,
    },
    customizedText: {
      fontSize: 12,
      fontWeight: '500',
    },
    ingredientActions: {
      flexDirection: 'row',
      gap: 12,
      marginTop: 16,
    },
    ingredientActionButton: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 12,
      paddingHorizontal: 16,
      borderRadius: 8,
      gap: 6,
    },
    ingredientActionText: {
      fontSize: 14,
      fontWeight: '600',
    },
    instructionsSectionHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 16,
    },
    zenModeButton: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 6,
      paddingHorizontal: 12,
      paddingVertical: 8,
      borderRadius: 16,
      backgroundColor: `${colors.primary}10`,
      borderWidth: 1,
      borderColor: `${colors.primary}30`,
    },
    zenModeText: {
      fontSize: 14,
      fontWeight: '500',
      color: colors.primary,
    },
    equipmentContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 8,
      marginBottom: 16,
    },
    equipmentItem: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: '#F5F5F5',
      borderWidth: 1,
      borderColor: '#E0E0E0',
      alignItems: 'center',
      justifyContent: 'center',
      padding: 4,
    },
    equipmentIcon: {
      width: 28,
      height: 28,
    },
    // Removed serving size adjustment styles as AI recipes don't support it
  });

export default function ProtectedAiMealDetailScreen() {
  return <AiMealDetailScreen />;
}
