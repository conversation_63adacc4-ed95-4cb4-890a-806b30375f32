import { CustomAlert } from '@/components/CustomAlert';
import { EditIngredientsModal } from '@/components/EditIngredientsModal';
import { ThemedText } from '@/components/ThemedText';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { ZenMode } from '@/components/ZenMode';
import { CircularProgress } from '@/components/CircularProgress';
import { Colors } from '@/constants/Colors';
import { useAuth } from '@/contexts/AuthContext';
import { useTheme } from '@/contexts/ThemeContext';
import { getIngredientEmoji } from '@/lib/ingredientEmoji';
import { getMealCategoryIcons } from '@/lib/mealIcons';
import { scaleIngredient, scaleIngredients } from '@/lib/ingredientScaling';
import { supabase } from '@/lib/supabase';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { router, Stack, useLocalSearchParams } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Image,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import type { Json } from '@/lib/database.types';

// Type definitions for better type safety
interface IngredientCustomization {
  ingredients: any[];
  originalMealId: string;
  adjustedServingSize?: number;
  originalServingSize?: number;
  updatedAt: string;
}

interface UserCustomizedIngredients {
  [mealId: string]: IngredientCustomization;
}

interface SideDishCustomization {
  ingredients: any[];
  originalSideDishId: string;
  adjustedServingSize?: number;
  originalServingSize?: number;
  updatedAt: string;
}

interface SelectedSideDishes {
  [key: string]: SideDishCustomization;
}

// Type guard functions
function isUserCustomizedIngredients(
  value: Json | null
): value is Record<string, any> {
  return value !== null && typeof value === 'object' && !Array.isArray(value);
}

function ensureUserCustomizedIngredients(
  value: Json | null
): Record<string, any> {
  if (isUserCustomizedIngredients(value)) {
    return value;
  }
  return {};
}

function isSelectedSideDishes(
  value: Json | null
): value is Record<string, any> {
  return value !== null && typeof value === 'object' && !Array.isArray(value);
}

function ensureSelectedSideDishes(value: Json | null): Record<string, any> {
  if (isSelectedSideDishes(value)) {
    return value;
  }
  return {};
}

function ensureArray(value: Json | null): any[] {
  if (Array.isArray(value)) {
    return value;
  }
  return [];
}

function SideDishDetailScreen() {
  const { activeTheme } = useTheme();
  const colors = Colors[activeTheme];
  const styles = createStyles(colors);
  const insets = useSafeAreaInsets();
  const params = useLocalSearchParams();
  const { session } = useAuth();
  const [showAlert, setShowAlert] = useState(false);
  const [alertConfig, setAlertConfig] = useState({ title: '', message: '' });
  const [mealData, setMealData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [addingToShoppingList, setAddingToShoppingList] = useState(false);
  const [isAlreadyAdded, setIsAlreadyAdded] = useState(false);
  const [showEditIngredientsModal, setShowEditIngredientsModal] =
    useState(false);
  const [hasCustomizedIngredients, setHasCustomizedIngredients] =
    useState(false);
  const [originalIngredients, setOriginalIngredients] = useState<any[]>([]);
  const [showZenMode, setShowZenMode] = useState(false);
  const [adjustedServingSize, setAdjustedServingSize] = useState<number | null>(
    null
  );
  const [isSavingServingSize, setIsSavingServingSize] = useState(false);

  const mealId = params.mealId as string;

  const formatTime = (hours: number | null, minutes: number | null) => {
    if (!hours && !minutes) return '0 min';
    const h = hours || 0;
    const m = minutes || 0;
    return h > 0 ? `${h}h ${m}m` : `${m} min`;
  };

  // Helper function to check if nutrition section should be hidden
  const shouldHideNutrition = (nutrition: any) => {
    if (!nutrition) return true;

    const calories = parseInt(nutrition.calories) || 0;
    const protein = parseInt(nutrition.protein) || 0;
    const carbs = parseInt(nutrition.carbs) || 0;
    const fat = parseInt(nutrition.fat) || 0;

    return calories === 0 && protein === 0 && carbs === 0 && fat === 0;
  };

  const getImageSource = (image: string | null) => {
    if (!image || image === 'default.jpg') {
      return require('@/assets/default.png');
    }

    // If it's already a full URL, use it as-is
    if (image.startsWith('http')) {
      return { uri: image };
    }

    // Construct Supabase Storage URL for meal images with cache busting
    const timestamp = Date.now();
    return {
      uri: `${process.env.EXPO_PUBLIC_SUPABASE_URL}/storage/v1/object/public/meals/${image}?t=${timestamp}`,
    };
  };

  // Parse ingredients from database format
  const parseIngredients = (ingredients: any) => {
    if (!ingredients) return [];

    let parsedIngredients: { name: string; amount: string; emoji: string }[] =
      [];

    try {
      if (Array.isArray(ingredients)) {
        // Handle array format from database
        parsedIngredients = ingredients
          .map((ingredient: any) => {
            if (typeof ingredient === 'object' && ingredient !== null) {
              // Handle the database format: {name, unit, amount, emoji}
              const name = ingredient.name || '';
              const emoji = ingredient.emoji || getIngredientEmoji(name);

              // Check if amount is already a combined string (from customized ingredients)
              const amountValue = ingredient.amount || '';
              let amountText = '';

              if (typeof amountValue === 'string' && amountValue.trim()) {
                // Check if this looks like a combined string (contains letters/units)
                if (/[a-zA-Z]/.test(amountValue)) {
                  // Already combined format like "4 lbs" or "0.5 tsp" - use as-is
                  amountText = amountValue.trim();
                } else if (ingredient.unit !== undefined) {
                  // Pure number with separate unit field (original database format)
                  const amount = parseFloat(amountValue) || 0;
                  const unit = ingredient.unit || '';
                  amountText =
                    amount > 0 && unit
                      ? `${amount} ${unit}`.trim()
                      : amount > 0
                        ? amount.toString()
                        : '';
                } else {
                  // Just a number, no unit
                  amountText = amountValue.trim();
                }
              } else if (typeof amountValue === 'number') {
                // Handle numeric amount (from database)
                if (ingredient.unit !== undefined) {
                  const unit = ingredient.unit || '';
                  amountText =
                    amountValue > 0 && unit
                      ? `${amountValue} ${unit}`.trim()
                      : amountValue > 0
                        ? amountValue.toString()
                        : '';
                } else {
                  amountText = amountValue > 0 ? amountValue.toString() : '';
                }
              }

              return { name, amount: amountText, emoji };
            } else {
              // Handle string format
              const text = String(ingredient);
              const match = text.match(
                /^(\d+\.?\d*\s*(?:tbsp|tsp|cup|cups|lb|lbs|oz|g|kg|ml|l|slice|slices|clove|cloves|pieces?|whole|half|quarter)?)\s*(.+)$/i
              );
              if (match) {
                return {
                  name: match[2].trim(),
                  amount: match[1].trim(),
                  emoji: getIngredientEmoji(match[2]),
                };
              } else {
                return {
                  name: text,
                  amount: '',
                  emoji: getIngredientEmoji(text),
                };
              }
            }
          })
          .filter((item) => item.name && item.name.length > 0);
      } else if (typeof ingredients === 'string') {
        // Handle string format (split by newlines)
        parsedIngredients = ingredients
          .split('\n')
          .filter(Boolean)
          .map((text) => {
            const match = text.match(
              /^(\d+\.?\d*\s*(?:tbsp|tsp|cup|cups|lb|lbs|oz|g|kg|ml|l|slice|slices|clove|cloves|pieces?|whole|half|quarter)?)\s*(.+)$/i
            );
            if (match) {
              return {
                name: match[2].trim(),
                amount: match[1].trim(),
                emoji: getIngredientEmoji(match[2]),
              };
            } else {
              return {
                name: text,
                amount: '',
                emoji: getIngredientEmoji(text),
              };
            }
          });
      }
    } catch (error) {
      console.error('Error parsing ingredients:', error);
    }

    // Fallback to mock data if no valid ingredients found
    if (parsedIngredients.length === 0) {
      parsedIngredients = [
        { name: 'olive oil', amount: '2 tbsp', emoji: '🫒' },
        { name: 'onion, diced', amount: '1', emoji: '🧅' },
        { name: 'garlic, minced', amount: '2 cloves', emoji: '🧄' },
        { name: 'salt and pepper to taste', amount: '', emoji: '🧂' },
      ];
    }

    return parsedIngredients;
  };

  // Use actual database data or fallback to mock data
  const mealDetails = mealData
    ? {
        name: mealData.name || 'Side Dish',
        prepTime: formatTime(mealData.prepTimeHour, mealData.prepTimeMin),
        cookTime: formatTime(mealData.cookTimeHour, mealData.cookTimeMin),
        servingSize: mealData.servingSize?.toString() || '4',
        description:
          mealData.description ||
          'A delicious side dish to complement your meal.',
        ingredients: (() => {
          // If serving size is adjusted, always scale from original ingredients
          if (
            adjustedServingSize &&
            mealData.servingSize &&
            adjustedServingSize !== mealData.servingSize
          ) {
            const baseIngredients = parseIngredients(
              originalIngredients.length > 0
                ? originalIngredients
                : mealData.ingredients
            );
            return scaleIngredients(
              baseIngredients,
              mealData.servingSize,
              adjustedServingSize
            );
          }
          // Otherwise, use the meal data ingredients (which might be manually customized)
          return parseIngredients(mealData.ingredients);
        })(),
        instructions: mealData.instructions
          ? Array.isArray(mealData.instructions)
            ? mealData.instructions.filter(Boolean)
            : typeof mealData.instructions === 'string'
              ? mealData.instructions.split('\n').filter(Boolean)
              : [
                  'Prepare ingredients as specified.',
                  'Follow standard cooking techniques.',
                  'Season to taste and serve.',
                ]
          : [
              'Prepare ingredients as specified.',
              'Follow standard cooking techniques.',
              'Season to taste and serve.',
            ],
        nutrition: {
          calories: (mealData.calories || 0).toString(),
          protein: `${mealData.protein || 0}g`,
          carbs: `${mealData.carbs || 0}g`,
          fat: `${mealData.fats || 0}g`,
        },
      }
    : {
        name: 'Loading...',
        prepTime: '...',
        cookTime: '...',
        servingSize: '...',
        description: 'Loading side dish details...',
        ingredients: [] as { name: string; amount: string; emoji: string }[],
        instructions: [],
        nutrition: {
          calories: '...',
          protein: '...',
          carbs: '...',
          fat: '...',
        },
      };

  useEffect(() => {
    if (mealId) {
      loadMealData();
      checkIfAlreadyAdded();
    }
  }, [mealId]);

  const checkIfAlreadyAdded = async () => {
    if (!session?.user?.id || !mealId) return;

    try {
      const { data: mealPlan, error } = await supabase
        .from('weekly_meal_plans')
        .select('selected_side_dishes')
        .eq('user_id', session.user.id)
        .eq('is_active', true)
        .single();

      if (!error && mealPlan?.selected_side_dishes) {
        const sideDishes = ensureArray(mealPlan.selected_side_dishes);
        const isAdded = sideDishes.some((dish: any) => dish.id === mealId);
        setIsAlreadyAdded(isAdded);
      }
    } catch (error) {
      console.error('Error checking if side dish is already added:', error);
    }
  };

  const loadMealData = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('meals')
        .select('*')
        .eq('id', mealId)
        .single();

      if (error) throw error;

      // Store original ingredients
      setOriginalIngredients(ensureArray(data.ingredients));

      // Check for customized ingredients
      let finalData = data;
      let hasCustomizations = false;
      let savedServingSize = null;

      if (session?.user?.id) {
        try {
          const { data: mealPlan, error: planError } = await supabase
            .from('weekly_meal_plans')
            .select('user_customized_ingredients')
            .eq('user_id', session.user.id)
            .eq('is_active', true)
            .single();

          if (!planError && mealPlan?.user_customized_ingredients) {
            const customizations = mealPlan.user_customized_ingredients as any;
            const customization = customizations[mealId];

            if (customization) {
              // Check if there's a saved serving size adjustment
              if (customization.adjustedServingSize) {
                savedServingSize = customization.adjustedServingSize;
                hasCustomizations = true;
                // Don't apply the customized ingredients yet - let the rendering logic handle the scaling
                finalData = data;
              } else if (customization.ingredients) {
                // This is a manual ingredient edit, not from serving size
                hasCustomizations = true;
                finalData = {
                  ...data,
                  ingredients: customization.ingredients,
                };
              }
            }
          }
        } catch {
          // Ignore errors loading customizations
        }
      }

      setMealData(finalData);
      setHasCustomizedIngredients(hasCustomizations);

      // Set saved serving size if available
      if (savedServingSize) {
        setAdjustedServingSize(savedServingSize);
      }
    } catch (error) {
      console.error('Error loading side dish data:', error);
      setAlertConfig({
        title: 'Error',
        message: 'Failed to load side dish details',
      });
      setShowAlert(true);
    } finally {
      setLoading(false);
    }
  };

  const addToShoppingListAndMealPlan = async () => {
    if (!session?.user?.id) {
      setAlertConfig({
        title: 'Sign In Required',
        message: 'Please sign in to add ingredients to your shopping list.',
      });
      setShowAlert(true);
      return;
    }

    if (!mealData) {
      setAlertConfig({ title: 'Error', message: 'Side dish data not loaded' });
      setShowAlert(true);
      return;
    }

    try {
      setAddingToShoppingList(true);

      // Get current week's meal plan
      const { data: mealPlan, error: planError } = await supabase
        .from('weekly_meal_plans')
        .select('id, additional_ingredients, selected_side_dishes')
        .eq('user_id', session.user.id)
        .eq('is_active', true)
        .single();

      if (planError) {
        setAlertConfig({
          title: 'No Meal Plan Found',
          message: 'You need an active meal plan to add side dish ingredients.',
        });
        setShowAlert(true);
        return;
      }

      const sideDishIngredients = mealData.ingredients || [];
      const existingAdditional = ensureArray(mealPlan.additional_ingredients);
      const existingSideDishes = ensureArray(mealPlan.selected_side_dishes);

      // Add source information to identify where these ingredients came from
      const newIngredients = {
        source: 'side_dish',
        source_id: mealId,
        source_name: mealData.name,
        ingredients: sideDishIngredients,
        added_at: new Date().toISOString(),
      };

      // Add side dish to selected list
      const newSideDish = {
        id: mealId,
        name: mealData.name,
        prep_time: formatTime(mealData.prepTimeHour, mealData.prepTimeMin),
        cook_time: formatTime(mealData.cookTimeHour, mealData.cookTimeMin),
        added_at: new Date().toISOString(),
      };

      // Check if this side dish is already added
      const isAlreadyAdded = existingSideDishes.some(
        (dish: any) => dish.id === mealId
      );
      if (isAlreadyAdded) {
        setAlertConfig({
          title: 'Already Added',
          message: 'This side dish is already in your meal plan.',
        });
        setShowAlert(true);
        return;
      }

      // Update the meal plan with additional ingredients and selected side dishes
      const { error: updateError } = await supabase
        .from('weekly_meal_plans')
        .update({
          additional_ingredients: [...existingAdditional, newIngredients],
          selected_side_dishes: [...existingSideDishes, newSideDish],
          updated_at: new Date().toISOString(),
        })
        .eq('id', mealPlan.id);

      if (updateError) throw updateError;

      // Trigger shopping list refresh
      await AsyncStorage.setItem('mealPlanUpdated', Date.now().toString());

      setAlertConfig({
        title: 'Added to Meal Plan',
        message: `"${mealData.name}" has been added to your meal plan and shopping list.`,
      });
      setShowAlert(true);

      // Update the status to show it's been added
      setIsAlreadyAdded(true);

      // Go back after a short delay
      setTimeout(() => {
        router.back();
      }, 1500);
    } catch (error) {
      console.error('Error adding side dish:', error);
      setAlertConfig({
        title: 'Error',
        message: 'Failed to add side dish to meal plan',
      });
      setShowAlert(true);
    } finally {
      setAddingToShoppingList(false);
    }
  };

  const removeSideDishFromMealPlan = async () => {
    if (!session?.user?.id || !mealData) {
      setAlertConfig({ title: 'Error', message: 'Unable to remove side dish' });
      setShowAlert(true);
      return;
    }

    try {
      setAddingToShoppingList(true);

      // Get current week's meal plan
      const { data: mealPlan, error: planError } = await supabase
        .from('weekly_meal_plans')
        .select('id, additional_ingredients, selected_side_dishes')
        .eq('user_id', session.user.id)
        .eq('is_active', true)
        .single();

      if (planError) {
        setAlertConfig({
          title: 'No Meal Plan Found',
          message: 'You need an active meal plan to remove side dishes.',
        });
        setShowAlert(true);
        return;
      }

      const existingAdditional = ensureArray(mealPlan.additional_ingredients);
      const existingSideDishes = ensureArray(mealPlan.selected_side_dishes);

      // Remove this side dish's ingredients from additional_ingredients
      const filteredIngredients = existingAdditional.filter(
        (item: any) => item.source_id !== mealId
      );

      // Remove this side dish from selected_side_dishes
      const filteredSideDishes = existingSideDishes.filter(
        (dish: any) => dish.id !== mealId
      );

      // Update the meal plan
      const { error: updateError } = await supabase
        .from('weekly_meal_plans')
        .update({
          additional_ingredients: filteredIngredients,
          selected_side_dishes: filteredSideDishes,
          updated_at: new Date().toISOString(),
        })
        .eq('id', mealPlan.id);

      if (updateError) throw updateError;

      // Trigger shopping list refresh
      await AsyncStorage.setItem('mealPlanUpdated', Date.now().toString());

      setAlertConfig({
        title: 'Removed from Meal Plan',
        message: `"${mealData.name}" has been removed from your meal plan and shopping list.`,
      });
      setShowAlert(true);

      // Update the status to show it's been removed
      setIsAlreadyAdded(false);

      // Go back after a short delay
      setTimeout(() => {
        router.back();
      }, 1500);
    } catch (error) {
      console.error('Error removing side dish:', error);
      setAlertConfig({
        title: 'Error',
        message: 'Failed to remove side dish from meal plan',
      });
      setShowAlert(true);
    } finally {
      setAddingToShoppingList(false);
    }
  };

  const saveCustomizedIngredients = async (customizedIngredients: any[]) => {
    if (!session?.user?.id || !mealData) return;

    try {
      // Get current meal plan
      const { data: mealPlan, error: planError } = await supabase
        .from('weekly_meal_plans')
        .select('id, user_customized_ingredients')
        .eq('user_id', session.user.id)
        .eq('is_active', true)
        .single();

      if (planError || !mealPlan) {
        setAlertConfig({
          title: 'Error',
          message: 'No active meal plan found',
        });
        setShowAlert(true);
        return;
      }

      // Update customized ingredients
      const existingCustomizations = ensureUserCustomizedIngredients(
        mealPlan.user_customized_ingredients
      );
      const updatedCustomizations = {
        ...existingCustomizations,
        [mealId]: {
          ingredients: customizedIngredients,
          originalMealId: mealId,
          updatedAt: new Date().toISOString(),
        },
      };

      const { error: updateError } = await supabase
        .from('weekly_meal_plans')
        .update({
          user_customized_ingredients: updatedCustomizations as Json,
          updated_at: new Date().toISOString(),
        })
        .eq('id', mealPlan.id);

      if (updateError) throw updateError;

      // Update local state
      setHasCustomizedIngredients(true);

      // Update meal data with customized ingredients
      setMealData({
        ...mealData,
        ingredients: customizedIngredients,
      });

      // Trigger shopping list refresh
      await AsyncStorage.setItem('mealPlanUpdated', Date.now().toString());

      setAlertConfig({
        title: 'Ingredients Updated',
        message:
          'Your ingredient changes have been saved and will be reflected in your shopping list.',
      });
      setShowAlert(true);
    } catch {
      setAlertConfig({
        title: 'Error',
        message: 'Failed to save ingredient changes',
      });
      setShowAlert(true);
    }
  };

  const resetToOriginalIngredients = async () => {
    if (!session?.user?.id || !mealData) return;

    try {
      // Get current meal plan
      const { data: mealPlan, error: planError } = await supabase
        .from('weekly_meal_plans')
        .select('id, user_customized_ingredients')
        .eq('user_id', session.user.id)
        .eq('is_active', true)
        .single();

      if (planError || !mealPlan) {
        setAlertConfig({
          title: 'Error',
          message: 'No active meal plan found',
        });
        setShowAlert(true);
        return;
      }

      // Remove customization for this meal
      const existingCustomizations = ensureUserCustomizedIngredients(
        mealPlan.user_customized_ingredients
      );
      const { [mealId]: removed, ...remainingCustomizations } =
        existingCustomizations;

      const { error: updateError } = await supabase
        .from('weekly_meal_plans')
        .update({
          user_customized_ingredients: remainingCustomizations as Json,
          updated_at: new Date().toISOString(),
        })
        .eq('id', mealPlan.id);

      if (updateError) throw updateError;

      // Update local state
      setHasCustomizedIngredients(false);
      setAdjustedServingSize(null);

      // Restore original ingredients
      setMealData({
        ...mealData,
        ingredients: originalIngredients,
      });

      // Trigger shopping list refresh
      await AsyncStorage.setItem('mealPlanUpdated', Date.now().toString());

      setAlertConfig({
        title: 'Ingredients Reset',
        message: 'Ingredients have been restored to the original recipe.',
      });
      setShowAlert(true);
    } catch {
      setAlertConfig({
        title: 'Error',
        message: 'Failed to reset ingredients',
      });
      setShowAlert(true);
    }
  };

  const handleServingSizeChange = async (newServingSize: number) => {
    setAdjustedServingSize(newServingSize);
    await saveServingSizeAdjustment(newServingSize);
  };

  const saveServingSizeAdjustment = async (newServingSize: number) => {
    if (!session?.user?.id || !mealId || !mealData || isSavingServingSize)
      return;

    // Don't save if it's the same as original
    if (newServingSize === mealData.servingSize) {
      return;
    }

    setIsSavingServingSize(true);
    try {
      // Get original serving size
      const originalServingSize = mealData.servingSize || 4;

      // Scale the ingredients
      const scaledIngredients = scaleIngredients(
        parseIngredients(originalIngredients),
        originalServingSize,
        newServingSize
      );

      // Get current meal plan
      const { data: mealPlan, error: planError } = await supabase
        .from('weekly_meal_plans')
        .select('id, user_customized_ingredients')
        .eq('user_id', session.user.id)
        .eq('is_active', true)
        .single();

      if (planError || !mealPlan) {
        console.error('No active meal plan found');
        return;
      }

      // Update customized ingredients with serving size info
      const existingCustomizations = ensureUserCustomizedIngredients(
        mealPlan.user_customized_ingredients
      );
      const updatedCustomizations = {
        ...existingCustomizations,
        [mealId]: {
          ingredients: scaledIngredients,
          originalMealId: mealId,
          adjustedServingSize: newServingSize,
          originalServingSize: originalServingSize,
          updatedAt: new Date().toISOString(),
        },
      };

      const { error: updateError } = await supabase
        .from('weekly_meal_plans')
        .update({
          user_customized_ingredients: updatedCustomizations as Json,
          updated_at: new Date().toISOString(),
        })
        .eq('id', mealPlan.id);

      if (updateError) throw updateError;

      // Update local state
      setHasCustomizedIngredients(true);

      // Trigger shopping list refresh
      await AsyncStorage.setItem('mealPlanUpdated', Date.now().toString());
    } catch (error) {
      console.error('Failed to save serving size adjustment:', error);
    } finally {
      setIsSavingServingSize(false);
    }
  };

  const handleBack = () => {
    router.back();
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <View style={[styles.header, { paddingTop: insets.top + 10 }]}>
          <TouchableOpacity style={styles.backButton} onPress={handleBack}>
            <IconSymbol name="chevron-left" size={24} color={colors.text} />
          </TouchableOpacity>
          <ThemedText type="title" style={styles.headerTitle}>
            Side Dish Details
          </ThemedText>
          <View style={styles.headerActions} />
        </View>
        <View style={styles.loadingContainer}>
          <ThemedText>Loading side dish details...</ThemedText>
        </View>
      </View>
    );
  }

  return (
    <>
      <Stack.Screen options={{ headerShown: false }} />
      <View style={styles.container}>
        {/* Header */}
        <View style={[styles.header, { paddingTop: insets.top + 10 }]}>
          <TouchableOpacity style={styles.backButton} onPress={handleBack}>
            <IconSymbol name="chevron-left" size={24} color={colors.text} />
          </TouchableOpacity>
          <ThemedText type="title" style={styles.headerTitle}>
            Side Dish Details
          </ThemedText>
          <View style={styles.headerActions} />
        </View>

        <View style={styles.contentWrapper}>
          <ScrollView
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.scrollContent}
          >
            {/* Meal Image */}
            <Image
              source={getImageSource(mealData?.image)}
              style={styles.mealImage}
              resizeMode="cover"
            />

            {/* Meal Info */}
            <View style={styles.content}>
              <ThemedText type="title" style={styles.mealName}>
                {mealDetails.name}
              </ThemedText>

              <ThemedText style={styles.description}>
                {mealDetails.description}
              </ThemedText>

              {/* Category Icons */}
              {mealData && (
                <View style={styles.categoryIconsContainer}>
                  {(() => {
                    const icons = getMealCategoryIcons({
                      course: mealData.course,
                      cuisine_type: mealData.cuisine_type,
                      prep_method: mealData.prep_method,
                    });
                    const capitalizeFirst = (str: string) => {
                      return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
                    };
                    return (
                      <>
                        <View style={styles.categoryIcon}>
                          <ThemedText style={styles.categoryLabel}>
                            {capitalizeFirst(icons.course.label)}
                          </ThemedText>
                        </View>
                        <View style={styles.categoryIcon}>
                          <ThemedText style={styles.categoryLabel}>
                            {capitalizeFirst(icons.cuisine.label)}
                          </ThemedText>
                        </View>
                        <View style={styles.categoryIcon}>
                          <ThemedText style={styles.categoryLabel}>
                            {capitalizeFirst(icons.prepMethod.label)}
                          </ThemedText>
                        </View>
                      </>
                    );
                  })()}
                </View>
              )}

              {/* Add to Meal Plan Button - Top */}
              <TouchableOpacity
                style={[
                  styles.addToMealPlanButtonTop,
                  {
                    backgroundColor: 'transparent',
                    borderColor: isAlreadyAdded ? '#ff4444' : colors.primary,
                    borderWidth: 1,
                    opacity: 1,
                  },
                ]}
                onPress={
                  isAlreadyAdded
                    ? removeSideDishFromMealPlan
                    : addToShoppingListAndMealPlan
                }
                disabled={addingToShoppingList}
              >
                {addingToShoppingList ? (
                  <ActivityIndicator
                    size="small"
                    color={isAlreadyAdded ? '#ff4444' : colors.primary}
                  />
                ) : (
                  <>
                    <Ionicons
                      name={isAlreadyAdded ? 'trash' : 'add-circle'}
                      size={20}
                      color={isAlreadyAdded ? '#ff4444' : colors.primary}
                    />
                    <ThemedText
                      style={[
                        styles.addToMealPlanButtonTopText,
                        { color: isAlreadyAdded ? '#ff4444' : colors.primary },
                      ]}
                    >
                      {isAlreadyAdded
                        ? 'Remove from Meal Plan'
                        : 'Add to Meal Plan & Shopping List'}
                    </ThemedText>
                  </>
                )}
              </TouchableOpacity>

              {/* Quick Stats */}
              <View style={styles.statsContainer}>
                <View style={styles.statItem}>
                  <IconSymbol
                    name="clock-outline"
                    size={20}
                    color={colors.primary}
                  />
                  <ThemedText style={styles.statLabel}>Prep</ThemedText>
                  <ThemedText style={styles.statValue}>
                    {mealDetails.prepTime}
                  </ThemedText>
                </View>
                <View style={styles.statItem}>
                  <IconSymbol name="fire" size={20} color={colors.primary} />
                  <ThemedText style={styles.statLabel}>Cook</ThemedText>
                  <ThemedText style={styles.statValue}>
                    {mealDetails.cookTime}
                  </ThemedText>
                </View>
                <View style={styles.statItem}>
                  <IconSymbol
                    name="account-group"
                    size={20}
                    color={colors.primary}
                  />
                  <ThemedText style={styles.statLabel}>Serves</ThemedText>
                  <ThemedText style={styles.statValue}>
                    {adjustedServingSize || mealDetails.servingSize}
                  </ThemedText>
                </View>
              </View>

              {/* Nutrition - only show if has meaningful values */}
              {!shouldHideNutrition(mealDetails.nutrition) && (
                <View style={styles.section}>
                  <ThemedText type="subtitle" style={styles.sectionTitle}>
                    Nutrition (per serving)
                  </ThemedText>
                  <View style={styles.nutritionGrid}>
                    <CircularProgress
                      size={80}
                      strokeWidth={6}
                      progress={Math.min(
                        parseInt(mealDetails.nutrition.calories) / 800,
                        1
                      )} // Assuming 800 cal as high reference
                      color={colors.primary}
                      backgroundColor={
                        activeTheme === 'dark'
                          ? colors.primary + '20'
                          : colors.primary + '15'
                      }
                      value={mealDetails.nutrition.calories}
                      label="Calories"
                    />
                    <CircularProgress
                      size={80}
                      strokeWidth={6}
                      progress={Math.min(
                        parseInt(mealDetails.nutrition.protein) / 50,
                        1
                      )} // 50g protein as reference
                      color="#F44336"
                      backgroundColor={
                        activeTheme === 'dark' ? '#F4433620' : '#F4433615'
                      }
                      value={mealDetails.nutrition.protein}
                      label="Protein"
                    />
                    <CircularProgress
                      size={80}
                      strokeWidth={6}
                      progress={Math.min(
                        parseInt(mealDetails.nutrition.carbs) / 100,
                        1
                      )} // 100g carbs as reference
                      color="#2196F3"
                      backgroundColor={
                        activeTheme === 'dark' ? '#2196F320' : '#2196F315'
                      }
                      value={mealDetails.nutrition.carbs}
                      label="Carbs"
                    />
                    <CircularProgress
                      size={80}
                      strokeWidth={6}
                      progress={Math.min(
                        parseInt(mealDetails.nutrition.fat) / 35,
                        1
                      )} // 35g fat as reference
                      color="#FF9800"
                      backgroundColor={
                        activeTheme === 'dark' ? '#FF980020' : '#FF980015'
                      }
                      value={mealDetails.nutrition.fat}
                      label="Fat"
                    />
                  </View>
                </View>
              )}

              {/* Ingredients */}
              <View style={styles.section}>
                <View style={styles.ingredientsSectionHeader}>
                  <ThemedText type="subtitle" style={styles.sectionTitle}>
                    Ingredients
                  </ThemedText>
                  {hasCustomizedIngredients && (
                    <View
                      style={[
                        styles.customizedIndicator,
                        { backgroundColor: `${colors.primary}10` },
                      ]}
                    >
                      <Ionicons
                        name="pencil"
                        size={14}
                        color={colors.primary}
                      />
                      <ThemedText
                        style={[
                          styles.customizedText,
                          { color: colors.primary },
                        ]}
                      >
                        Customized
                      </ThemedText>
                    </View>
                  )}
                </View>
                
                {/* Serving Size Adjustment */}
                <View style={styles.servingAdjustmentContainer}>
                  <ThemedText style={styles.servingAdjustmentText}>
                    Adjust servings
                  </ThemedText>
                  <View style={styles.servingAdjustmentControls}>
                    <TouchableOpacity
                      style={[
                        styles.servingAdjustmentButton,
                        {
                          backgroundColor: colors.cardBackground,
                          borderColor: colors.border,
                          opacity: 
                            isSavingServingSize ||
                            (adjustedServingSize ||
                              parseInt(mealDetails.servingSize) ||
                              4) <= 2
                              ? 0.5 : 1
                        }
                      ]}
                      onPress={() => {
                        const currentSize =
                          adjustedServingSize ||
                          parseInt(mealDetails.servingSize) ||
                          4;
                        if (currentSize > 2) {
                          const newSize = currentSize - 1;
                          setAdjustedServingSize(newSize);
                          saveServingSizeAdjustment(newSize);
                        }
                      }}
                      disabled={
                        isSavingServingSize ||
                        (adjustedServingSize ||
                          parseInt(mealDetails.servingSize) ||
                          4) <= 2
                      }
                    >
                      <Ionicons
                        name="remove"
                        size={20}
                        color={colors.primary}
                      />
                    </TouchableOpacity>
                    <ThemedText style={styles.servingAdjustmentValue}>
                      {adjustedServingSize || mealDetails.servingSize}
                    </ThemedText>
                    <TouchableOpacity
                      style={[
                        styles.servingAdjustmentButton,
                        {
                          backgroundColor: colors.cardBackground,
                          borderColor: colors.border,
                          opacity: isSavingServingSize ? 0.5 : 1
                        }
                      ]}
                      onPress={() => {
                        const currentSize =
                          adjustedServingSize ||
                          parseInt(mealDetails.servingSize) ||
                          4;
                        if (currentSize < 20) {
                          const newSize = currentSize + 1;
                          setAdjustedServingSize(newSize);
                          saveServingSizeAdjustment(newSize);
                        }
                      }}
                      disabled={isSavingServingSize}
                    >
                      <Ionicons
                        name="add"
                        size={20}
                        color={colors.primary}
                      />
                    </TouchableOpacity>
                  </View>
                  {adjustedServingSize &&
                    adjustedServingSize !==
                      parseInt(mealDetails.servingSize) && (
                      <TouchableOpacity
                        style={styles.resetServingButton}
                        onPress={() => {
                          setAdjustedServingSize(null);
                          resetToOriginalIngredients();
                        }}
                        disabled={isSavingServingSize}
                      >
                        <ThemedText style={styles.resetServingText}>
                          Reset
                        </ThemedText>
                      </TouchableOpacity>
                    )}
                </View>
                <View style={styles.ingredientsColumn}>
                  {mealDetails.ingredients.map(
                    (
                      ingredient: {
                        name: string;
                        amount: string;
                        emoji: string;
                      },
                      index: number
                    ) => (
                      <View
                        key={index}
                        style={[
                          styles.ingredientContainer,
                          {
                            backgroundColor: colors.cardBackground,
                            borderColor: colors.border,
                          },
                        ]}
                      >
                        <Text style={styles.ingredientEmoji}>
                          {ingredient.emoji}
                        </Text>
                        <View style={styles.ingredientTextContainer}>
                          <ThemedText style={styles.ingredientName}>
                            {ingredient.name}
                          </ThemedText>
                          {ingredient.amount && (
                            <ThemedText style={styles.ingredientAmount}>
                              {ingredient.amount}
                            </ThemedText>
                          )}
                        </View>
                      </View>
                    )
                  )}
                </View>

                {/* Edit and Reset Buttons */}
                <View style={styles.ingredientActions}>
                  <TouchableOpacity
                    style={[
                      styles.ingredientActionButton,
                      {
                        backgroundColor: 'transparent',
                        borderColor: colors.primary,
                        borderWidth: 1,
                      },
                    ]}
                    onPress={() => setShowEditIngredientsModal(true)}
                  >
                    <Ionicons
                      name="pencil-outline"
                      size={16}
                      color={colors.primary}
                    />
                    <ThemedText
                      style={[
                        styles.ingredientActionText,
                        { color: colors.primary },
                      ]}
                    >
                      Edit Ingredients
                    </ThemedText>
                  </TouchableOpacity>

                  {(hasCustomizedIngredients ||
                    (adjustedServingSize &&
                      adjustedServingSize !==
                        parseInt(mealDetails.servingSize))) && (
                    <TouchableOpacity
                      style={[
                        styles.ingredientActionButton,
                        {
                          backgroundColor: colors.background,
                          borderColor: colors.border,
                          borderWidth: 1,
                        },
                      ]}
                      onPress={() => {
                        setAlertConfig({
                          title: 'Reset Ingredients',
                          message:
                            'This will restore the ingredients back to the original recipe and reset serving size. Your customizations will be lost. Continue?',
                        });
                        setShowAlert(true);
                      }}
                    >
                      <Ionicons
                        name="refresh-outline"
                        size={16}
                        color={colors.text}
                      />
                      <ThemedText
                        style={[styles.ingredientActionText, { opacity: 0.7 }]}
                      >
                        Reset
                      </ThemedText>
                    </TouchableOpacity>
                  )}
                </View>
              </View>

              {/* Instructions */}
              <View style={styles.section}>
                <View style={styles.instructionsSectionHeader}>
                  <ThemedText type="subtitle" style={styles.sectionTitle}>
                    Instructions
                  </ThemedText>
                  <TouchableOpacity
                    style={styles.zenModeButton}
                    onPress={() => setShowZenMode(true)}
                  >
                    <Ionicons
                      name="expand-outline"
                      size={20}
                      color={colors.primary}
                    />
                    <ThemedText style={styles.zenModeText}>
                      Cook Mode
                    </ThemedText>
                  </TouchableOpacity>
                </View>
                {mealDetails.instructions.map(
                  (instruction: string, index: number) => (
                    <View key={index} style={styles.instructionItem}>
                      <View
                        style={[
                          styles.stepNumber,
                          { backgroundColor: colors.primary },
                        ]}
                      >
                        <Text style={styles.stepNumberText}>{index + 1}</Text>
                      </View>
                      <ThemedText style={styles.instructionText}>
                        {instruction}
                      </ThemedText>
                    </View>
                  )
                )}
              </View>

              {/* Add to Meal Plan Button - Bottom */}
              <TouchableOpacity
                style={[
                  styles.addToMealPlanButton,
                  {
                    backgroundColor: 'transparent',
                    borderColor: isAlreadyAdded ? '#ff4444' : colors.primary,
                    borderWidth: 1,
                    opacity: 1,
                  },
                ]}
                onPress={
                  isAlreadyAdded
                    ? removeSideDishFromMealPlan
                    : addToShoppingListAndMealPlan
                }
                disabled={addingToShoppingList}
              >
                {addingToShoppingList ? (
                  <ActivityIndicator
                    size="small"
                    color={isAlreadyAdded ? '#ff4444' : colors.primary}
                  />
                ) : (
                  <>
                    <Ionicons
                      name={isAlreadyAdded ? 'trash' : 'add-circle'}
                      size={20}
                      color={isAlreadyAdded ? '#ff4444' : colors.primary}
                    />
                    <ThemedText
                      style={[
                        styles.addToMealPlanButtonText,
                        { color: isAlreadyAdded ? '#ff4444' : colors.primary },
                      ]}
                    >
                      {isAlreadyAdded
                        ? 'Remove from Meal Plan'
                        : 'Add to Meal Plan & Shopping List'}
                    </ThemedText>
                  </>
                )}
              </TouchableOpacity>
            </View>
          </ScrollView>
        </View>

        <CustomAlert
          visible={showAlert}
          title={alertConfig.title}
          message={alertConfig.message}
          onConfirm={() => {
            setShowAlert(false);
            if (alertConfig.title === 'Reset Ingredients') {
              resetToOriginalIngredients();
            }
          }}
          onCancel={() => setShowAlert(false)}
        />

        {/* Edit Ingredients Modal */}
        <EditIngredientsModal
          visible={showEditIngredientsModal}
          ingredients={mealDetails.ingredients}
          onClose={() => setShowEditIngredientsModal(false)}
          onSave={saveCustomizedIngredients}
          mealName={mealDetails.name}
        />

        {/* Zen Mode */}
        <ZenMode
          visible={showZenMode}
          instructions={mealDetails.instructions}
          onClose={() => setShowZenMode(false)}
          mealName={mealDetails.name}
        />
      </View>
    </>
  );
}

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    loadingContainer: {
      height: '100%',
      justifyContent: 'center',
      alignItems: 'center',
      padding: 24,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 24,
      paddingBottom: 16,
      justifyContent: 'space-between',
      backgroundColor: colors.background,
    },
    backButton: {
      padding: 8,
    },
    headerTitle: {
      flex: 1,
      textAlign: 'center',
      marginHorizontal: 16,
      fontSize: 18,
      fontWeight: '600',
    },
    headerActions: {
      flexDirection: 'row',
      gap: 8,
      width: 40, // Same width as back button for centering
    },
    contentWrapper: {
      flex: 1,
      backgroundColor: colors.background,
    },
    mealImage: {
      width: '100%',
      height: 250,
      marginBottom: 24,
    },
    content: {
      paddingHorizontal: 24,
    },
    mealName: {
      marginBottom: 12,
    },
    description: {
      fontSize: 16,
      lineHeight: 24,
      opacity: 0.7,
      marginBottom: 24,
    },
    statsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: 32,
      paddingVertical: 20,
    },
    statItem: {
      alignItems: 'center',
      flex: 1,
    },
    statLabel: {
      fontSize: 16,
      opacity: 0.6,
      marginTop: 4,
    },
    statValue: {
      fontSize: 18,
      fontWeight: '600',
      marginTop: 2,
    },
    section: {
      marginBottom: 32,
    },
    sectionTitle: {
      marginBottom: 16,
      fontSize: 18,
      fontWeight: '600',
    },
    nutritionGrid: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      paddingVertical: 8,
    },
    ingredientsColumn: {
      gap: 12,
    },
    ingredientContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderRadius: 8,
      borderWidth: 1,
      gap: 12,
    },
    ingredientEmoji: {
      fontSize: 18,
    },
    ingredientTextContainer: {
      flex: 1,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    ingredientName: {
      fontSize: 16,
      fontWeight: '500',
      flex: 1,
    },
    ingredientAmount: {
      fontSize: 16,
      fontWeight: '600',
      color: colors.primary,
      marginLeft: 8,
    },
    instructionItem: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      marginBottom: 16,
    },
    stepNumber: {
      width: 28,
      height: 28,
      borderRadius: 14,
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: 12,
      marginTop: 2,
    },
    stepNumberText: {
      color: colors.background,
      fontSize: 14,
      fontWeight: '600',
    },
    instructionText: {
      flex: 1,
      fontSize: 16,
      lineHeight: 24,
      paddingTop: 3,
    },
    addToMealPlanButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 16,
      paddingHorizontal: 24,
      borderRadius: 12,
      marginBottom: 32,
      gap: 8,
    },
    addToMealPlanButtonText: {
      fontSize: 16,
      fontWeight: '600',
      color: colors.background,
    },
    addToMealPlanButtonTop: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 12,
      paddingHorizontal: 20,
      borderRadius: 8,
      marginBottom: 24,
      gap: 8,
    },
    addToMealPlanButtonTopText: {
      fontSize: 14,
      fontWeight: '600',
    },
    scrollContent: {
      paddingBottom: 24,
    },
    categoryIconsContainer: {
      flexDirection: 'row',
      gap: 12,
      marginBottom: 24,
      flexWrap: 'wrap',
    },
    categoryIcon: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 6,
      backgroundColor: `${colors.primary}10`,
      paddingHorizontal: 12,
      paddingVertical: 8,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: `${colors.primary}30`,
    },
    categoryLabel: {
      fontSize: 12,
      fontWeight: '500',
      color: colors.text,
      opacity: 0.9,
    },
    ingredientsSectionHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 16,
    },
    customizedIndicator: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 4,
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12,
    },
    customizedText: {
      fontSize: 12,
      fontWeight: '500',
    },
    ingredientActions: {
      flexDirection: 'row',
      gap: 12,
      marginTop: 16,
    },
    ingredientActionButton: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 12,
      paddingHorizontal: 16,
      borderRadius: 8,
      gap: 6,
    },
    ingredientActionText: {
      fontSize: 14,
      fontWeight: '600',
    },
    instructionsSectionHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 16,
    },
    zenModeButton: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 6,
      paddingHorizontal: 12,
      paddingVertical: 8,
      borderRadius: 16,
      backgroundColor: `${colors.primary}10`,
      borderWidth: 1,
      borderColor: `${colors.primary}30`,
    },
    zenModeText: {
      fontSize: 14,
      fontWeight: '500',
      color: colors.primary,
    },
    servingSizeContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: 4,
    },
    servingSizeButton: {
      width: 36,
      height: 36,
      borderRadius: 18,
      borderWidth: 1,
      borderColor: colors.border,
      backgroundColor: colors.cardBackground,
      alignItems: 'center',
      justifyContent: 'center',
    },
    servingSizeValue: {
      fontSize: 18,
      fontWeight: '700',
      marginHorizontal: 12,
    },
    resetServingButton: {
      marginTop: 4,
      paddingHorizontal: 8,
      paddingVertical: 2,
      borderRadius: 12,
      backgroundColor: `${colors.primary}10`,
    },
    resetServingText: {
      fontSize: 11,
      fontWeight: '500',
      color: colors.primary,
    },
    servingAdjustmentContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: 16,
      paddingHorizontal: 16,
      paddingVertical: 12,
      backgroundColor: `${colors.primary}10`,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: `${colors.primary}20`,
    },
    servingAdjustmentText: {
      fontSize: 14,
      fontWeight: '500',
      color: colors.text,
      opacity: 0.8,
    },
    servingAdjustmentControls: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 12,
    },
    servingAdjustmentButton: {
      width: 36,
      height: 36,
      borderRadius: 18,
      borderWidth: 1,
      alignItems: 'center',
      justifyContent: 'center',
    },
    servingAdjustmentValue: {
      fontSize: 16,
      fontWeight: '600',
      minWidth: 20,
      textAlign: 'center',
    },
  });

export default function ProtectedSideDishDetailScreen() {
  return <SideDishDetailScreen />;
}
