import { CustomAlert } from '@/components/CustomAlert';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useAuth } from '@/contexts/AuthContext';
import type { Database } from '@/lib/database.types';
import { supabase } from '@/lib/supabase';
import {
  getUserPreferences,
  updateUserPreferences,
  DIETARY_RESTRICTIONS_OPTIONS,
  ALLERGEN_FREE_OPTIONS,
  CUISINE_OPTIONS,
} from '@/lib/userPreferences';
import { useFocusEffect } from '@react-navigation/native';
import * as Clipboard from 'expo-clipboard';
import { router, Stack } from 'expo-router';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
  Alert,
  RefreshControl,
  ScrollView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useThemeColor } from '@/hooks/useThemeColor';
import { useTheme } from '@/contexts/ThemeContext';
import { useTabBarHeight } from '@/hooks/useTabBarHeight';

type UserPreferences = Database['public']['Tables']['user_preferences']['Row'];
type Affiliate = Database['public']['Tables']['affiliates']['Row'];
type AffiliateReferral =
  Database['public']['Tables']['affiliate_referrals']['Row'];

export default function ProfileScreen() {
  const insets = useSafeAreaInsets();
  const { signOut, user, getUserDisplayName } = useAuth();
  const { themeMode, setThemeMode } = useTheme();
  const { contentBottomPadding } = useTabBarHeight();
  const scrollViewRef = useRef<ScrollView>(null);

  // Theme colors
  const textColor = useThemeColor({}, 'text');
  const iconColor = useThemeColor({}, 'icon');
  const borderColor = useThemeColor({}, 'border');
  const cardBackground = useThemeColor({}, 'cardBackground');
  const primaryColor = useThemeColor({}, 'primary');
  const styles = createStyles(primaryColor);
  const [preferences, setPreferences] = useState<UserPreferences | null>(null);
  const [loading, setLoading] = useState(true);
  const [editingItem, setEditingItem] = useState<string | null>(null);
  const [tempValue, setTempValue] = useState<any>(null);
  const [showSuccessAlert, setShowSuccessAlert] = useState(false);
  const [showErrorAlert, setShowErrorAlert] = useState(false);

  // Password change states
  const [showPasswordChange, setShowPasswordChange] = useState(false);
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [passwordLoading, setPasswordLoading] = useState(false);
  const [passwordError, setPasswordError] = useState('');
  const [showPasswordSuccess, setShowPasswordSuccess] = useState(false);

  // Affiliate states
  const [affiliate, setAffiliate] = useState<Affiliate | null>(null);
  const [affiliateReferrals, setAffiliateReferrals] = useState<
    AffiliateReferral[]
  >([]);
  const [affiliateLoading, setAffiliateLoading] = useState(true);
  const [showShareSuccess, setShowShareSuccess] = useState(false);

  // Refresh state
  const [refreshing, setRefreshing] = useState(false);

  // Stripe Connect states
  const [connectLoading] = useState(false);

  useEffect(() => {
    loadPreferences();
    loadAffiliateData();
  }, [user?.email]); // eslint-disable-line react-hooks/exhaustive-deps

  // Scroll to top when tab comes into focus
  useFocusEffect(
    useCallback(() => {
      scrollViewRef.current?.scrollTo({ y: 0, animated: false });
    }, [])
  );

  // Refresh affiliate data when the tab comes into focus
  useFocusEffect(
    React.useCallback(() => {
      loadAffiliateData();
    }, [user?.id]) // eslint-disable-line react-hooks/exhaustive-deps
  );

  const onRefresh = async () => {
    setRefreshing(true);
    try {
      await Promise.all([loadPreferences(), loadAffiliateData()]);
    } catch {
    } finally {
      setRefreshing(false);
    }
  };

  const loadPreferences = async () => {
    try {
      const userPrefs = await getUserPreferences();
      setPreferences(userPrefs);
    } catch {
    } finally {
      setLoading(false);
    }
  };

  const loadAffiliateData = async () => {
    if (!user?.id) {
      setAffiliateLoading(false);
      return;
    }

    try {
      // Check if user is an affiliate
      const { data: affiliateData, error: affiliateError } = await supabase
        .from('affiliates')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (affiliateError && affiliateError.code !== 'PGRST116') {
        throw affiliateError;
      }

      if (affiliateData) {
        setAffiliate(affiliateData);

        // Load affiliate referrals
        const { data: referralsData, error: referralsError } = await supabase
          .from('affiliate_referrals')
          .select('*')
          .eq('affiliate_id', affiliateData.id)
          .order('created_at', { ascending: false });

        if (!referralsError) {
          setAffiliateReferrals(referralsData || []);
        }
      }
    } catch {
    } finally {
      setAffiliateLoading(false);
    }
  };

  const handleEditItem = (item: string, currentValue: any) => {
    setEditingItem(item);
    setTempValue(currentValue);
  };

  const handleSaveEdit = async () => {
    if (!preferences || !editingItem || tempValue === null) return;

    try {
      let processedValue = tempValue;

      // Convert UI labels to database format for specific fields
      if (editingItem === 'dietary_restrictions' && Array.isArray(tempValue)) {
        processedValue = tempValue.map(mapDietaryToDatabase);
      } else if (editingItem === 'allergies' && Array.isArray(tempValue)) {
        processedValue = tempValue.includes('None') ? [] : tempValue.map(mapAllergenToDatabase);
      } else if (editingItem === 'cuisine_preferences' && Array.isArray(tempValue)) {
        processedValue = tempValue.map(mapCuisineToDatabase);
      } else if (editingItem === 'seasonal_preference' && Array.isArray(tempValue)) {
        processedValue = tempValue;
      }

      const updateData: any = { [editingItem]: processedValue };
      const result = await updateUserPreferences(updateData);

      if (result) {
        setPreferences(result);
        setEditingItem(null);
        setTempValue(null);
        setShowSuccessAlert(true);
      } else {
        throw new Error('Failed to update preferences');
      }
    } catch {
      setShowErrorAlert(true);
    }
  };

  const handleCancelEdit = () => {
    setEditingItem(null);
    setTempValue(null);
  };

  const handleLogout = () => {
    Alert.alert('Sign Out', 'Are you sure you want to sign out?', [
      { text: 'Cancel', style: 'cancel' },
      {
        text: 'Sign Out',
        style: 'destructive',
        onPress: signOut,
      },
    ]);
  };

  const handlePasswordChange = async () => {
    if (!newPassword || !confirmPassword) {
      setPasswordError('Please fill in all fields');
      return;
    }

    if (newPassword !== confirmPassword) {
      setPasswordError('New passwords do not match');
      return;
    }

    if (newPassword.length < 6) {
      setPasswordError('Password must be at least 6 characters long');
      return;
    }

    setPasswordLoading(true);
    setPasswordError('');

    try {
      const { error } = await supabase.auth.updateUser({
        password: newPassword,
      });

      if (error) throw error;

      setShowPasswordSuccess(true);
      setShowPasswordChange(false);
      setNewPassword('');
      setConfirmPassword('');
    } catch (error: any) {
      setPasswordError(error.message || 'Failed to change password');
    } finally {
      setPasswordLoading(false);
    }
  };

  const cancelPasswordChange = () => {
    setShowPasswordChange(false);
    setNewPassword('');
    setConfirmPassword('');
    setPasswordError('');
  };

  const copyCouponCode = async () => {
    if (!affiliate?.coupon_code) return;

    try {
      await Clipboard.setStringAsync(affiliate.coupon_code);
      setShowShareSuccess(true);
    } catch {
      Alert.alert('Error', 'Failed to copy coupon code to clipboard');
    }
  };

  const setupStripeConnect = async () => {
    if (!affiliate || !user?.email) return;

    // For now, show a placeholder message since this is a demo
    Alert.alert(
      'Stripe Connect Setup',
      "This would normally open Stripe Connect onboarding to set up your payout account. In production, you would:\n\n1. Complete business verification\n2. Add bank account details\n3. Provide tax information\n4. Enable automatic payouts\n\nOnce setup, you'll receive payments automatically when you reach $25 in commissions.",
      [{ text: 'OK' }]
    );

    // Simulate Connect setup for demo purposes
    /* TODO: Uncomment when ready for production
    setConnectLoading(true);
    try {
      // Get Supabase URL and key from environment
      const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
      const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;
      
      if (!supabaseUrl || !supabaseAnonKey) {
        throw new Error('Missing Supabase configuration');
      }

      // Call our Stripe Connect Edge Function
      const response = await fetch(`${supabaseUrl}/functions/v1/stripe-connect`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${supabaseAnonKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'create_account',
          affiliate_id: affiliate.id,
          email: user.email,
          business_type: 'individual'
        }),
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to create Stripe Connect account');
      }

      // Create account link for onboarding
      const linkResponse = await fetch(`${supabaseUrl}/functions/v1/stripe-connect`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${supabaseAnonKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'create_account_link',
          account_id: result.account_id,
          refresh_url: 'menumaker://profile',
          return_url: 'menumaker://profile?connect=success'
        }),
      });

      const linkResult = await linkResponse.json();

      if (!linkResult.success) {
        throw new Error(linkResult.error || 'Failed to create onboarding link');
      }

      // Open Stripe onboarding in browser
      await Linking.openURL(linkResult.url);
      
      // Refresh affiliate data after a delay to get updated status
      setTimeout(() => {
        loadAffiliateData();
      }, 2000);

    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to setup payout account');
    } finally {
      setConnectLoading(false);
    }
    */
  };

  const getConnectStatusDisplay = () => {
    if (!affiliate?.stripe_connect_status) return 'Not Setup';

    const statusMap = {
      not_started: 'Not Setup',
      pending: 'Setup Required',
      restricted: 'Under Review',
      active: 'Ready for Payouts',
      rejected: 'Rejected',
      disabled: 'Disabled',
    };

    return (
      statusMap[affiliate.stripe_connect_status as keyof typeof statusMap] ||
      affiliate.stripe_connect_status
    );
  };

  const getConnectStatusColor = () => {
    if (!affiliate?.stripe_connect_status) return '#666';

    const colorMap = {
      not_started: '#666',
      pending: '#FF9800',
      restricted: '#FF9800',
      active: primaryColor,
      rejected: '#F44336',
      disabled: '#666',
    };

    return (
      colorMap[affiliate.stripe_connect_status as keyof typeof colorMap] ||
      '#666'
    );
  };

  const formatAccountCreated = (timestamp: string | undefined) => {
    if (!timestamp) return 'Unknown';
    return new Date(timestamp).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatLastSignIn = (timestamp: string | undefined) => {
    if (!timestamp) return 'Unknown';
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = Math.floor(
      (now.getTime() - date.getTime()) / (1000 * 60 * 60)
    );

    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours} hours ago`;
    if (diffInHours < 48) return 'Yesterday';

    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Helper functions to convert between database format and UI labels
  const formatDietaryOption = (option: string) => {
    const mapping: { [key: string]: string } = {
      'VEGETARIAN': 'Vegetarian',
      'VEGAN': 'Vegan', 
      'GLUTEN_FREE': 'Gluten-Free',
      'DAIRY_FREE': 'Dairy-Free',
      'KETO_FRIENDLY': 'Keto',
      'PALEO': 'Paleo',
      'KOSHER': 'Kosher',
      'PESCATARIAN': 'Pescatarian',
      'MEDITERRANEAN': 'Mediterranean',
      'SUGAR_CONSCIOUS': 'Sugar-Conscious',
      'NO_SUGAR_ADDED': 'No Sugar Added',
      'DASH': 'DASH Diet',
      'FODMAP_FREE': 'Low FODMAP'
    };
    return mapping[option] || option;
  };

  const formatAllergenOption = (option: string) => {
    const mapping: { [key: string]: string } = {
      'PEANUT_FREE': 'Peanuts',
      'TREE_NUT_FREE': 'Tree Nuts',
      'SHELLFISH_FREE': 'Shellfish',
      'FISH_FREE': 'Fish',
      'EGG_FREE': 'Eggs',
      'DAIRY_FREE': 'Dairy',
      'SOY_FREE': 'Soy',
      'WHEAT_FREE': 'Wheat',
      'SESAME_FREE': 'Sesame',
      'CELERY_FREE': 'Celery',
      'MUSTARD_FREE': 'Mustard',
      'LUPINE_FREE': 'Lupine',
      'MOLLUSK_FREE': 'Mollusks',
      'CRUSTACEAN_FREE': 'Crustaceans'
    };
    return mapping[option] || option;
  };

  const formatCuisineOption = (option: string) => {
    const mapping: { [key: string]: string } = {
      'american': 'American',
      'italian': 'Italian',
      'mexican': 'Mexican',
      'mediterranean': 'Mediterranean',
      'french': 'French',
      'chinese': 'Chinese',
      'south american': 'South American',
      'asian': 'Asian',
      'japanese': 'Japanese',
      'british': 'British',
      'south east asian': 'South East Asian',
      'nordic': 'Nordic',
      'middle eastern': 'Middle Eastern',
      'indian': 'Indian',
      'central europe': 'Central European',
      'caribbean': 'Caribbean',
      'eastern europe': 'Eastern European',
      'greek': 'Greek',
      'korean': 'Korean'
    };
    return mapping[option] || option;
  };

  const mapDietaryToDatabase = (uiValue: string) => {
    const reverseMapping = Object.fromEntries(
      Object.entries({
        'VEGETARIAN': 'Vegetarian',
        'VEGAN': 'Vegan', 
        'GLUTEN_FREE': 'Gluten-Free',
        'DAIRY_FREE': 'Dairy-Free',
        'KETO_FRIENDLY': 'Keto',
        'PALEO': 'Paleo',
        'KOSHER': 'Kosher',
        'PESCATARIAN': 'Pescatarian',
        'MEDITERRANEAN': 'Mediterranean',
        'SUGAR_CONSCIOUS': 'Sugar-Conscious',
        'NO_SUGAR_ADDED': 'No Sugar Added',
        'DASH': 'DASH Diet',
        'FODMAP_FREE': 'Low FODMAP'
      }).map(([key, value]) => [value, key])
    );
    return reverseMapping[uiValue] || uiValue;
  };

  const mapAllergenToDatabase = (uiValue: string) => {
    if (uiValue === 'None') return 'None';
    const reverseMapping = Object.fromEntries(
      Object.entries({
        'PEANUT_FREE': 'Peanuts',
        'TREE_NUT_FREE': 'Tree Nuts',
        'SHELLFISH_FREE': 'Shellfish',
        'FISH_FREE': 'Fish',
        'EGG_FREE': 'Eggs',
        'DAIRY_FREE': 'Dairy',
        'SOY_FREE': 'Soy',
        'WHEAT_FREE': 'Wheat',
        'SESAME_FREE': 'Sesame',
        'CELERY_FREE': 'Celery',
        'MUSTARD_FREE': 'Mustard',
        'LUPINE_FREE': 'Lupine',
        'MOLLUSK_FREE': 'Mollusks',
        'CRUSTACEAN_FREE': 'Crustaceans'
      }).map(([key, value]) => [value, key])
    );
    return reverseMapping[uiValue] || uiValue;
  };

  const mapCuisineToDatabase = (uiValue: string) => {
    const reverseMapping = Object.fromEntries(
      Object.entries({
        'american': 'American',
        'italian': 'Italian',
        'mexican': 'Mexican',
        'mediterranean': 'Mediterranean',
        'french': 'French',
        'chinese': 'Chinese',
        'south american': 'South American',
        'asian': 'Asian',
        'japanese': 'Japanese',
        'british': 'British',
        'south east asian': 'South East Asian',
        'nordic': 'Nordic',
        'middle eastern': 'Middle Eastern',
        'indian': 'Indian',
        'central europe': 'Central European',
        'caribbean': 'Caribbean',
        'eastern europe': 'Eastern European',
        'greek': 'Greek',
        'korean': 'Korean'
      }).map(([key, value]) => [value, key])
    );
    return reverseMapping[uiValue] || uiValue;
  };

  const formatDietaryRestrictions = (restrictions: string[] | null) => {
    if (!restrictions || restrictions.length === 0) return 'None';
    return restrictions.map(formatDietaryOption).join(', ');
  };

  const formatAllergies = (allergies: string[] | null) => {
    if (!allergies || allergies.length === 0) return 'None';
    return allergies.map(formatAllergenOption).join(', ');
  };

  const formatCookingTime = (time: string | null) => {
    if (!time) return 'Not set';
    const mapping: { [key: string]: string } = {
      short: '15 minutes',
      medium: '30 minutes',
      long: '45 minutes',
      extended: '1+ hour',
      any: 'No preference',
    };
    return mapping[time] || time;
  };


  const formatSeasonalPreferences = (seasonal: string[] | null) => {
    if (!seasonal || seasonal.length === 0) return 'Not set';
    
    const mapping: { [key: string]: string } = {
      automatic: 'Seasonal (Auto)',
      spring_foods: 'Spring Foods',
      summer_foods: 'Summer Foods',
      fall_foods: 'Fall Foods',
      winter_foods: 'Winter Foods',
      no_preference: 'Year-Round',
    };
    
    return seasonal.map(s => mapping[s] || s).join(', ');
  };

  const formatCuisinePreferences = (cuisines: string[] | null) => {
    if (!cuisines || cuisines.length === 0) return 'No preferences set';
    return cuisines.map(formatCuisineOption).join(', ');
  };

  const formatKitchenEquipment = (equipment: string[] | null) => {
    if (!equipment || equipment.length === 0) return 'None specified';
    return equipment.join(', ');
  };

  // Component for editable preference items
  const PreferenceItem = ({
    icon,
    label,
    value,
    fieldName,
    type = 'text',
    options = [],
    isExclusion = false,
  }: {
    icon: string;
    label: string;
    value: string;
    fieldName: string;
    type?: 'text' | 'select' | 'number' | 'multiselect' | 'singleselect';
    options?: (string | { label: string; value: string })[];
    isExclusion?: boolean;
  }) => {
    const isEditing = editingItem === fieldName;

    const handleCardPress = () => {
      if (!isEditing) {
        handleEditItem(
          fieldName,
          fieldName === 'household_size'
            ? preferences?.[fieldName as keyof typeof preferences]
            : fieldName === 'dietary_restrictions' ||
                fieldName === 'allergies' ||
                fieldName === 'cuisine_preferences' ||
                fieldName === 'kitchen_equipment' ||
                fieldName === 'seasonal_preference'
              ? preferences?.[fieldName as keyof typeof preferences] || []
              : preferences?.[fieldName as keyof typeof preferences]
        );
      }
    };

    return (
      <TouchableOpacity
        style={[
          styles.preferenceItem,
          { backgroundColor: cardBackground, borderColor: borderColor },
          isEditing && {
            borderColor: primaryColor,
            backgroundColor: `${primaryColor}10`,
          },
        ]}
        onPress={handleCardPress}
        disabled={isEditing}
        activeOpacity={0.7}
      >
        <View style={styles.preferenceHeader}>
          <IconSymbol name={icon as any} size={20} color={primaryColor} />
          <ThemedText style={[styles.preferenceLabel, { color: textColor }]}>
            {label}
          </ThemedText>
          <IconSymbol name="chevron-right" size={16} color={iconColor} />
        </View>

        {isEditing ? (
          <View style={styles.editContainer}>
            {type === 'select' && (
              <View style={styles.selectContainer}>
                {options.map((option) => {
                  const optionValue =
                    typeof option === 'string' ? option : option.value;
                  const optionLabel =
                    typeof option === 'string' ? option : option.label;
                  return (
                    <TouchableOpacity
                      key={optionValue}
                      style={[
                        styles.selectOption,
                        {
                          backgroundColor: cardBackground,
                          borderColor: borderColor,
                        },
                        tempValue === optionValue && [
                          styles.selectOptionActive,
                          {
                            backgroundColor: `${primaryColor}10`,
                            borderColor: primaryColor,
                          },
                        ],
                      ]}
                      onPress={() => setTempValue(optionValue)}
                    >
                      <ThemedText
                        style={[
                          styles.selectOptionText,
                          {
                            color:
                              tempValue === optionValue ? primaryColor : textColor,
                          },
                          tempValue === optionValue &&
                            styles.selectOptionTextActive,
                        ]}
                      >
                        {optionLabel}
                      </ThemedText>
                    </TouchableOpacity>
                  );
                })}
              </View>
            )}

            {type === 'number' && (
              <View style={styles.numberContainer}>
                {[1, 2, 3, 4, 5, 6].map((num) => (
                  <TouchableOpacity
                    key={num}
                    style={[
                      styles.numberOption,
                      {
                        backgroundColor: cardBackground,
                        borderColor: borderColor,
                      },
                      tempValue === num && [
                        styles.numberOptionActive,
                        {
                          backgroundColor: `${primaryColor}10`,
                          borderColor: primaryColor,
                        },
                      ],
                    ]}
                    onPress={() => setTempValue(num)}
                  >
                    <ThemedText
                      style={[
                        styles.numberOptionText,
                        { color: tempValue === num ? primaryColor : textColor },
                        tempValue === num && styles.numberOptionTextActive,
                      ]}
                    >
                      {num}
                    </ThemedText>
                  </TouchableOpacity>
                ))}
              </View>
            )}

            {(type === 'multiselect' || type === 'singleselect') && (
              <View style={styles.multiselectContainer}>
                {options.map((option) => {
                  const optionValue =
                    typeof option === 'string' ? option : option.value;
                  const optionLabel =
                    typeof option === 'string' ? option : option.label;
                  const isSelected = type === 'multiselect' 
                    ? tempValue?.includes(optionValue)
                    : tempValue === optionValue;
                  return (
                    <TouchableOpacity
                      key={optionValue}
                      style={[
                        styles.multiselectOption,
                        {
                          backgroundColor: cardBackground,
                          borderColor: borderColor,
                        },
                        isSelected && [
                          styles.multiselectOptionActive,
                          {
                            backgroundColor: `${primaryColor}10`,
                            borderColor: primaryColor,
                          },
                        ],
                        isExclusion && isSelected && {
                          backgroundColor: '#FF6B6B',
                          borderColor: '#FF6B6B',
                        },
                      ]}
                      onPress={() => {
                        if (type === 'multiselect') {
                          const currentArray = tempValue || [];
                          if (isSelected) {
                            setTempValue(
                              currentArray.filter(
                                (item: string) => item !== optionValue
                              )
                            );
                          } else {
                            setTempValue([...currentArray, optionValue]);
                          }
                        } else {
                          // singleselect
                          setTempValue(optionValue);
                        }
                      }}
                    >
                      <View style={styles.multiselectOptionContent}>
                        {isExclusion && isSelected && (
                          <IconSymbol name="close" size={12} color="#000000" style={styles.exclusionIcon} />
                        )}
                        <ThemedText
                          style={[
                            styles.multiselectOptionText,
                            { color: isSelected ? primaryColor : textColor },
                            isSelected && styles.multiselectOptionTextActive,
                          ]}
                        >
                          {optionLabel}
                        </ThemedText>
                      </View>
                    </TouchableOpacity>
                  );
                })}
              </View>
            )}

            <View style={styles.editActions}>
              <TouchableOpacity
                style={[
                  styles.cancelButton,
                  { backgroundColor: cardBackground, borderColor: borderColor },
                ]}
                onPress={handleCancelEdit}
              >
                <ThemedText
                  style={[styles.cancelButtonText, { color: textColor }]}
                >
                  Cancel
                </ThemedText>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.saveButton, { borderColor: primaryColor }]}
                onPress={handleSaveEdit}
              >
                <ThemedText style={[styles.saveButtonText, { color: primaryColor }]}>Save</ThemedText>
              </TouchableOpacity>
            </View>
          </View>
        ) : (
          <ThemedText style={styles.preferenceValue}>{value}</ThemedText>
        )}
      </TouchableOpacity>
    );
  };

  const handleBack = () => {
    router.back();
  };

  if (loading) {
    return (
      <ThemedView style={styles.container}>
        <Stack.Screen options={{ headerShown: false }} />
        <View style={[styles.header, { paddingTop: insets.top + 10 }]}>
          <TouchableOpacity style={styles.backButton} onPress={handleBack}>
            <IconSymbol name="chevron-left" size={24} color={textColor} />
          </TouchableOpacity>
          <ThemedText type="title" style={styles.headerTitle}>
            Profile
          </ThemedText>
          <View style={styles.headerActions} />
        </View>
        <View style={styles.loadingContainer}>
          <ThemedText style={styles.centeredLoadingText}>
            Loading preferences...
          </ThemedText>
        </View>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <Stack.Screen options={{ headerShown: false }} />

      {/* Header */}
      <View style={[styles.header, { paddingTop: insets.top + 10 }]}>
        <TouchableOpacity style={styles.backButton} onPress={handleBack}>
          <IconSymbol name="chevron-left" size={24} color={textColor} />
        </TouchableOpacity>
        <ThemedText type="title" style={styles.headerTitle}>
          Profile
        </ThemedText>
        <View style={styles.headerActions}>
          <View style={styles.themeToggle}>
            <TouchableOpacity
              style={[
                styles.themeButton,
                {
                  backgroundColor:
                    themeMode === 'light' ? `${primaryColor}10` : 'transparent',
                },
              ]}
              onPress={() => setThemeMode('light')}
              activeOpacity={0.7}
            >
              <IconSymbol
                name="white-balance-sunny"
                size={18}
                color={themeMode === 'light' ? primaryColor : textColor}
              />
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.themeButton,
                {
                  backgroundColor:
                    themeMode === 'dark' ? `${primaryColor}10` : 'transparent',
                },
              ]}
              onPress={() => setThemeMode('dark')}
              activeOpacity={0.7}
            >
              <IconSymbol
                name="moon-waning-crescent"
                size={18}
                color={themeMode === 'dark' ? primaryColor : textColor}
              />
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.themeButton,
                {
                  backgroundColor:
                    themeMode === 'system' ? `${primaryColor}10` : 'transparent',
                },
              ]}
              onPress={() => setThemeMode('system')}
              activeOpacity={0.7}
            >
              <IconSymbol
                name="cog-outline"
                size={18}
                color={themeMode === 'system' ? primaryColor : textColor}
              />
            </TouchableOpacity>
          </View>
        </View>
      </View>

      <ScrollView
        ref={scrollViewRef}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={[
          styles.scrollContent,
          { paddingBottom: contentBottomPadding },
        ]}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor={primaryColor}
            colors={[primaryColor]}
          />
        }
      >
        <View style={styles.profileContent}>
          {/* User Information Section */}
          <View style={styles.userInfoSection}>
            <View
              style={[
                styles.userInfoCard,
                { backgroundColor: cardBackground, borderColor: borderColor },
              ]}
            >
              <View style={styles.userDetails}>
                <ThemedText style={[styles.displayName, { color: textColor }]}>
                  Hello, {getUserDisplayName() || 'User'}!
                </ThemedText>

                <ThemedText style={[styles.email, { color: primaryColor }]}>
                  {user?.email || 'No email'}
                </ThemedText>

                <View style={styles.userMetadata}>
                  <View style={styles.metadataItem}>
                    <IconSymbol
                      name="calendar-outline"
                      size={14}
                      color={iconColor}
                    />
                    <ThemedText
                      style={[styles.metadataText, { color: textColor }]}
                    >
                      Joined {formatAccountCreated(user?.created_at)}
                    </ThemedText>
                  </View>

                  <View style={styles.metadataItem}>
                    <IconSymbol
                      name="clock-outline"
                      size={14}
                      color={iconColor}
                    />
                    <ThemedText
                      style={[styles.metadataText, { color: textColor }]}
                    >
                      Last active {formatLastSignIn(user?.last_sign_in_at)}
                    </ThemedText>
                  </View>

                  {user?.email_confirmed_at && (
                    <View style={styles.metadataItem}>
                      <IconSymbol
                        name="check-circle"
                        size={14}
                        color={primaryColor}
                      />
                      <ThemedText
                        style={[styles.metadataText, { color: textColor }]}
                      >
                        Email verified
                      </ThemedText>
                    </View>
                  )}
                </View>
              </View>
            </View>
          </View>
        </View>

        {preferences ? (
          <View style={styles.preferencesSection}>
            <ThemedText
              type="subtitle"
              style={[styles.sectionTitle, { color: textColor }]}
            >
              Your Preferences
            </ThemedText>
            <ThemedText style={[styles.sectionDescription, { color: iconColor }]}>
              Tap any preference to customize. Selected items will be excluded from your meal plans.
            </ThemedText>

            <PreferenceItem
              icon="silverware-fork-knife"
              label="Dietary Restrictions"
              value={formatDietaryRestrictions(
                preferences.dietary_restrictions
              )}
              fieldName="dietary_restrictions"
              type="multiselect"
              options={DIETARY_RESTRICTIONS_OPTIONS.map(formatDietaryOption)}
              isExclusion={true}
            />

            <PreferenceItem
              icon="alert-circle-outline"
              label="Allergies & Food Safety"
              value={formatAllergies(preferences.allergies)}
              fieldName="allergies"
              type="multiselect"
              options={[...ALLERGEN_FREE_OPTIONS.map(formatAllergenOption), 'None']}
              isExclusion={true}
            />

            <PreferenceItem
              icon="earth"
              label="Cuisine Dislikes"
              value={formatCuisinePreferences(preferences.cuisine_preferences)}
              fieldName="cuisine_preferences"
              type="multiselect"
              options={CUISINE_OPTIONS.map(formatCuisineOption)}
              isExclusion={true}
            />

            <PreferenceItem
              icon="account-group-outline"
              label="Household Size"
              value={`${preferences.household_size} ${preferences.household_size === 1 ? 'person' : 'people'}`}
              fieldName="household_size"
              type="number"
            />

            <PreferenceItem
              icon="clock-outline"
              label="Cooking Time"
              value={formatCookingTime(preferences.available_cooking_time)}
              fieldName="available_cooking_time"
              type="singleselect"
              options={[
                { label: '15 minutes', value: 'short' },
                { label: '30 minutes', value: 'medium' },
                { label: '45 minutes', value: 'long' },
                { label: '1+ hour', value: 'extended' },
                { label: 'No preference', value: 'any' },
              ]}
            />

            <PreferenceItem
              icon="tools"
              label="Kitchen Equipment"
              value={formatKitchenEquipment(preferences.kitchen_equipment)}
              fieldName="kitchen_equipment"
              type="multiselect"
              options={[
                'Air Fryer',
                'Slow Cooker',
                'Instant Pot',
                'Stand Mixer',
                'Food Processor',
                'Blender',
                'Grill',
                'Rice Cooker',
                'Dutch Oven',
                'Cast Iron Pan',
              ]}
            />

            <PreferenceItem
              icon="weather-sunny"
              label="Seasonal Food Preferences"
              value={formatSeasonalPreferences(preferences.seasonal_preference)}
              fieldName="seasonal_preference"
              type="multiselect"
              options={[
                { label: 'Seasonal (Auto)', value: 'automatic' },
                { label: 'Spring Foods', value: 'spring_foods' },
                { label: 'Summer Foods', value: 'summer_foods' },
                { label: 'Fall Foods', value: 'fall_foods' },
                { label: 'Winter Foods', value: 'winter_foods' },
                { label: 'Year-Round', value: 'no_preference' },
              ]}
            />
          </View>
        ) : (
          <View style={styles.noPreferencesSection}>
            <IconSymbol name="cog-outline" size={48} color="#666" />
            <ThemedText style={styles.noPreferencesText}>
              No preferences set yet
            </ThemedText>
            <ThemedText style={styles.noPreferencesSubtext}>
              Complete the onboarding process to personalize your meal planning
              experience
            </ThemedText>
          </View>
        )}

        {/* Affiliate Section */}
        {!affiliateLoading && affiliate && (
          <View style={styles.affiliateSection}>
            <ThemedText
              type="subtitle"
              style={[styles.sectionTitle, { color: textColor }]}
            >
              Partner Program
            </ThemedText>

            <View
              style={[
                styles.affiliateCard,
                { backgroundColor: cardBackground, borderColor: borderColor },
              ]}
            >
              <View style={styles.affiliateHeader}>
                <IconSymbol name="tag-outline" size={24} color={primaryColor} />
                <View style={styles.affiliateHeaderText}>
                  <ThemedText
                    style={[styles.affiliateTitle, { color: textColor }]}
                  >
                    Affiliate Partner
                  </ThemedText>
                  <ThemedText
                    style={[styles.affiliateCode, { color: textColor }]}
                  >
                    Coupon: {affiliate.coupon_code || affiliate.affiliate_code}
                  </ThemedText>
                </View>
              </View>

              <View
                style={[
                  styles.affiliateStats,
                  { backgroundColor: cardBackground },
                ]}
              >
                <View style={styles.statItem}>
                  <ThemedText style={[styles.statNumber, { color: textColor }]}>
                    {affiliate.total_referrals}
                  </ThemedText>
                  <ThemedText style={styles.statLabel}>
                    Total Referrals
                  </ThemedText>
                </View>
                <View style={styles.statDivider} />
                <View style={styles.statItem}>
                  <ThemedText style={[styles.statNumber, { color: textColor }]}>
                    ${affiliate.total_commission_earned.toFixed(2)}
                  </ThemedText>
                  <ThemedText style={styles.statLabel}>Total Earned</ThemedText>
                </View>
                <View style={styles.statDivider} />
                <View style={styles.statItem}>
                  <ThemedText style={[styles.statNumber, { color: textColor }]}>
                    {
                      affiliateReferrals.filter((r) => r.status === 'confirmed')
                        .length
                    }
                  </ThemedText>
                  <ThemedText style={styles.statLabel}>Active</ThemedText>
                </View>
              </View>

              {/* Payout Setup Section */}
              <View style={styles.payoutSection}>
                <View style={styles.payoutHeader}>
                  <IconSymbol
                    name="wallet-outline"
                    size={20}
                    color={primaryColor}
                  />
                  <ThemedText
                    style={[styles.payoutTitle, { color: textColor }]}
                  >
                    Payout Setup
                  </ThemedText>
                  <View
                    style={[
                      styles.statusBadge,
                      { backgroundColor: getConnectStatusColor() },
                    ]}
                  >
                    <ThemedText style={styles.statusText}>
                      {getConnectStatusDisplay()}
                    </ThemedText>
                  </View>
                </View>

                {affiliate.stripe_connect_status !== 'active' ? (
                  <View style={styles.payoutSetupContainer}>
                    <ThemedText
                      style={[styles.payoutDescription, { color: textColor }]}
                    >
                      Setup your payout account to receive affiliate commissions
                      automatically.
                    </ThemedText>
                    <TouchableOpacity
                      style={[
                        styles.setupButton,
                        { borderColor: primaryColor },
                        connectLoading && styles.setupButtonDisabled,
                      ]}
                      onPress={setupStripeConnect}
                      disabled={connectLoading}
                    >
                      <IconSymbol
                        name="plus-circle-outline"
                        size={16}
                        color={primaryColor}
                      />
                      <ThemedText style={[styles.setupButtonText, { color: primaryColor }]}>
                        {connectLoading
                          ? 'Setting up...'
                          : 'Setup Payout Account'}
                      </ThemedText>
                    </TouchableOpacity>
                  </View>
                ) : (
                  <View style={styles.payoutActiveContainer}>
                    <View style={styles.payoutInfo}>
                      <IconSymbol
                        name="check-circle-outline"
                        size={16}
                        color={primaryColor}
                      />
                      <ThemedText style={styles.payoutActiveText}>
                        Payouts are enabled! You&apos;ll receive automatic
                        payments when you reach $
                        {affiliate.minimum_payout_threshold?.toFixed(2) ||
                          '25.00'}
                        .
                      </ThemedText>
                    </View>
                  </View>
                )}
              </View>

              <View style={styles.affiliateLinkContainer}>
                <ThemedText style={[styles.linkLabel, { color: textColor }]}>
                  Your Coupon Code:
                </ThemedText>
                <View
                  style={[
                    styles.linkBox,
                    {
                      backgroundColor: cardBackground,
                      borderColor: borderColor,
                    },
                  ]}
                >
                  <ThemedText
                    style={[styles.linkText, { color: textColor }]}
                    numberOfLines={1}
                  >
                    {affiliate.coupon_code || affiliate.affiliate_code}
                  </ThemedText>
                </View>
              </View>

              <View style={styles.affiliateActions}>
                <TouchableOpacity
                  style={[
                    styles.copyButton,
                    {
                      backgroundColor: `${primaryColor}10`,
                      borderColor: primaryColor,
                    },
                  ]}
                  onPress={copyCouponCode}
                >
                  <IconSymbol
                    name="content-copy"
                    size={16}
                    color={primaryColor}
                  />
                  <ThemedText
                    style={[styles.copyButtonText, { color: textColor }]}
                  >
                    Copy Coupon
                  </ThemedText>
                </TouchableOpacity>
              </View>

              {affiliateReferrals.length > 0 && (
                <View style={styles.recentReferrals}>
                  <ThemedText
                    style={[styles.recentReferralsTitle, { color: textColor }]}
                  >
                    Recent Referrals
                  </ThemedText>
                  {affiliateReferrals.slice(0, 3).map((referral) => (
                    <View key={referral.id} style={styles.referralItem}>
                      <View style={styles.referralInfo}>
                        <View
                          style={[
                            styles.referralStatus,
                            {
                              backgroundColor:
                                referral.status === 'confirmed'
                                  ? primaryColor
                                  : referral.status === 'pending'
                                    ? '#FF9800'
                                    : '#666',
                            },
                          ]}
                        />
                        <View>
                          <ThemedText style={styles.referralAmount}>
                            +${referral.commission_amount.toFixed(2)}
                          </ThemedText>
                          <ThemedText style={styles.referralDate}>
                            {new Date(
                              referral.conversion_date
                            ).toLocaleDateString()}
                          </ThemedText>
                        </View>
                      </View>
                      <ThemedText style={styles.referralStatusText}>
                        {referral.status.charAt(0).toUpperCase() +
                          referral.status.slice(1)}
                      </ThemedText>
                    </View>
                  ))}
                </View>
              )}
            </View>
          </View>
        )}

        <View style={styles.accountSection}>
          <ThemedText
            type="subtitle"
            style={[styles.sectionTitle, { color: textColor }]}
          >
            Account Security
          </ThemedText>

          {!showPasswordChange ? (
            <TouchableOpacity
              style={[
                styles.passwordChangeButton,
                { backgroundColor: cardBackground, borderColor: borderColor },
              ]}
              onPress={() => setShowPasswordChange(true)}
            >
              <IconSymbol name="lock-outline" size={16} color={primaryColor} />
              <ThemedText
                style={[styles.passwordChangeText, { color: textColor }]}
              >
                Change Password
              </ThemedText>
              <IconSymbol name="chevron-right" size={16} color={iconColor} />
            </TouchableOpacity>
          ) : (
            <View
              style={[
                styles.passwordChangeForm,
                { backgroundColor: cardBackground, borderColor: borderColor },
              ]}
            >
              <ThemedText
                style={[styles.passwordFormTitle, { color: textColor }]}
              >
                Change Password
              </ThemedText>

              <View style={styles.inputContainer}>
                <ThemedText style={[styles.inputLabel, { color: textColor }]}>
                  New Password
                </ThemedText>
                <TextInput
                  style={[
                    styles.textInput,
                    {
                      backgroundColor: cardBackground,
                      color: textColor,
                      borderColor: borderColor,
                    },
                  ]}
                  value={newPassword}
                  onChangeText={setNewPassword}
                  placeholder="Enter new password"
                  placeholderTextColor={iconColor}
                  secureTextEntry
                  autoCapitalize="none"
                />
              </View>

              <View style={styles.inputContainer}>
                <ThemedText style={[styles.inputLabel, { color: textColor }]}>
                  Confirm New Password
                </ThemedText>
                <TextInput
                  style={[
                    styles.textInput,
                    {
                      backgroundColor: cardBackground,
                      color: textColor,
                      borderColor: borderColor,
                    },
                  ]}
                  value={confirmPassword}
                  onChangeText={setConfirmPassword}
                  placeholder="Confirm new password"
                  placeholderTextColor={iconColor}
                  secureTextEntry
                  autoCapitalize="none"
                />
              </View>

              {passwordError ? (
                <ThemedText style={styles.errorText}>
                  {passwordError}
                </ThemedText>
              ) : null}

              <View style={styles.passwordFormActions}>
                <TouchableOpacity
                  style={[
                    styles.cancelPasswordButton,
                    {
                      backgroundColor: cardBackground,
                      borderColor: borderColor,
                    },
                  ]}
                  onPress={cancelPasswordChange}
                  disabled={passwordLoading}
                >
                  <ThemedText
                    style={[styles.cancelPasswordText, { color: textColor }]}
                  >
                    Cancel
                  </ThemedText>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[
                    styles.savePasswordButton,
                    { borderColor: primaryColor },
                    passwordLoading && styles.savePasswordButtonDisabled,
                  ]}
                  onPress={handlePasswordChange}
                  disabled={passwordLoading}
                >
                  <ThemedText style={[styles.savePasswordText, { color: primaryColor }]}>
                    {passwordLoading ? 'Updating...' : 'Update Password'}
                  </ThemedText>
                </TouchableOpacity>
              </View>
            </View>
          )}

          <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
            <IconSymbol name="logout" size={16} color="#FFFFFF" />
            <ThemedText style={styles.logoutText}>Sign Out</ThemedText>
          </TouchableOpacity>
        </View>
      </ScrollView>

      <CustomAlert
        visible={showSuccessAlert}
        title="Success"
        message="Your preferences have been updated successfully!"
        confirmText="OK"
        onConfirm={() => setShowSuccessAlert(false)}
      />

      <CustomAlert
        visible={showErrorAlert}
        title="Error"
        message="Failed to update preferences. Please try again."
        confirmText="OK"
        onConfirm={() => setShowErrorAlert(false)}
      />

      <CustomAlert
        visible={showPasswordSuccess}
        title="Password Updated"
        message="Your password has been successfully updated!"
        confirmText="OK"
        onConfirm={() => setShowPasswordSuccess(false)}
      />

      <CustomAlert
        visible={showShareSuccess}
        title="Coupon Copied!"
        message="Your coupon code has been copied to the clipboard."
        confirmText="OK"
        onConfirm={() => setShowShareSuccess(false)}
      />
    </ThemedView>
  );
}

const createStyles = (primaryColor: string) => StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    padding: 24,
    // paddingBottom will be set dynamically based on tab bar height
  },
  // Detail page style header
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingBottom: 16,
    justifyContent: 'space-between',
    backgroundColor: 'transparent',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    flex: 1,
    textAlign: 'center',
    marginHorizontal: 16,
  },
  headerActions: {
    alignItems: 'flex-end',
    minWidth: 80,
  },
  profileContent: {
    marginBottom: 32,
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 0,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 100,
  },
  loadingText: {
    fontSize: 16,
    opacity: 0.7,
  },
  themeToggle: {
    flexDirection: 'row',
    gap: 4,
  },
  themeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  userInfoSection: {
    marginBottom: 24,
  },
  userInfoCard: {
    borderRadius: 16,
    padding: 24,
    borderWidth: 1,
    alignItems: 'center',
  },
  userDetails: {
    alignItems: 'center',
    width: '100%',
  },
  displayName: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 8,
    textAlign: 'center',
  },
  email: {
    fontSize: 16,
    marginBottom: 16,
    textAlign: 'center',
  },
  userMetadata: {
    gap: 8,
    alignItems: 'center',
  },
  metadataItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  metadataText: {
    fontSize: 13,
    opacity: 0.6,
  },
  centeredLoadingText: {
    fontSize: 16,
    opacity: 0.7,
    textAlign: 'center',
    marginTop: 32,
  },
  preferencesSection: {
    marginBottom: 40,
  },
  sectionTitle: {
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    marginBottom: 20,
    opacity: 0.7,
    lineHeight: 20,
  },
  preferenceItem: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
  },
  preferenceItemEditing: {
    // Dynamic colors will be applied inline
  },
  preferenceHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  preferenceLabel: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 12,
    flex: 1,
  },
  preferenceValue: {
    fontSize: 16,
    opacity: 0.9,
    lineHeight: 22,
  },
  editButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    paddingHorizontal: 24,
    borderRadius: 12,
    marginTop: 16,
  },
  editButtonText: {
    color: '#000000',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  noPreferencesSection: {
    alignItems: 'center',
    paddingVertical: 40,
    marginBottom: 40,
  },
  noPreferencesText: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 8,
  },
  noPreferencesSubtext: {
    fontSize: 14,
    opacity: 0.7,
    textAlign: 'center',
    lineHeight: 20,
    paddingHorizontal: 20,
  },
  accountSection: {
    marginTop: 20,
  },
  passwordChangeButton: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  passwordChangeText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 12,
    flex: 1,
  },
  passwordChangeForm: {
    borderRadius: 12,
    padding: 20,
    marginBottom: 12,
    borderWidth: 1,
  },
  passwordFormTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 20,
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  textInput: {
    borderRadius: 8,
    padding: 14,
    fontSize: 16,
    borderWidth: 1,
  },
  errorText: {
    color: '#FF6B6B',
    fontSize: 14,
    marginBottom: 16,
  },
  passwordFormActions: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 8,
  },
  cancelPasswordButton: {
    flex: 1,
    borderRadius: 8,
    paddingVertical: 14,
    alignItems: 'center',
    borderWidth: 1,
  },
  cancelPasswordText: {
    fontSize: 16,
    fontWeight: '600',
  },
  savePasswordButton: {
    flex: 1,
    borderRadius: 8,
    paddingVertical: 14,
    alignItems: 'center',
    borderWidth: 1,
  },
  savePasswordButtonDisabled: {
    opacity: 0.6,
  },
  savePasswordText: {
    fontSize: 16,
    fontWeight: '600',
  },
  logoutButton: {
    backgroundColor: '#FF6B6B',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    paddingHorizontal: 24,
    borderRadius: 12,
    marginTop: 16,
    marginBottom: 20,
  },
  logoutText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  // Edit styles
  editContainer: {
    marginTop: 12,
  },
  selectContainer: {
    gap: 8,
    marginBottom: 16,
  },
  selectOption: {
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderWidth: 1,
  },
  selectOptionActive: {
    // backgroundColor and borderColor will be set dynamically
  },
  selectOptionText: {
    fontSize: 14,
  },
  selectOptionTextActive: {
    fontWeight: '600',
  },
  numberContainer: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 16,
    flexWrap: 'wrap',
  },
  numberOption: {
    borderRadius: 8,
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
  },
  numberOptionActive: {
    // backgroundColor and borderColor will be set dynamically
  },
  numberOptionText: {
    fontSize: 16,
    fontWeight: '600',
  },
  numberOptionTextActive: {
    fontWeight: '600',
  },
  multiselectContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 16,
  },
  multiselectOption: {
    borderRadius: 16,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderWidth: 1,
  },
  multiselectOptionActive: {
    // backgroundColor and borderColor will be set dynamically
  },
  multiselectOptionText: {
    fontSize: 12,
  },
  multiselectOptionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  exclusionIcon: {
    marginRight: 2,
  },
  multiselectOptionTextActive: {
    color: '#000000',
    fontWeight: '600',
  },
  editActions: {
    flexDirection: 'row',
    gap: 12,
  },
  cancelButton: {
    flex: 1,
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
    borderWidth: 1,
  },
  cancelButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  saveButton: {
    flex: 1,
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
    borderWidth: 1,
  },
  saveButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  // Affiliate styles
  affiliateSection: {
    marginBottom: 40,
  },
  affiliateCard: {
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
  },
  affiliateHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  affiliateHeaderText: {
    marginLeft: 12,
  },
  affiliateTitle: {
    fontSize: 18,
    fontWeight: '700',
  },
  affiliateCode: {
    fontSize: 14,
    marginTop: 2,
  },
  affiliateStats: {
    flexDirection: 'row',
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statDivider: {
    width: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    marginHorizontal: 16,
  },
  statNumber: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
  },
  affiliateLinkContainer: {
    marginBottom: 20,
  },
  linkLabel: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  linkBox: {
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
  },
  linkText: {
    fontSize: 14,
    fontFamily: 'monospace',
  },
  affiliateActions: {
    marginBottom: 20,
  },
  copyButton: {
    borderRadius: 8,
    paddingVertical: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
  },
  copyButtonText: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 6,
  },
  recentReferrals: {
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.1)',
    paddingTop: 20,
  },
  recentReferralsTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  referralItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.05)',
  },
  referralInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  referralStatus: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 12,
  },
  referralAmount: {
    fontSize: 14,
    fontWeight: '600',
    color: primaryColor,
  },
  referralDate: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.6)',
  },
  referralStatusText: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.7)',
    textTransform: 'capitalize',
  },
  // Payout styles
  payoutSection: {
    marginBottom: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.03)',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.08)',
  },
  payoutHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  payoutTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
    flex: 1,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  payoutSetupContainer: {
    alignItems: 'center',
  },
  payoutDescription: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 16,
    lineHeight: 20,
    opacity: 0.8,
  },
  setupButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
    borderWidth: 1,
  },
  setupButtonDisabled: {
    opacity: 0.6,
  },
  setupButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  payoutActiveContainer: {
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
    borderRadius: 8,
    padding: 12,
  },
  payoutInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  payoutActiveText: {
    fontSize: 14,
    color: primaryColor,
    flex: 1,
    lineHeight: 18,
  },
});
