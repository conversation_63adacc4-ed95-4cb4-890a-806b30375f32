import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, ActivityIndicator } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { useSubscription } from '@/contexts/SubscriptionContext';

export default function PaymentSuccessScreen() {
  const { updateSubscriptionStatus } = useSubscription();
  const [processing, setProcessing] = useState(true);

  useEffect(() => {
    const activateSubscription = async () => {
      try {
        await updateSubscriptionStatus('active');

        // Small delay to show success state
        setTimeout(() => {
          router.replace('/(onboarding)/welcome');
        }, 2000);
      } catch (error) {
        console.error('Error activating subscription:', error);
        setProcessing(false);
        // Show error and redirect back to paywall
        setTimeout(() => {
          router.replace('/(paywall)/paywall');
        }, 3000);
      }
    };

    activateSubscription();
  }, []);

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        {processing ? (
          <>
            <Text style={styles.title}>🎉 Payment Successful!</Text>
            <Text style={styles.subtitle}>Activating your subscription...</Text>
            <ActivityIndicator
              size="large"
              color="#FFE598"
              style={styles.loader}
            />
          </>
        ) : (
          <>
            <Text style={styles.title}>❌ Activation Failed</Text>
            <Text style={styles.subtitle}>
              Please contact support. Redirecting...
            </Text>
          </>
        )}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#212121',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 16,
  },
  subtitle: {
    fontSize: 18,
    color: '#B0B0B0',
    textAlign: 'center',
    marginBottom: 32,
  },
  loader: {
    marginTop: 16,
  },
});
