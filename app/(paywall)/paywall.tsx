import React, { useState } from 'react';
import {
  Alert,
  Linking,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

// Use Payment Link URL if available, otherwise construct from price/product ID
const STRIPE_CHECKOUT_URL =
  process.env.EXPO_PUBLIC_STRIPE_PAYMENT_LINK ||
  (process.env.EXPO_PUBLIC_STRIPE_PRODUCT_ID
    ? `https://buy.stripe.com/test_${process.env.EXPO_PUBLIC_STRIPE_PRODUCT_ID}`
    : null);

export default function PaywallScreen() {
  const [loading, setLoading] = useState(false);

  const handleUpgradeToPremium = async () => {
    if (!STRIPE_CHECKOUT_URL) {
      Alert.alert(
        'Configuration Error',
        'Payment system is not properly configured. Please contact support.',
        [{ text: 'OK' }]
      );
      return;
    }

    setLoading(true);
    try {
      // Note: For now, open the payment link directly
      // Stripe Payment Links have success/cancel URLs configured in the Stripe dashboard
      const checkoutUrl = STRIPE_CHECKOUT_URL;

      const supported = await Linking.canOpenURL(checkoutUrl);
      if (supported) {
        await Linking.openURL(checkoutUrl);
        setLoading(false);
      } else {
        Alert.alert(
          'Error',
          'Unable to open payment page. Please check your browser settings.'
        );
        setLoading(false);
      }
    } catch (error) {
      console.error('Error opening Stripe checkout:', error);
      Alert.alert('Error', 'Failed to open payment page. Please try again.');
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>Welcome to MenuMaker!</Text>
        <Text style={styles.subtitle}>Subscribe to get started</Text>

        {/* Single Plan */}
        <View style={styles.planCard}>
          <View style={styles.planHeader}>
            <Text style={styles.planTitle}>MenuMaker Premium</Text>
          </View>

          <Text style={styles.planPrice}>$4.99/month</Text>

          <View style={styles.featuresList}>
            <Text style={styles.feature}>✓ Unlimited meal plans</Text>
            <Text style={styles.feature}>✓ Smart shopping lists</Text>
            <Text style={styles.feature}>✓ Walmart integration</Text>
            <Text style={styles.feature}>✓ Instacart integration</Text>
            <Text style={styles.feature}>✓ Custom dietary preferences</Text>
            <Text style={styles.feature}>✓ Recipe favorites & creation</Text>
            <Text style={styles.feature}>✓ Personal AI assistant</Text>
          </View>

          <TouchableOpacity
            style={[styles.upgradeButton, loading && styles.buttonDisabled]}
            onPress={handleUpgradeToPremium}
            disabled={loading}
          >
            <Text style={styles.upgradeButtonText}>
              {loading ? 'Opening Payment...' : 'Subscribe Now'}
            </Text>
          </TouchableOpacity>
        </View>

        <Text style={styles.disclaimer}>
          Cancel anytime • Secure payment with Stripe
        </Text>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#212121',
  },
  content: {
    flex: 1,
    padding: 24,
    justifyContent: 'center',
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 18,
    color: '#B0B0B0',
    textAlign: 'center',
    marginBottom: 40,
  },
  planCard: {
    backgroundColor: '#2A2A2A',
    borderRadius: 16,
    padding: 24,
    marginBottom: 20,
    borderWidth: 2,
    borderColor: '#FFE598',
  },
  planHeader: {
    alignItems: 'center',
    marginBottom: 16,
  },
  planTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  planPrice: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#FFE598',
    marginBottom: 24,
  },
  featuresList: {
    marginBottom: 24,
  },
  feature: {
    fontSize: 16,
    color: '#FFFFFF',
    marginBottom: 8,
    paddingLeft: 8,
  },
  upgradeButton: {
    borderColor: '#FFE598',
    borderWidth: 2,
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  buttonDisabled: {
    opacity: 0.6,
  },
  upgradeButtonText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFE598',
  },
  disclaimer: {
    fontSize: 12,
    color: '#808080',
    textAlign: 'center',
    marginTop: 20,
    lineHeight: 16,
  },
});
