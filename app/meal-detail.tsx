import { CustomAlert } from '@/components/CustomAlert';
import { EditIngredientsModal } from '@/components/EditIngredientsModal';
import { ThemedText } from '@/components/ThemedText';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { ZenMode } from '@/components/ZenMode';
import { CircularProgress } from '@/components/CircularProgress';
import { Colors } from '@/constants/Colors';
import { useAuth } from '@/contexts/AuthContext';
import { useTheme } from '@/contexts/ThemeContext';
import { getIngredientEmoji } from '@/lib/ingredientEmoji';
import { getMealsByIds } from '@/lib/mealFiltering';
import { getMealCategoryIcons } from '@/lib/mealIcons';
import { supabase } from '@/lib/supabase';
import {
  getWeekStartDate,
  getGlobalWeeklyCycleStartDay,
} from '@/lib/weekCalculation';
import { scaleIngredients } from '@/lib/ingredientScaling';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { router, Stack, useLocalSearchParams } from 'expo-router';
import React, { useEffect, useRef, useState } from 'react';
import {
  ActivityIndicator,
  Modal,
  Pressable,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { Image } from 'expo-image';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import type { Json } from '@/lib/database.types';

// Type definitions for better type safety


// Type guard functions
function isUserCustomizedIngredients(
  value: Json | null
): value is Record<string, any> {
  return value !== null && typeof value === 'object' && !Array.isArray(value);
}

function ensureUserCustomizedIngredients(
  value: Json | null
): Record<string, any> {
  if (isUserCustomizedIngredients(value)) {
    return value;
  }
  return {};
}

function ensureArray(value: Json | null): any[] {
  if (Array.isArray(value)) {
    return value;
  }
  return [];
}

// Fisher-Yates shuffle algorithm for randomizing arrays
const shuffleArray = function <T>(array: T[]): T[] {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
};

function MealDetailScreen() {
  const { activeTheme } = useTheme();
  const colors = Colors[activeTheme];
  const styles = createStyles(colors);
  const insets = useSafeAreaInsets();
  const params = useLocalSearchParams();
  const { session } = useAuth();
  const [isFavorite, setIsFavorite] = useState(false);
  const [showAlert, setShowAlert] = useState(false);
  const [alertConfig, setAlertConfig] = useState({ title: '', message: '' });
  const [mealData, setMealData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const scrollViewRef = useRef<ScrollView>(null);
  const [showDayPicker, setShowDayPicker] = useState(false);
  const [selectedMeal, setSelectedMeal] = useState<{
    id: string;
    name: string;
  } | null>(null);
  const [showEditIngredientsModal, setShowEditIngredientsModal] =
    useState(false);
  const [hasCustomizedIngredients, setHasCustomizedIngredients] =
    useState(false);
  const [originalIngredients, setOriginalIngredients] = useState<any[]>([]);
  const [showZenMode, setShowZenMode] = useState(false);
  const [adjustedServingSize, setAdjustedServingSize] = useState<number | null>(
    null
  );
  const [isSavingServingSize, setIsSavingServingSize] = useState(false);

  // Parse meal data from params
  const mealId = params.mealId as string;
  const fromAI = params.fromAI === 'true';
  const generatedRecipeParam = params.generatedRecipe as string;

  // Parse AI-generated recipe data if provided
  const aiGeneratedRecipe = generatedRecipeParam
    ? (() => {
        try {
          return JSON.parse(decodeURIComponent(generatedRecipeParam));
        } catch {
          return null;
        }
      })()
    : null;

  const formatTime = (hours: number | null, minutes: number | null) => {
    if (!hours && !minutes) return '0 min';
    const h = hours || 0;
    const m = minutes || 0;
    return h > 0 ? `${h}h ${m}m` : `${m} min`;
  };

  // Helper function to check if nutrition section should be hidden
  const shouldHideNutrition = (nutrition: any) => {
    if (!nutrition) return true;

    const calories = parseInt(nutrition.calories) || 0;
    const protein = parseInt(nutrition.protein) || 0;
    const carbs = parseInt(nutrition.carbs) || 0;
    const fat = parseInt(nutrition.fat) || 0;

    return calories === 0 && protein === 0 && carbs === 0 && fat === 0;
  };

  const getImageSource = (image: string | null) => {
    if (!image || image === 'default.jpg') {
      return require('@/assets/default.png');
    }

    // If it's already a full URL, use it as-is
    if (image.startsWith('http')) {
      return { uri: image };
    }

    // Construct Supabase Storage URL for meal images with cache busting
    const timestamp = Date.now();
    return {
      uri: `${process.env.EXPO_PUBLIC_SUPABASE_URL}/storage/v1/object/public/meals/${image}?t=${timestamp}`,
    };
  };

  const getEquipmentIcon = (equipment: string) => {
    const equipmentMap: Record<string, any> = {
      'oven': require('@/assets/icons/icon-oven.png'),
      'skillet': require('@/assets/icons/icon-skillet.png'),
      'pot': require('@/assets/icons/icon-pot.png'),
      'grill': require('@/assets/icons/icon-grill.png'),
      'microwave': require('@/assets/icons/icon-microwave.png'),
      'crockpot': require('@/assets/icons/icon-crockpot.png'),
      'slow cooker': require('@/assets/icons/icon-crockpot.png'),
      'bowl': require('@/assets/icons/icon-bowl.png'),
      'mixing bowl': require('@/assets/icons/icon-bowl.png'),
    };
    
    const equipmentLower = equipment.toLowerCase();
    return equipmentMap[equipmentLower] || null;
  };

  // Fetch actual side dishes from database based on meal data
  const [sideDishes, setSideDishes] = useState<
    {
      id: string;
      name: string;
      image: string | null;
      prepTime: string;
      cookTime: string;
    }[]
  >([]);
  const [addingSideDish, setAddingSideDish] = useState<string | null>(null);

  const loadSideDishes = async (mainCourseMeal: any) => {
    if (!mainCourseMeal) {
      return;
    }

    try {
      const cuisine = mainCourseMeal.cuisine_type || '';

      // First, let's just get ANY side dishes to ensure we always have results
      const { data: allSides, error: allError } = await supabase
        .from('meals')
        .select(
          'id, name, cuisine_type, prep_method, image, prepTimeHour, prepTimeMin, cookTimeHour, cookTimeMin'
        )
        .eq('course', 'Side Dish')
        .limit(100);

      if (allError) {
        setSideDishes([]);
        return;
      }

      if (!allSides || allSides.length === 0) {
        setSideDishes([]);
        return;
      }

      // Try to find cuisine-matched sides
      let matchedSides = allSides.filter(
        (side) => side.cuisine_type === cuisine && cuisine !== ''
      );

      // If not enough cuisine matches, add some popular options
      if (matchedSides.length < 4) {
        const popularSides = allSides.filter(
          (side) =>
            !matchedSides.some((m) => m.id === side.id) &&
            (side.cuisine_type === 'American' ||
              side.cuisine_type === 'Mediterranean' ||
              side.name?.toLowerCase().includes('salad') ||
              side.name?.toLowerCase().includes('rice') ||
              side.name?.toLowerCase().includes('vegetable') ||
              side.name?.toLowerCase().includes('potato'))
        );

        matchedSides = [...matchedSides, ...popularSides];
      }

      // If still not enough, just use any sides
      if (matchedSides.length < 4) {
        matchedSides = allSides;
      }

      // Shuffle and take 4
      const finalSides = shuffleArray([...matchedSides]).slice(0, 4);

      setSideDishes(
        finalSides.map((side) => ({
          id: side.id,
          name: side.name,
          image: side.image,
          prepTime: formatTime(side.prepTimeHour, side.prepTimeMin),
          cookTime: formatTime(side.cookTimeHour, side.cookTimeMin),
        }))
      );
    } catch {
      setSideDishes([]);
    }
  };

  // Parse ingredients from database format or AI-generated format
  const parseIngredients = (ingredients: any) => {
    if (!ingredients) return [];

    let parsedIngredients: { name: string; amount: string; emoji: string }[] =
      [];

    try {
      if (Array.isArray(ingredients)) {
        // Check if it's AI-generated format (simple string array)
        if (typeof ingredients[0] === 'string') {
          // AI-generated recipe format: string array
          parsedIngredients = ingredients.map((ingredient: string) => {
            // Try to parse amount/unit from the string
            const match = ingredient.match(
              /^(\d+\.?\d*\s*(?:tbsp|tsp|cup|cups|lb|lbs|oz|g|kg|ml|l|slice|slices|clove|cloves|pieces?|whole|half|quarter)?)\s*(.+)$/i
            );
            if (match) {
              return {
                name: match[2].trim(),
                amount: match[1].trim(),
                emoji: getIngredientEmoji(match[2]),
              };
            } else {
              return {
                name: ingredient,
                amount: '',
                emoji: getIngredientEmoji(ingredient),
              };
            }
          });
        } else {
          // Handle database format from meals table
          parsedIngredients = ingredients
            .map((ingredient: any) => {
              if (typeof ingredient === 'object' && ingredient !== null) {
                // Handle the database format: {name, unit, amount, emoji, ingredient_id}
                const name = ingredient.name || '';
                const emoji = ingredient.emoji || getIngredientEmoji(name);
                const amount = ingredient.amount;
                const unit = ingredient.unit;

                let amountText = '';
                
                // Handle different cases based on actual data patterns
                if (amount === 0 && unit && unit !== '') {
                  // Case: Salt with amount=0, unit="to taste" - show just the unit
                  amountText = unit;
                } else if (amount === 0 && (!unit || unit === '')) {
                  // Case: amount=0, unit="" - show no amount
                  amountText = '';
                } else if (amount && amount > 0 && unit && unit !== '') {
                  // Case: Normal ingredients with both amount and unit
                  amountText = `${amount} ${unit}`;
                } else if (amount && amount > 0 && (!unit || unit === '')) {
                  // Case: Ingredients with amount but no unit (like "1 yum yum sauce")
                  // Only show amount if it's meaningful (not just "1")
                  if (amount === 1) {
                    amountText = ''; // Don't show "1" for items like "yum yum sauce"
                  } else {
                    amountText = `${amount}`;
                  }
                } else {
                  // All other cases - no amount shown
                  amountText = '';
                }

                return { name, amount: amountText, emoji };
              } else {
                // Handle string format
                const text = String(ingredient);
                const match = text.match(
                  /^(\d+\.?\d*\s*(?:tbsp|tsp|cup|cups|lb|lbs|oz|g|kg|ml|l|slice|slices|clove|cloves|pieces?|whole|half|quarter)?)\s*(.+)$/i
                );
                if (match) {
                  return {
                    name: match[2].trim(),
                    amount: match[1].trim(),
                    emoji: getIngredientEmoji(match[2]),
                  };
                } else {
                  return {
                    name: text,
                    amount: '',
                    emoji: getIngredientEmoji(text),
                  };
                }
              }
            })
            .filter((item) => item.name && item.name.length > 0);
        }
      } else if (typeof ingredients === 'string') {
        // Handle string format (split by newlines)
        parsedIngredients = ingredients
          .split('\n')
          .filter(Boolean)
          .map((text) => {
            const match = text.match(
              /^(\d+\.?\d*\s*(?:tbsp|tsp|cup|cups|lb|lbs|oz|g|kg|ml|l|slice|slices|clove|cloves|pieces?|whole|half|quarter)?)\s*(.+)$/i
            );
            if (match) {
              return {
                name: match[2].trim(),
                amount: match[1].trim(),
                emoji: getIngredientEmoji(match[2]),
              };
            } else {
              return {
                name: text,
                amount: '',
                emoji: getIngredientEmoji(text),
              };
            }
          });
      }
    } catch {}

    // Fallback to mock data if no valid ingredients found
    if (parsedIngredients.length === 0) {
      parsedIngredients = [
        {
          name: 'chicken breast, cut into strips',
          amount: '2 lbs',
          emoji: '🍖',
        },
        { name: 'olive oil', amount: '2 tbsp', emoji: '🫒' },
        { name: 'bell pepper, sliced', amount: '1', emoji: '🌶️' },
        { name: 'onion, sliced', amount: '1', emoji: '🧅' },
        { name: 'garlic, minced', amount: '3 cloves', emoji: '🧄' },
        { name: 'soy sauce', amount: '2 tbsp', emoji: '🥢' },
        { name: 'honey', amount: '1 tbsp', emoji: '🍯' },
        { name: 'ginger, grated', amount: '1 tsp', emoji: '🫚' },
        { name: 'salt and pepper to taste', amount: '', emoji: '🧂' },
        { name: 'green onions, chopped', amount: '2', emoji: '🧅' },
      ];
    }

    return parsedIngredients;
  };

  // Use actual database data or fallback to mock data
  const mealDetails = mealData
    ? {
        name: mealData.name || 'Meal',
        prepTime: formatTime(mealData.prepTimeHour, mealData.prepTimeMin),
        cookTime: formatTime(mealData.cookTimeHour, mealData.cookTimeMin),
        servingSize: mealData.servingSize?.toString() || '4',
        description:
          mealData.description ||
          "A delicious and nutritious meal that's perfect for any day of the week.",
        ingredients: (() => {
          // If serving size is adjusted, always scale from original ingredients
          if (
            adjustedServingSize &&
            mealData.servingSize &&
            adjustedServingSize !== mealData.servingSize
          ) {
            const baseIngredients = parseIngredients(
              originalIngredients.length > 0
                ? originalIngredients
                : mealData.ingredients
            );
            return scaleIngredients(
              baseIngredients,
              mealData.servingSize,
              adjustedServingSize
            );
          }
          // Otherwise, use the meal data ingredients (which might be manually customized)
          return parseIngredients(mealData.ingredients);
        })(),
        instructions: mealData.instructions
          ? Array.isArray(mealData.instructions)
            ? mealData.instructions.filter(Boolean)
            : typeof mealData.instructions === 'string'
              ? mealData.instructions.split('\n').filter(Boolean)
              : [
                  'Heat olive oil in a large skillet or wok over medium-high heat.',
                  'Season chicken strips with salt and pepper, then add to the hot skillet.',
                  'Cook chicken for 5-7 minutes until golden brown and cooked through.',
                  'Add bell pepper and onion to the skillet, cook for 3-4 minutes until softened.',
                  'Add garlic and ginger, cook for another minute until fragrant.',
                  'In a small bowl, whisk together soy sauce and honey.',
                  'Pour sauce over the chicken and vegetables, toss to coat.',
                  'Cook for 2-3 minutes until sauce thickens slightly.',
                  'Garnish with chopped green onions and serve immediately.',
                ]
          : [
              'Heat olive oil in a large skillet or wok over medium-high heat.',
              'Season chicken strips with salt and pepper, then add to the hot skillet.',
              'Cook chicken for 5-7 minutes until golden brown and cooked through.',
              'Add bell pepper and onion to the skillet, cook for 3-4 minutes until softened.',
              'Add garlic and ginger, cook for another minute until fragrant.',
              'In a small bowl, whisk together soy sauce and honey.',
              'Pour sauce over the chicken and vegetables, toss to coat.',
              'Cook for 2-3 minutes until sauce thickens slightly.',
              'Garnish with chopped green onions and serve immediately.',
            ],
        sideDishes: sideDishes.map((side) => side.name),
        nutrition: {
          calories: (mealData.calories || 0).toString(),
          protein: `${mealData.protein || 0}g`,
          carbs: `${mealData.carbs || 0}g`,
          fat: `${mealData.fats || 0}g`,
        },
      }
    : {
        name: 'Loading...',
        prepTime: '...',
        cookTime: '...',
        servingSize: '...',
        description: 'Loading meal details...',
        ingredients: [] as { name: string; amount: string; emoji: string }[],
        instructions: [],
        sideDishes: [],
        nutrition: {
          calories: '...',
          protein: '...',
          carbs: '...',
          fat: '...',
        },
      };

  useEffect(() => {
    if (mealId) {
      loadMealData();
    }
    if (session?.user?.id && mealId) {
      checkFavoriteStatus();
    }
  }, [session, mealId]);

  // Load side dishes when meal data changes
  useEffect(() => {
    if (mealData && !loading) {
      loadSideDishes(mealData);
    }
  }, [mealData, loading]);

  const loadMealData = async () => {
    try {
      setLoading(true);

      let baseMealData: any = null;
      let originalIngredientsData: any[] = [];

      // Check if we have AI-generated recipe data or if this is marked as fromAI
      if (aiGeneratedRecipe) {
        // Convert AI-generated recipe to meal data format
        baseMealData = {
          id: aiGeneratedRecipe.id,
          name: aiGeneratedRecipe.name,
          course: aiGeneratedRecipe.course || 'Main Course',
          prepTimeMin: aiGeneratedRecipe.prepTime
            ? parseInt(aiGeneratedRecipe.prepTime)
            : null,
          cookTimeMin: aiGeneratedRecipe.cookTime
            ? parseInt(aiGeneratedRecipe.cookTime)
            : null,
          prepTimeHour: 0,
          cookTimeHour: 0,
          servingSize: null,
          image: null, // AI-generated recipes don't have images
          ingredients: aiGeneratedRecipe.ingredients,
          instructions: aiGeneratedRecipe.instructions,
          cuisine_type: null,
          dietary_tags: [],
          allergen_contains: [],
          description: null,
          calories: null,
          protein: null,
          carbs: null,
          fats: null,
          isUserRecipe: false,
          isAIGenerated: true, // Flag to indicate AI-generated
        };

        originalIngredientsData = aiGeneratedRecipe.ingredients || [];
      } else if (fromAI && !aiGeneratedRecipe) {
        // Try to fetch AI-generated recipe from database
        const { data: aiRecipe, error: aiError } = await supabase
          .from('ai_generated_meals')
          .select('*')
          .eq('id', mealId)
          .single();

        if (!aiError && aiRecipe) {
          baseMealData = {
            id: aiRecipe.id,
            name: aiRecipe.name,
            course: aiRecipe.course || 'Main Course',
            prepTimeMin: aiRecipe.estimated_prep_time_min,
            cookTimeMin: aiRecipe.estimated_cook_time_min,
            prepTimeHour: 0,
            cookTimeHour: 0,
            servingSize: null,
            image: null,
            ingredients: aiRecipe.ingredients,
            instructions: aiRecipe.instructions,
            cuisine_type: aiRecipe.cuisine_type,
            dietary_tags: aiRecipe.dietary_tags || [],
            allergen_contains: aiRecipe.allergen_contains || [],
            description: null,
            calories: null,
            protein: null,
            carbs: null,
            fats: null,
            isUserRecipe: false,
            isAIGenerated: true,
          };

          originalIngredientsData = ensureArray(aiRecipe.ingredients);
        } else {
          throw new Error('AI-generated meal not found');
        }
      } else {
        // Use unified function to check both meals and user_meals tables
        const meals = await getMealsByIds(
          [mealId],
          session?.user?.id,
          '*, ingredient_sections'
        );

        if (meals.length === 0) {
          throw new Error('Meal not found');
        }

        baseMealData = meals[0];
        originalIngredientsData = baseMealData.ingredients || [];
      }

      // Now check for customized ingredients and apply them
      let finalMealData = baseMealData;
      let hasCustomizations = false;
      let savedServingSize = null;

      if (session?.user?.id && mealId) {
        try {
          const { data: mealPlan, error } = await supabase
            .from('weekly_meal_plans')
            .select('user_customized_ingredients')
            .eq('user_id', session.user.id)
            .eq('is_active', true)
            .single();

          if (!error && mealPlan?.user_customized_ingredients) {
            const customizations = mealPlan.user_customized_ingredients as any;
            const customization = customizations[mealId];

            if (customization) {
              // Check if there's a saved serving size adjustment
              if (customization.adjustedServingSize) {
                savedServingSize = customization.adjustedServingSize;
                hasCustomizations = true;
                // Don't apply the customized ingredients yet - let the rendering logic handle the scaling
                finalMealData = baseMealData;
              } else if (customization.ingredients) {
                // This is a manual ingredient edit, not from serving size
                hasCustomizations = true;
                finalMealData = {
                  ...baseMealData,
                  ingredients: customization.ingredients,
                };
              }
            }
          }
        } catch {
          // Error loading customizations, proceed with original ingredients
        }
      }

      setMealData(finalMealData);
      setOriginalIngredients(originalIngredientsData);
      setHasCustomizedIngredients(hasCustomizations);

      // Set saved serving size if available
      if (savedServingSize) {
        setAdjustedServingSize(savedServingSize);
      }
    } catch {
      setAlertConfig({
        title: 'Error',
        message: 'Failed to load meal details',
      });
      setShowAlert(true);
    } finally {
      setLoading(false);
    }
  };

  const saveServingSizeAdjustment = async (newServingSize: number) => {
    if (!session?.user?.id || !mealId || !mealData || isSavingServingSize)
      return;

    // Don't save if it's the same as original
    if (newServingSize === mealData.servingSize) {
      return;
    }

    setIsSavingServingSize(true);
    try {
      // Get original serving size
      const originalServingSize = mealData.servingSize || 4;

      // Scale the ingredients
      const scaledIngredients = scaleIngredients(
        parseIngredients(originalIngredients),
        originalServingSize,
        newServingSize
      );

      // Get current meal plan
      const { data: mealPlan, error: planError } = await supabase
        .from('weekly_meal_plans')
        .select('id, user_customized_ingredients')
        .eq('user_id', session.user.id)
        .eq('is_active', true)
        .single();

      if (planError || !mealPlan) {
        console.error('No active meal plan found');
        return;
      }

      // Update customized ingredients with serving size info
      const existingCustomizations = ensureUserCustomizedIngredients(
        mealPlan.user_customized_ingredients
      );
      const updatedCustomizations = {
        ...existingCustomizations,
        [mealId]: {
          ingredients: scaledIngredients,
          originalMealId: mealId,
          adjustedServingSize: newServingSize,
          originalServingSize: originalServingSize,
          updatedAt: new Date().toISOString(),
        },
      };

      const { error: updateError } = await supabase
        .from('weekly_meal_plans')
        .update({
          user_customized_ingredients: updatedCustomizations as Json,
          updated_at: new Date().toISOString(),
        })
        .eq('id', mealPlan.id);

      if (updateError) throw updateError;

      // Update local state
      setHasCustomizedIngredients(true);

      // Trigger shopping list refresh
      await AsyncStorage.setItem('mealPlanUpdated', Date.now().toString());
    } catch (error) {
      console.error('Failed to save serving size adjustment:', error);
    } finally {
      setIsSavingServingSize(false);
    }
  };

  const saveCustomizedIngredients = async (customizedIngredients: any[]) => {
    if (!session?.user?.id || !mealId) return;

    try {
      // Get current meal plan
      const { data: mealPlan, error: planError } = await supabase
        .from('weekly_meal_plans')
        .select('id, user_customized_ingredients')
        .eq('user_id', session.user.id)
        .eq('is_active', true)
        .single();

      if (planError || !mealPlan) {
        setAlertConfig({
          title: 'Error',
          message: 'No active meal plan found',
        });
        setShowAlert(true);
        return;
      }

      // Update customized ingredients
      const existingCustomizations = ensureUserCustomizedIngredients(
        mealPlan.user_customized_ingredients
      );
      const updatedCustomizations = {
        ...existingCustomizations,
        [mealId]: {
          ingredients: customizedIngredients,
          originalMealId: mealId,
          updatedAt: new Date().toISOString(),
        },
      };

      const { error: updateError } = await supabase
        .from('weekly_meal_plans')
        .update({
          user_customized_ingredients: updatedCustomizations as Json,
          updated_at: new Date().toISOString(),
        })
        .eq('id', mealPlan.id);

      if (updateError) throw updateError;

      // Update local state
      setHasCustomizedIngredients(true);

      // Update meal data with customized ingredients
      if (mealData) {
        setMealData({
          ...mealData,
          ingredients: customizedIngredients,
        });
      }

      // Trigger shopping list refresh
      await AsyncStorage.setItem('mealPlanUpdated', Date.now().toString());

      setAlertConfig({
        title: 'Ingredients Updated',
        message:
          'Your ingredient changes have been saved and will be reflected in your shopping list.',
      });
      setShowAlert(true);
    } catch {
      setAlertConfig({
        title: 'Error',
        message: 'Failed to save ingredient changes',
      });
      setShowAlert(true);
    }
  };

  const resetToOriginalIngredients = async () => {
    if (!session?.user?.id || !mealId) return;

    try {
      // Get current meal plan
      const { data: mealPlan, error: planError } = await supabase
        .from('weekly_meal_plans')
        .select('id, user_customized_ingredients')
        .eq('user_id', session.user.id)
        .eq('is_active', true)
        .single();

      if (planError || !mealPlan) {
        setAlertConfig({
          title: 'Error',
          message: 'No active meal plan found',
        });
        setShowAlert(true);
        return;
      }

      // Remove customization for this meal
      const existingCustomizations = ensureUserCustomizedIngredients(
        mealPlan.user_customized_ingredients
      );
      const { [mealId]: removed, ...remainingCustomizations } =
        existingCustomizations;

      const { error: updateError } = await supabase
        .from('weekly_meal_plans')
        .update({
          user_customized_ingredients: remainingCustomizations as Json,
          updated_at: new Date().toISOString(),
        })
        .eq('id', mealPlan.id);

      if (updateError) throw updateError;

      // Update local state
      setHasCustomizedIngredients(false);
      setAdjustedServingSize(null);

      // Restore original ingredients
      if (mealData) {
        setMealData({
          ...mealData,
          ingredients: originalIngredients,
        });
      }

      // Trigger shopping list refresh
      await AsyncStorage.setItem('mealPlanUpdated', Date.now().toString());

      setAlertConfig({
        title: 'Ingredients Reset',
        message: 'Ingredients have been restored to the original recipe.',
      });
      setShowAlert(true);
    } catch {
      setAlertConfig({
        title: 'Error',
        message: 'Failed to reset ingredients',
      });
      setShowAlert(true);
    }
  };

  const checkFavoriteStatus = async () => {
    if (!session?.user?.id || !mealId || aiGeneratedRecipe) return; // Skip for AI-generated recipes

    try {
      const { data, error } = await supabase
        .from('user_favorites')
        .select('id')
        .eq('user_id', session.user.id)
        .eq('meal_id', mealId)
        .single();

      setIsFavorite(!!data && !error);
    } catch {}
  };

  const addSideDishToShoppingList = async (
    sideDishId: string,
    sideDishName: string
  ) => {
    if (!session?.user?.id) {
      setAlertConfig({
        title: 'Sign In Required',
        message: 'Please sign in to add ingredients to your shopping list.',
      });
      setShowAlert(true);
      return;
    }

    try {
      setAddingSideDish(sideDishId);

      // Get the side dish details including ingredients and timing
      const { data: sideDish, error: sideDishError } = await supabase
        .from('meals')
        .select(
          'ingredients, prepTimeHour, prepTimeMin, cookTimeHour, cookTimeMin'
        )
        .eq('id', sideDishId)
        .single();

      if (sideDishError) throw sideDishError;

      // Get current week's meal plan to add ingredients to
      const { data: mealPlan, error: planError } = await supabase
        .from('weekly_meal_plans')
        .select('id, additional_ingredients, selected_side_dishes')
        .eq('user_id', session.user.id)
        .eq('is_active', true)
        .single();

      if (planError) {
        setAlertConfig({
          title: 'No Meal Plan Found',
          message: 'You need an active meal plan to add side dish ingredients.',
        });
        setShowAlert(true);
        return;
      }

      // Prepare the side dish ingredients with a source identifier
      const sideDishIngredients = sideDish.ingredients || [];
      const existingAdditional = mealPlan.additional_ingredients || [];
      const existingSideDishes = mealPlan.selected_side_dishes || [];

      // Add source information to identify where these ingredients came from
      const newIngredients = {
        source: 'side_dish',
        source_id: sideDishId,
        source_name: sideDishName,
        ingredients: sideDishIngredients,
        added_at: new Date().toISOString(),
      };

      // Add side dish to selected list (similar to modal)
      const newSideDish = {
        id: sideDishId,
        name: sideDishName,
        prep_time: formatTime(sideDish.prepTimeHour, sideDish.prepTimeMin),
        cook_time: formatTime(sideDish.cookTimeHour, sideDish.cookTimeMin),
        added_at: new Date().toISOString(),
      };

      // Check if this side dish is already added
      const isAlreadyAdded = (existingSideDishes as any[]).some(
        (dish: any) => dish.id === sideDishId
      );
      if (isAlreadyAdded) {
        setAlertConfig({
          title: 'Already Added',
          message: 'This side dish is already in your meal plan.',
        });
        setShowAlert(true);
        return;
      }

      // Update the meal plan with additional ingredients AND selected side dishes
      const { error: updateError } = await supabase
        .from('weekly_meal_plans')
        .update({
          additional_ingredients: [
            ...(existingAdditional as any[]),
            newIngredients,
          ],
          selected_side_dishes: [...(existingSideDishes as any[]), newSideDish],
          updated_at: new Date().toISOString(),
        })
        .eq('id', mealPlan.id);

      if (updateError) throw updateError;

      // Trigger shopping list refresh
      await AsyncStorage.setItem('mealPlanUpdated', Date.now().toString());

      setAlertConfig({
        title: 'Added to Meal Plan',
        message: `"${sideDishName}" has been added to your meal plan and shopping list.`,
      });
      setShowAlert(true);
    } catch {
      setAlertConfig({
        title: 'Error',
        message: 'Failed to add ingredients to shopping list',
      });
      setShowAlert(true);
    } finally {
      setAddingSideDish(null);
    }
  };

  const toggleFavorite = async () => {
    if (!session?.user?.id) {
      setAlertConfig({
        title: 'Sign In Required',
        message: 'Please sign in to save favorites.',
      });
      setShowAlert(true);
      return;
    }

    if (!mealId) {
      setAlertConfig({
        title: 'Error',
        message: 'Unable to save this meal to favorites.',
      });
      setShowAlert(true);
      return;
    }

    try {
      if (isFavorite) {
        const { error } = await supabase
          .from('user_favorites')
          .delete()
          .eq('user_id', session.user.id)
          .eq('meal_id', mealId);

        if (error) throw error;
        setIsFavorite(false);
      } else {
        const { error } = await supabase.from('user_favorites').insert({
          user_id: session.user.id,
          meal_id: mealId,
        });

        if (error) throw error;
        setIsFavorite(true);
      }

      // Set a flag to trigger favorites refresh on other pages
      await AsyncStorage.setItem('favoritesUpdated', Date.now().toString());
    } catch {
      setAlertConfig({ title: 'Error', message: 'Failed to update favorites' });
      setShowAlert(true);
    }
  };

  const handleSwapMeal = () => {
    if (mealData) {
      setSelectedMeal({ id: mealData.id, name: mealData.name });
      setShowDayPicker(true);
    }
  };

  const handleDaySelect = async (day: string) => {
    setShowDayPicker(false);
    if (selectedMeal) {
      await performMealSwap(selectedMeal.id, selectedMeal.name, day);
    }
  };

  const performMealSwap = async (
    newMealId: string,
    mealName: string,
    day: string
  ) => {
    try {
      if (!session?.user?.id) {
        setAlertConfig({
          title: 'Sign In Required',
          message: 'Please sign in to add meals to your plan.',
        });
        setShowAlert(true);
        return;
      }

      // Get current meal plan
      const { data: mealPlan, error } = await supabase
        .from('weekly_meal_plans')
        .select('id, meal_ids')
        .eq('user_id', session.user.id)
        .eq('is_active', true)
        .single();

      const dayIndex = [
        'Sunday',
        'Monday',
        'Tuesday',
        'Wednesday',
        'Thursday',
        'Friday',
        'Saturday',
      ].indexOf(day);

      if (error || !mealPlan) {
        // Create a new meal plan for the week using user's personalized cycle
        const userCycleStartDay = getGlobalWeeklyCycleStartDay();
        const weekStartDate = getWeekStartDate(userCycleStartDay);

        const newMealIds = new Array(7).fill(null);
        newMealIds[dayIndex] = newMealId;

        const { error: insertError } = await supabase
          .from('weekly_meal_plans')
          .insert({
            user_id: session.user.id,
            week_start_date: weekStartDate,
            meal_ids: newMealIds,
            is_active: true,
          });

        if (insertError) {
          setAlertConfig({
            title: 'Error',
            message: 'Failed to create meal plan. Please try again.',
          });
          setShowAlert(true);
          return;
        }
      } else {
        // Update existing meal plan - place meal on specific day
        const updatedMealIds = [
          ...(mealPlan.meal_ids || new Array(7).fill(null)),
        ];
        updatedMealIds[dayIndex] = newMealId;

        // Update the meal plan
        const { error: updateError } = await supabase
          .from('weekly_meal_plans')
          .update({ meal_ids: updatedMealIds })
          .eq('id', mealPlan.id);

        if (updateError) {
          setAlertConfig({
            title: 'Error',
            message: 'Failed to update meal plan. Please try again.',
          });
          setShowAlert(true);
          return;
        }
      }

      // Set a flag to trigger meal plan refresh
      await AsyncStorage.setItem('mealPlanUpdated', Date.now().toString());

      // Success message
      setAlertConfig({
        title: 'Added to Meal Plan',
        message: `"${mealName}" has been added to your ${day} meal plan!`,
      });
      setShowAlert(true);
    } catch {
      setAlertConfig({
        title: 'Error',
        message: 'Something went wrong with the meal swap. Please try again.',
      });
      setShowAlert(true);
    }
  };

  const handleBack = () => {
    router.back();
  };

  const scrollToSideDishes = () => {
    // Try scrolling to a specific position first
    scrollViewRef.current?.scrollTo({
      y: 2000, // Scroll to approximate position
      animated: true,
    });

    // If that doesn't work, scroll to end after a short delay
    setTimeout(() => {
      scrollViewRef.current?.scrollToEnd({ animated: true });
    }, 100);
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <View style={[styles.header, { paddingTop: insets.top + 10 }]}>
          <TouchableOpacity style={styles.backButton} onPress={handleBack}>
            <IconSymbol name="chevron-left" size={24} color={colors.text} />
          </TouchableOpacity>
          <ThemedText type="title" style={styles.headerTitle}>
            Recipe Details
          </ThemedText>
          <View style={styles.headerActions} />
        </View>
        <View style={styles.loadingContainer}>
          <ThemedText>Loading meal details...</ThemedText>
        </View>
      </View>
    );
  }

  // Render ingredients with section support
  const renderIngredients = () => {
    if (!mealDetails.ingredients || mealDetails.ingredients.length === 0) {
      return null;
    }

    // Check if we have ingredient sections (and ingredients is null/empty)
    const hasSections =
      mealData?.ingredient_sections &&
      Array.isArray(mealData.ingredient_sections) &&
      mealData.ingredient_sections.length > 0 &&
      (!mealData.ingredients || mealData.ingredients.length === 0);

    if (hasSections) {
      // Render grouped ingredients
      return mealData.ingredient_sections.map(
        (section: any, sectionIndex: number) => (
          <View key={sectionIndex} style={styles.ingredientSection}>
            {section.title && (
              <View
                style={[
                  styles.sectionHeader,
                  {
                    backgroundColor: colors.primary + '20',
                    borderColor: colors.primary + '30',
                  },
                ]}
              >
                <ThemedText
                  style={[styles.sectionTitle, { color: colors.primary }]}
                >
                  {section.title}
                </ThemedText>
              </View>
            )}
            {section.ingredients &&
              section.ingredients.map((ingredient: any, index: number) => (
                <View
                  key={`${sectionIndex}-${index}`}
                  style={[
                    styles.ingredientContainer,
                    {
                      backgroundColor: colors.cardBackground,
                      borderColor: colors.border,
                    },
                  ]}
                >
                  <Text style={styles.ingredientEmoji}>{ingredient.emoji}</Text>
                  <View style={styles.ingredientTextContainer}>
                    <ThemedText style={styles.ingredientName}>
                      {ingredient.name}
                    </ThemedText>
                    {ingredient.amount && ingredient.amount.toString().trim() !== '' && (
                      <ThemedText style={styles.ingredientAmount}>
                        {ingredient.amount}
                      </ThemedText>
                    )}
                  </View>
                </View>
              ))}
          </View>
        )
      );
    } else {
      // Render flat ingredients (backward compatibility)
      return mealDetails.ingredients.map(
        (
          ingredient: {
            name: string;
            amount: string;
            emoji: string;
          },
          index: number
        ) => (
          <View
            key={index}
            style={[
              styles.ingredientContainer,
              {
                backgroundColor: colors.cardBackground,
                borderColor: colors.border,
              },
            ]}
          >
            <Text style={styles.ingredientEmoji}>{ingredient.emoji}</Text>
            <View style={styles.ingredientTextContainer}>
              <ThemedText style={styles.ingredientName}>
                {ingredient.name}
              </ThemedText>
              {ingredient.amount && ingredient.amount.toString().trim() !== '' && (
                <ThemedText style={styles.ingredientAmount}>
                  {ingredient.amount}
                </ThemedText>
              )}
            </View>
          </View>
        )
      );
    }
  };

  return (
    <>
      <Stack.Screen options={{ headerShown: false }} />
      <View style={styles.container}>
        {/* Header */}
        <View style={[styles.header, { paddingTop: insets.top + 10 }]}>
          <TouchableOpacity style={styles.backButton} onPress={handleBack}>
            <IconSymbol name="chevron-left" size={24} color={colors.text} />
          </TouchableOpacity>
          <ThemedText type="title" style={styles.headerTitle}>
            Recipe Details
          </ThemedText>
          <View style={styles.headerActions}>
            {fromAI && (
              <Pressable
                style={styles.headerActionButton}
                onPress={handleSwapMeal}
              >
                <Ionicons
                  name="add-circle-outline"
                  size={20}
                  color={colors.primary}
                />
              </Pressable>
            )}
            {!aiGeneratedRecipe && !mealData?.isAIGenerated && (
              <Pressable
                style={styles.headerActionButton}
                onPress={toggleFavorite}
              >
                <Ionicons
                  name={isFavorite ? 'heart' : 'heart-outline'}
                  size={20}
                  color={isFavorite ? colors.primary : colors.text}
                />
              </Pressable>
            )}
          </View>
        </View>

        <View style={styles.contentWrapper}>
          <ScrollView
            ref={scrollViewRef}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.scrollContent}
          >
            {/* Meal Image */}
            <View style={styles.mealImageContainer}>
              <Image
                source={getImageSource(mealData?.image)}
                style={styles.mealImage}
                resizeMode="cover"
              />
              
              {/* Equipment Icons Overlay */}
              {mealData?.required_equipment && mealData.required_equipment.length > 0 && (
                <View style={styles.equipmentOverlayContainer}>
                  {mealData.required_equipment.slice(0, 3).map((equipment, idx) => {
                    const icon = getEquipmentIcon(equipment);
                    return icon ? (
                      <View key={idx} style={styles.equipmentOverlayItem}>
                        <Image
                          source={icon}
                          style={styles.equipmentOverlayIcon}
                          contentFit="contain"
                        />
                      </View>
                    ) : null;
                  })}
                </View>
              )}
            </View>

            {/* Meal Info */}
            <View style={styles.content}>
              <ThemedText type="title" style={styles.mealName}>
                {mealDetails.name}
              </ThemedText>

              <ThemedText style={styles.description}>
                {mealDetails.description}
              </ThemedText>

              {/* Category Icons */}
              {mealData && (
                <View style={styles.categoryIconsContainer}>
                  {(() => {
                    const icons = getMealCategoryIcons({
                      course: mealData.course,
                      cuisine_type: mealData.cuisine_type,
                      prep_method: mealData.prep_method,
                    });
                    const capitalizeFirst = (str: string) => {
                      return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
                    };
                    return (
                      <>
                        {(aiGeneratedRecipe || mealData?.isAIGenerated) && (
                          <View style={styles.categoryIcon}>
                            <ThemedText
                              style={[
                                styles.categoryLabel,
                                { color: colors.primary },
                              ]}
                            >
                              AI Generated
                            </ThemedText>
                          </View>
                        )}
                        <View style={styles.categoryIcon}>
                          <ThemedText style={styles.categoryLabel}>
                            {capitalizeFirst(icons.cuisine.label)}
                          </ThemedText>
                        </View>
                        <View style={styles.categoryIcon}>
                          <ThemedText style={styles.categoryLabel}>
                            {capitalizeFirst(icons.prepMethod.label)}
                          </ThemedText>
                        </View>
                      </>
                    );
                  })()}
                </View>
              )}

              {/* Recommended Side Dishes Button - Only show if there are side dishes */}
              {sideDishes.length > 0 && (
                <TouchableOpacity
                  style={styles.sideDishesButton}
                  onPress={scrollToSideDishes}
                >
                  <Ionicons
                    name="restaurant-outline"
                    size={16}
                    color={colors.primary}
                  />
                  <ThemedText style={styles.sideDishesButtonText}>
                    Recommended Side Dishes
                  </ThemedText>
                  <Ionicons
                    name="chevron-down"
                    size={16}
                    color={colors.primary}
                  />
                </TouchableOpacity>
              )}

              {/* Quick Stats */}
              <View style={styles.statsContainer}>
                <View style={styles.statItem}>
                  <IconSymbol
                    name="clock-outline"
                    size={20}
                    color={colors.primary}
                  />
                  <ThemedText style={styles.statLabel}>Prep</ThemedText>
                  <ThemedText style={styles.statValue}>
                    {mealDetails.prepTime}
                  </ThemedText>
                </View>
                <View style={styles.statItem}>
                  <IconSymbol name="fire" size={20} color={colors.primary} />
                  <ThemedText style={styles.statLabel}>Cook</ThemedText>
                  <ThemedText style={styles.statValue}>
                    {mealDetails.cookTime}
                  </ThemedText>
                </View>
                <View style={styles.statItem}>
                  <IconSymbol
                    name="account-group"
                    size={20}
                    color={colors.primary}
                  />
                  <ThemedText style={styles.statLabel}>Serves</ThemedText>
                  <ThemedText style={styles.statValue}>
                    {adjustedServingSize || mealDetails.servingSize}
                  </ThemedText>
                </View>
              </View>

              {/* Nutrition - only show if has meaningful values */}
              {!shouldHideNutrition(mealDetails.nutrition) && (
                <View style={styles.section}>
                  <ThemedText type="subtitle" style={styles.sectionTitle}>
                    Nutrition (per serving)
                  </ThemedText>
                  <View style={styles.nutritionGrid}>
                    <CircularProgress
                      size={80}
                      strokeWidth={6}
                      progress={Math.min(
                        parseInt(mealDetails.nutrition.calories) / 800,
                        1
                      )} // Assuming 800 cal as high reference
                      color={colors.primary}
                      backgroundColor={
                        activeTheme === 'dark'
                          ? colors.primary + '20'
                          : colors.primary + '15'
                      }
                      value={mealDetails.nutrition.calories}
                      label="Calories"
                    />
                    <CircularProgress
                      size={80}
                      strokeWidth={6}
                      progress={Math.min(
                        parseInt(mealDetails.nutrition.protein) / 50,
                        1
                      )} // 50g protein as reference
                      color="#F44336"
                      backgroundColor={
                        activeTheme === 'dark' ? '#F4433620' : '#F4433615'
                      }
                      value={mealDetails.nutrition.protein}
                      label="Protein"
                    />
                    <CircularProgress
                      size={80}
                      strokeWidth={6}
                      progress={Math.min(
                        parseInt(mealDetails.nutrition.carbs) / 100,
                        1
                      )} // 100g carbs as reference
                      color="#2196F3"
                      backgroundColor={
                        activeTheme === 'dark' ? '#2196F320' : '#2196F315'
                      }
                      value={mealDetails.nutrition.carbs}
                      label="Carbs"
                    />
                    <CircularProgress
                      size={80}
                      strokeWidth={6}
                      progress={Math.min(
                        parseInt(mealDetails.nutrition.fat) / 35,
                        1
                      )} // 35g fat as reference
                      color="#FF9800"
                      backgroundColor={
                        activeTheme === 'dark' ? '#FF980020' : '#FF980015'
                      }
                      value={mealDetails.nutrition.fat}
                      label="Fat"
                    />
                  </View>
                </View>
              )}

              {/* Ingredients */}
              <View style={styles.section}>
                <View style={styles.ingredientsSectionHeader}>
                  <ThemedText type="subtitle" style={styles.sectionTitleInline}>
                    Ingredients
                  </ThemedText>
                  <View style={styles.ingredientIndicators}>
                    {adjustedServingSize &&
                      adjustedServingSize !==
                        parseInt(mealDetails.servingSize) && (
                        <View
                          style={[
                            styles.customizedIndicator,
                            { backgroundColor: `${colors.primary}10` },
                          ]}
                        >
                          <Ionicons
                            name="calculator-outline"
                            size={14}
                            color={colors.primary}
                          />
                          <ThemedText
                            style={[
                              styles.customizedText,
                              { color: colors.primary },
                            ]}
                          >
                            Scaled
                          </ThemedText>
                        </View>
                      )}
                    {hasCustomizedIngredients && (
                      <View
                        style={[
                          styles.customizedIndicator,
                          { backgroundColor: `${colors.primary}10` },
                        ]}
                      >
                        <Ionicons
                          name="pencil"
                          size={14}
                          color={colors.primary}
                        />
                        <ThemedText
                          style={[
                            styles.customizedText,
                            { color: colors.primary },
                          ]}
                        >
                          Customized
                        </ThemedText>
                      </View>
                    )}
                  </View>
                </View>
                
                {/* Serving Size Adjustment */}
                <View style={styles.servingAdjustmentContainer}>
                  <ThemedText style={styles.servingAdjustmentText}>
                    Adjust servings
                  </ThemedText>
                  <View style={styles.servingAdjustmentControls}>
                    <TouchableOpacity
                      style={[
                        styles.servingAdjustmentButton,
                        {
                          backgroundColor: colors.cardBackground,
                          borderColor: colors.border,
                          opacity: 
                            isSavingServingSize ||
                            (adjustedServingSize ||
                              parseInt(mealDetails.servingSize) ||
                              4) <= 2
                              ? 0.5 : 1
                        }
                      ]}
                      onPress={() => {
                        const currentSize =
                          adjustedServingSize ||
                          parseInt(mealDetails.servingSize) ||
                          4;
                        if (currentSize > 2) {
                          const newSize = currentSize - 1;
                          setAdjustedServingSize(newSize);
                          saveServingSizeAdjustment(newSize);
                        }
                      }}
                      disabled={
                        isSavingServingSize ||
                        (adjustedServingSize ||
                          parseInt(mealDetails.servingSize) ||
                          4) <= 2
                      }
                    >
                      <Ionicons
                        name="remove"
                        size={20}
                        color={colors.primary}
                      />
                    </TouchableOpacity>
                    <ThemedText style={styles.servingAdjustmentValue}>
                      {adjustedServingSize || mealDetails.servingSize}
                    </ThemedText>
                    <TouchableOpacity
                      style={[
                        styles.servingAdjustmentButton,
                        {
                          backgroundColor: colors.cardBackground,
                          borderColor: colors.border,
                          opacity: isSavingServingSize ? 0.5 : 1
                        }
                      ]}
                      onPress={() => {
                        const currentSize =
                          adjustedServingSize ||
                          parseInt(mealDetails.servingSize) ||
                          4;
                        if (currentSize < 20) {
                          const newSize = currentSize + 1;
                          setAdjustedServingSize(newSize);
                          saveServingSizeAdjustment(newSize);
                        }
                      }}
                      disabled={isSavingServingSize}
                    >
                      <Ionicons
                        name="add"
                        size={20}
                        color={colors.primary}
                      />
                    </TouchableOpacity>
                  </View>
                </View>
                
                <View style={styles.ingredientsColumn}>
                  {renderIngredients()}
                </View>

                {/* Edit and Reset Buttons */}
                <View style={styles.ingredientActions}>
                  <TouchableOpacity
                    style={[
                      styles.ingredientActionButton,
                      {
                        backgroundColor: 'transparent',
                        borderColor: colors.primary,
                        borderWidth: 1,
                      },
                    ]}
                    onPress={() => setShowEditIngredientsModal(true)}
                  >
                    <Ionicons
                      name="pencil-outline"
                      size={16}
                      color={colors.primary}
                    />
                    <ThemedText
                      style={[
                        styles.ingredientActionText,
                        { color: colors.primary },
                      ]}
                    >
                      Edit Ingredients
                    </ThemedText>
                  </TouchableOpacity>

                  {(hasCustomizedIngredients ||
                    (adjustedServingSize &&
                      adjustedServingSize !==
                        parseInt(mealDetails.servingSize))) && (
                    <TouchableOpacity
                      style={[
                        styles.ingredientActionButton,
                        {
                          backgroundColor: colors.background,
                          borderColor: colors.border,
                          borderWidth: 1,
                        },
                      ]}
                      onPress={() => {
                        setAlertConfig({
                          title: 'Reset Ingredients',
                          message:
                            'This will restore the ingredients back to the original recipe and reset serving size. Your customizations will be lost. Continue?',
                        });
                        setShowAlert(true);
                      }}
                    >
                      <Ionicons
                        name="refresh-outline"
                        size={16}
                        color={colors.text}
                      />
                      <ThemedText
                        style={[styles.ingredientActionText, { opacity: 0.7 }]}
                      >
                        Reset
                      </ThemedText>
                    </TouchableOpacity>
                  )}
                </View>
              </View>

              {/* Instructions */}
              <View style={styles.section}>
                <View style={styles.instructionsSectionHeader}>
                  <ThemedText
                    type="subtitle"
                    style={[styles.sectionTitle, { marginBottom: 0 }]}
                  >
                    Instructions
                  </ThemedText>
                  <TouchableOpacity
                    style={styles.zenModeButton}
                    onPress={() => setShowZenMode(true)}
                  >
                    <Ionicons
                      name="expand-outline"
                      size={20}
                      color={colors.primary}
                    />
                    <ThemedText style={styles.zenModeText}>
                      Cook Mode
                    </ThemedText>
                  </TouchableOpacity>
                </View>
                {mealDetails.instructions.map(
                  (instruction: string, index: number) => (
                    <View key={index} style={styles.instructionItem}>
                      <View
                        style={[
                          styles.stepNumber,
                          { backgroundColor: colors.primary },
                        ]}
                      >
                        <Text style={styles.stepNumberText}>{index + 1}</Text>
                      </View>
                      <ThemedText style={styles.instructionText}>
                        {instruction}
                      </ThemedText>
                    </View>
                  )
                )}
              </View>

              {/* Side Dishes - Only show if there are side dishes */}
              {sideDishes.length > 0 && (
                <View style={[styles.section, { marginBottom: 0 }]}>
                  <ThemedText type="subtitle" style={styles.sectionTitle}>
                    Recommended Side Dishes
                  </ThemedText>
                  <View style={styles.sideDishesGrid}>
                    {sideDishes.map((dish) => (
                      <View
                        key={dish.id}
                        style={[
                          styles.sideDishItem,
                          {
                            backgroundColor: colors.cardBackground,
                            borderColor: colors.border,
                          },
                        ]}
                      >
                        <TouchableOpacity
                          style={styles.sideDishHeader}
                          onPress={() =>
                            router.push({
                              pathname: '/side-dish-detail',
                              params: {
                                mealId: dish.id,
                              },
                            })
                          }
                        >
                          <Image
                            source={getImageSource(dish.image)}
                            style={styles.sideDishImage}
                            resizeMode="cover"
                          />
                          <View style={styles.sideDishTextContainer}>
                            <ThemedText style={styles.sideDishText}>
                              {dish.name}
                            </ThemedText>
                            <View style={styles.sideDishTimingContainer}>
                              <View style={styles.sideDishTimeItem}>
                                <IconSymbol
                                  name="clock-outline"
                                  size={12}
                                  color={colors.primary}
                                />
                                <ThemedText style={styles.sideDishTimeText}>
                                  {dish.prepTime}
                                </ThemedText>
                              </View>
                              <View style={styles.sideDishTimeItem}>
                                <IconSymbol
                                  name="fire"
                                  size={12}
                                  color={colors.primary}
                                />
                                <ThemedText style={styles.sideDishTimeText}>
                                  {dish.cookTime}
                                </ThemedText>
                              </View>
                            </View>
                          </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                          style={[
                            styles.addIngredientsButton,
                            {
                              backgroundColor: 'transparent',
                              borderColor: colors.primary,
                              borderWidth: 1,
                            },
                          ]}
                          onPress={() =>
                            addSideDishToShoppingList(dish.id, dish.name)
                          }
                          disabled={addingSideDish === dish.id}
                        >
                          {addingSideDish === dish.id ? (
                            <ActivityIndicator
                              size="small"
                              color={colors.primary}
                            />
                          ) : (
                            <>
                              <Ionicons
                                name="add-circle-outline"
                                size={14}
                                color={colors.primary}
                              />
                              <ThemedText
                                style={[
                                  styles.addIngredientsButtonText,
                                  { color: colors.primary },
                                ]}
                              >
                                Add to Ingredients
                              </ThemedText>
                            </>
                          )}
                        </TouchableOpacity>
                      </View>
                    ))}
                  </View>
                </View>
              )}
            </View>
          </ScrollView>
        </View>

        <CustomAlert
          visible={showAlert}
          title={alertConfig.title}
          message={alertConfig.message}
          onConfirm={() => {
            setShowAlert(false);
            if (alertConfig.title === 'Reset Ingredients') {
              resetToOriginalIngredients();
            }
          }}
          onCancel={() => setShowAlert(false)}
        />

        {/* Edit Ingredients Modal */}
        <EditIngredientsModal
          visible={showEditIngredientsModal}
          ingredients={mealDetails.ingredients}
          onClose={() => setShowEditIngredientsModal(false)}
          onSave={saveCustomizedIngredients}
          mealName={mealDetails.name}
        />

        {/* Zen Mode */}
        <ZenMode
          visible={showZenMode}
          instructions={mealDetails.instructions}
          onClose={() => setShowZenMode(false)}
          mealName={mealDetails.name}
        />

        {/* Day Picker Modal - only shown when coming from AI */}
        {fromAI && (
          <Modal
            visible={showDayPicker}
            transparent={true}
            animationType="slide"
            onRequestClose={() => setShowDayPicker(false)}
          >
            <Pressable
              style={styles.modalOverlay}
              onPress={() => setShowDayPicker(false)}
            >
              <View
                style={[
                  styles.modalContent,
                  { backgroundColor: colors.cardBackground },
                ]}
              >
                <ThemedText style={styles.modalTitle}>
                  Choose a day for {selectedMeal?.name}
                </ThemedText>

                {[
                  'Sunday',
                  'Monday',
                  'Tuesday',
                  'Wednesday',
                  'Thursday',
                  'Friday',
                  'Saturday',
                ].map((day) => (
                  <TouchableOpacity
                    key={day}
                    style={[
                      styles.dayOption,
                      { borderBottomColor: colors.border },
                    ]}
                    onPress={() => handleDaySelect(day)}
                  >
                    <ThemedText style={styles.dayText}>{day}</ThemedText>
                    <Ionicons
                      name="chevron-forward"
                      size={20}
                      color={colors.text}
                    />
                  </TouchableOpacity>
                ))}

                <TouchableOpacity
                  style={styles.cancelButton}
                  onPress={() => setShowDayPicker(false)}
                >
                  <ThemedText
                    style={[styles.cancelText, { color: colors.text }]}
                  >
                    Cancel
                  </ThemedText>
                </TouchableOpacity>
              </View>
            </Pressable>
          </Modal>
        )}
      </View>
    </>
  );
}

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    loadingContainer: {
      height: '100%',
      justifyContent: 'center',
      alignItems: 'center',
      padding: 24,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 24,
      paddingBottom: 16,
      justifyContent: 'space-between',
      backgroundColor: colors.background,
    },
    backButton: {
      padding: 8,
    },
    headerTitle: {
      flex: 1,
      textAlign: 'center',
      marginHorizontal: 16,
      fontSize: 18,
      fontWeight: '600',
    },
    headerActions: {
      flexDirection: 'row',
      gap: 8,
    },
    headerActionButton: {
      padding: 8,
      borderRadius: 8,
      backgroundColor: `${colors.primary}10`,
      borderWidth: 1,
      borderColor: `${colors.primary}30`,
      opacity: 0.8,
    },
    contentWrapper: {
      flex: 1,
      backgroundColor: colors.background,
    },
    mealImageContainer: {
      position: 'relative',
      width: '100%',
      height: 250,
      marginBottom: 24,
    },
    mealImage: {
      width: '100%',
      height: '100%',
      resizeMode: 'cover',
    },
    equipmentOverlayContainer: {
      position: 'absolute',
      bottom: 12,
      right: 12,
      flexDirection: 'row',
      gap: 4,
    },
    equipmentOverlayItem: {
      width: 28,
      height: 28,
      borderRadius: 14,
      backgroundColor: 'rgba(255, 255, 255, 0.9)',
      borderWidth: 1,
      borderColor: 'rgba(0, 0, 0, 0.1)',
      alignItems: 'center',
      justifyContent: 'center',
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    },
    equipmentOverlayIcon: {
      width: 18,
      height: 18,
    },
    content: {
      paddingHorizontal: 24,
    },
    mealName: {
      marginBottom: 12,
    },
    description: {
      fontSize: 16,
      lineHeight: 24,
      opacity: 0.7,
      marginBottom: 24,
    },
    statsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: 32,
      paddingVertical: 20,
    },
    statItem: {
      alignItems: 'center',
      flex: 1,
    },
    statLabel: {
      fontSize: 16,
      opacity: 0.6,
      marginTop: 4,
    },
    statValue: {
      fontSize: 18,
      fontWeight: '600',
      marginTop: 2,
    },
    section: {
      marginBottom: 32,
    },
    sectionTitle: {
      marginBottom: 16,
      fontSize: 18,
      fontWeight: '600',
    },
    sectionTitleInline: {
      marginBottom: 0,
      fontSize: 18,
      fontWeight: '600',
    },
    nutritionGrid: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      paddingVertical: 8,
    },
    ingredientsColumn: {
      gap: 12,
    },
    ingredientContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderRadius: 8,
      borderWidth: 1,
      gap: 12,
    },
    ingredientEmoji: {
      fontSize: 18,
    },
    ingredientTextContainer: {
      flex: 1,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    ingredientName: {
      fontSize: 16,
      fontWeight: '500',
      flex: 1,
    },
    ingredientAmount: {
      fontSize: 16,
      fontWeight: '600',
      color: colors.primary,
      marginLeft: 8,
    },
    ingredientSection: {
      marginBottom: 16,
    },
    sectionHeader: {
      paddingHorizontal: 12,
      paddingVertical: 8,
      borderRadius: 6,
      borderWidth: 1,
      marginBottom: 8,
    },
    instructionItem: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      marginBottom: 16,
    },
    stepNumber: {
      width: 28,
      height: 28,
      borderRadius: 14,
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: 12,
      marginTop: 2,
    },
    stepNumberText: {
      color: colors.background,
      fontSize: 14,
      fontWeight: '600',
    },
    instructionText: {
      flex: 1,
      fontSize: 16,
      lineHeight: 24,
      paddingTop: 3,
    },
    sideDishesGrid: {
      flexDirection: 'column',
      gap: 12,
    },
    sideDishItem: {
      flexDirection: 'column',
      padding: 12,
      borderRadius: 8,
      borderWidth: 1,
      width: '100%',
    },
    sideDishHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 12,
      marginBottom: 8,
    },
    sideDishImage: {
      width: 80,
      height: 80,
      borderRadius: 8,
    },
    sideDishTextContainer: {
      flex: 1,
    },
    sideDishText: {
      fontSize: 14,
      fontWeight: '500',
      marginBottom: 4,
    },
    sideDishTimingContainer: {
      flexDirection: 'row',
      gap: 12,
    },
    sideDishTimeItem: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 4,
    },
    sideDishTimeText: {
      fontSize: 12,
      opacity: 0.7,
    },
    addIngredientsButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 6,
      paddingHorizontal: 8,
      borderRadius: 6,
      gap: 4,
    },
    addIngredientsButtonText: {
      fontSize: 12,
      fontWeight: '600',
    },
    scrollContent: {
      paddingBottom: 24,
    },
    sideDishesButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 12,
      paddingHorizontal: 16,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: colors.primary,
      backgroundColor: 'transparent',
      marginBottom: 24,
      gap: 8,
    },
    sideDishesButtonText: {
      fontSize: 14,
      fontWeight: '600',
      color: colors.primary,
    },
    categoryIconsContainer: {
      flexDirection: 'row',
      gap: 12,
      marginBottom: 24,
      flexWrap: 'wrap',
    },
    categoryIcon: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 6,
      backgroundColor: `${colors.primary}10`,
      paddingHorizontal: 12,
      paddingVertical: 8,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: `${colors.primary}30`,
    },
    categoryLabel: {
      fontSize: 12,
      fontWeight: '500',
      color: colors.text,
      opacity: 0.9,
    },
    modalOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'flex-end',
    },
    modalContent: {
      borderTopLeftRadius: 20,
      borderTopRightRadius: 20,
      padding: 24,
      paddingBottom: 40,
    },
    modalTitle: {
      fontSize: 20,
      fontWeight: '600',
      marginBottom: 20,
      textAlign: 'center',
    },
    dayOption: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: 16,
      borderBottomWidth: 1,
    },
    dayText: {
      fontSize: 18,
    },
    cancelButton: {
      marginTop: 20,
      paddingVertical: 12,
      alignItems: 'center',
    },
    cancelText: {
      fontSize: 16,
      opacity: 0.7,
    },
    ingredientsSectionHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 16,
      minHeight: 24,
    },
    customizedIndicator: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 4,
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 8,
    },
    customizedText: {
      fontSize: 12,
      fontWeight: '500',
    },
    ingredientActions: {
      flexDirection: 'row',
      gap: 12,
      marginTop: 16,
    },
    ingredientActionButton: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 12,
      paddingHorizontal: 16,
      borderRadius: 8,
      gap: 6,
    },
    ingredientActionText: {
      fontSize: 14,
      fontWeight: '600',
    },
    instructionsSectionHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 16,
    },
    zenModeButton: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 6,
      paddingHorizontal: 12,
      paddingVertical: 8,
      borderRadius: 8,
      backgroundColor: `${colors.primary}10`,
      borderWidth: 1,
      borderColor: `${colors.primary}30`,
    },
    zenModeText: {
      fontSize: 14,
      fontWeight: '500',
      color: colors.primary,
    },
    ingredientIndicators: {
      flexDirection: 'row',
      gap: 8,
    },
    servingAdjustmentContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: 16,
      paddingHorizontal: 16,
      paddingVertical: 12,
      backgroundColor: `${colors.primary}10`,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: `${colors.primary}20`,
    },
    servingAdjustmentText: {
      fontSize: 14,
      fontWeight: '500',
      color: colors.text,
      opacity: 0.8,
    },
    servingAdjustmentControls: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 12,
    },
    servingAdjustmentButton: {
      width: 36,
      height: 36,
      borderRadius: 18,
      borderWidth: 1,
      alignItems: 'center',
      justifyContent: 'center',
    },
    servingAdjustmentValue: {
      fontSize: 16,
      fontWeight: '600',
      minWidth: 20,
      textAlign: 'center',
    },
    equipmentContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 8,
      marginBottom: 16,
    },
    equipmentItem: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: '#F5F5F5',
      borderWidth: 1,
      borderColor: '#E0E0E0',
      alignItems: 'center',
      justifyContent: 'center',
      padding: 4,
    },
    equipmentIcon: {
      width: 28,
      height: 28,
    },
  });

export default function WrappedMealDetailScreen() {
  return <MealDetailScreen />;
}
