import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Text,
  ScrollView,
} from 'react-native';
import { router } from 'expo-router';
import { ThemedView } from '@/components/ThemedView';
import { ThemedText } from '@/components/ThemedText';
import { Colors } from '@/constants/Colors';
import { useTheme } from '@/contexts/ThemeContext';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useOnboarding } from '@/contexts/OnboardingContext';

const HOUSEHOLD_SIZES = [1, 2, 3, 4, 5, 6];
const COOKING_TIMES = [
  '15 min',
  '30 min',
  '45 min',
  '1+ hour',
  'No preference',
];
const SEASONAL_PREFERENCES = [
  { value: 'automatic', label: 'Seasonal (Auto)', emoji: '🌍' },
  { value: 'spring_foods', label: 'Spring Foods', emoji: '🌸' },
  { value: 'summer_foods', label: 'Summer Foods', emoji: '🌞' },
  { value: 'fall_foods', label: 'Fall Foods', emoji: '🍂' },
  { value: 'winter_foods', label: 'Winter Foods', emoji: '❄️' },
  { value: 'no_preference', label: 'Year-Round', emoji: '🗓️' },
];
const KITCHEN_EQUIPMENT = [
  'Air Fryer',
  'Slow Cooker',
  'Instant Pot',
  'Stand Mixer',
  'Food Processor',
  'Blender',
  'Grill',
  'Rice Cooker',
  'Dutch Oven',
  'Cast Iron Pan',
];

export default function HouseholdScreen() {
  const { activeTheme } = useTheme();
  const colors = Colors[activeTheme];
  const styles = createStyles(colors);
  const {
    state,
    updateHouseholdDetails,
    updateKitchenEquipment,
    updateSeasonalPreference,
  } = useOnboarding();
  const [householdSize, setHouseholdSize] = useState<number | null>(
    state.householdSize
  );
  const [cookingTime, setCookingTime] = useState<string | null>(
    state.cookingTime
  );
  const [kitchenEquipment, setKitchenEquipment] = useState<string[]>(
    state.kitchenEquipment
  );
  const [seasonalPreference, setSeasonalPreference] = useState<string | null>(
    state.seasonalPreference
  );

  useEffect(() => {
    setHouseholdSize(state.householdSize);
    setCookingTime(state.cookingTime);
    setKitchenEquipment(state.kitchenEquipment);
    setSeasonalPreference(state.seasonalPreference);
  }, [
    state.householdSize,
    state.cookingTime,
    state.kitchenEquipment,
    state.seasonalPreference,
  ]);

  const toggleEquipment = (item: string) => {
    if (kitchenEquipment.includes(item)) {
      setKitchenEquipment(kitchenEquipment.filter((i) => i !== item));
    } else {
      setKitchenEquipment([...kitchenEquipment, item]);
    }
  };

  const handleContinue = () => {
    if (householdSize) {
      updateHouseholdDetails(
        householdSize,
        cookingTime || 'No preference',
        'intermediate'
      ); // Default skill level
    }
    updateKitchenEquipment(kitchenEquipment);
    if (seasonalPreference) {
      updateSeasonalPreference(seasonalPreference);
    }
    router.push('/(onboarding)/final-setup');
  };

  const handleBack = () => {
    router.back();
  };

  return (
    <ThemedView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={[styles.backButton, { backgroundColor: colors.primary }]}
          onPress={handleBack}
        >
          <IconSymbol name="chevron-left" size={20} color={colors.background} />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <ThemedText
          type="title"
          style={[styles.title, { color: colors.primary }]}
        >
          Household Details
        </ThemedText>

        <ThemedText style={styles.subtitle}>
          Help us plan the right portions and complexity
        </ThemedText>

        <View style={styles.section}>
          <ThemedText type="subtitle" style={styles.sectionTitle}>
            How many people are you cooking for?
          </ThemedText>

          <View style={styles.optionsGrid}>
            {HOUSEHOLD_SIZES.map((size) => (
              <TouchableOpacity
                key={size}
                style={[
                  styles.compactOption,
                  { borderColor: colors.border },
                  householdSize === size && {
                    backgroundColor: colors.primary,
                    borderColor: colors.primary,
                  },
                ]}
                onPress={() => setHouseholdSize(size)}
              >
                <ThemedText
                  style={[
                    styles.compactOptionText,
                    householdSize === size && { color: colors.background },
                  ]}
                >
                  {size}
                </ThemedText>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <View style={styles.section}>
          <ThemedText type="subtitle" style={styles.sectionTitle}>
            How much time do you have for cooking?
          </ThemedText>

          <View style={styles.optionsGrid}>
            {COOKING_TIMES.map((time) => (
              <TouchableOpacity
                key={time}
                style={[
                  styles.compactOption,
                  { borderColor: colors.border },
                  cookingTime === time && {
                    backgroundColor: colors.primary,
                    borderColor: colors.primary,
                  },
                ]}
                onPress={() => setCookingTime(time)}
              >
                <ThemedText
                  style={[
                    styles.compactOptionText,
                    cookingTime === time && { color: colors.background },
                  ]}
                >
                  {time}
                </ThemedText>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <View style={styles.section}>
          <ThemedText type="subtitle" style={styles.sectionTitle}>
            Seasonal Food Preferences
          </ThemedText>
          <ThemedText style={styles.sectionSubtitle}>
            Choose how you want meals to match the seasons
          </ThemedText>

          <View style={styles.optionsGrid}>
            {SEASONAL_PREFERENCES.map((preference) => (
              <TouchableOpacity
                key={preference.value}
                style={[
                  styles.seasonalOption,
                  { borderColor: colors.border },
                  seasonalPreference === preference.value && {
                    backgroundColor: colors.primary,
                    borderColor: colors.primary,
                  },
                ]}
                onPress={() => setSeasonalPreference(preference.value)}
              >
                <Text
                  style={[
                    styles.seasonalEmoji,
                    seasonalPreference === preference.value && { opacity: 1 },
                  ]}
                >
                  {preference.emoji}
                </Text>
                <ThemedText
                  style={[
                    styles.seasonalText,
                    seasonalPreference === preference.value && {
                      color: colors.background,
                    },
                  ]}
                >
                  {preference.label}
                </ThemedText>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <View style={styles.section}>
          <ThemedText type="subtitle" style={styles.sectionTitle}>
            Kitchen Equipment
          </ThemedText>
          <ThemedText style={styles.sectionSubtitle}>
            Select the equipment you have available (optional)
          </ThemedText>

          <View style={styles.equipmentGrid}>
            {KITCHEN_EQUIPMENT.map((equipment) => (
              <TouchableOpacity
                key={equipment}
                style={[
                  styles.equipmentOption,
                  { borderColor: colors.border },
                  kitchenEquipment.includes(equipment) && {
                    backgroundColor: colors.primary,
                    borderColor: colors.primary,
                  },
                ]}
                onPress={() => toggleEquipment(equipment)}
              >
                <ThemedText
                  style={[
                    styles.equipmentText,
                    kitchenEquipment.includes(equipment) && {
                      color: colors.background,
                    },
                  ]}
                >
                  {equipment}
                </ThemedText>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </ScrollView>

      <View style={styles.footer}>
        <TouchableOpacity
          style={[styles.continueButton, { backgroundColor: colors.primary }]}
          onPress={handleContinue}
        >
          <Text
            style={[styles.continueButtonText, { color: colors.background }]}
          >
            Continue
          </Text>
        </TouchableOpacity>

        <View style={styles.progress}>
          <View
            style={[styles.progressDot, { backgroundColor: colors.primary }]}
          />
          <View
            style={[styles.progressDot, { backgroundColor: colors.primary }]}
          />
          <View
            style={[
              styles.progressDot,
              styles.progressDotActive,
              { backgroundColor: colors.primary },
            ]}
          />
          <View
            style={[styles.progressDot, { backgroundColor: colors.border }]}
          />
        </View>
      </View>
    </ThemedView>
  );
}

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    header: {
      paddingTop: 80,
      paddingHorizontal: 24,
      paddingBottom: 8,
    },
    backButton: {
      width: 40,
      height: 40,
      borderRadius: 8,
      alignItems: 'center',
      justifyContent: 'center',
      elevation: 5,
      marginBottom: 20,
    },
    content: {
      flex: 1,
      paddingHorizontal: 24,
      paddingBottom: 20,
    },
    title: {
      textAlign: 'left',
      marginBottom: 12,
    },
    subtitle: {
      fontSize: 16,
      textAlign: 'left',
      opacity: 0.7,
      marginBottom: 48,
    },
    section: {
      marginBottom: 32,
    },
    sectionTitle: {
      marginBottom: 20,
    },
    sectionSubtitle: {
      fontSize: 14,
      opacity: 0.7,
      marginBottom: 16,
    },
    optionsGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 12,
    },
    compactOption: {
      borderWidth: 1,
      borderRadius: 20,
      paddingHorizontal: 16,
      paddingVertical: 8,
      marginBottom: 8,
    },
    compactOptionText: {
      fontSize: 14,
      fontWeight: '500',
    },
    seasonalOption: {
      borderWidth: 1,
      borderRadius: 20,
      paddingHorizontal: 12,
      paddingVertical: 10,
      marginBottom: 8,
      flexDirection: 'row',
      alignItems: 'center',
      minWidth: 140,
    },
    seasonalEmoji: {
      fontSize: 16,
      marginRight: 6,
      opacity: 0.7,
    },
    seasonalText: {
      fontSize: 14,
      fontWeight: '500',
      flex: 1,
    },
    equipmentGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 12,
    },
    equipmentOption: {
      borderWidth: 1,
      borderRadius: 20,
      paddingHorizontal: 16,
      paddingVertical: 8,
      marginBottom: 8,
    },
    equipmentText: {
      fontSize: 14,
      fontWeight: '500',
    },
    footer: {
      paddingHorizontal: 24,
      paddingBottom: 48,
    },
    continueButton: {
      borderRadius: 8,
      paddingVertical: 14,
      alignItems: 'center',
      marginBottom: 24,
    },
    continueButtonText: {
      fontSize: 16,
      fontWeight: '600',
    },
    progress: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
    },
    progressDot: {
      width: 8,
      height: 8,
      borderRadius: 4,
      marginHorizontal: 4,
    },
    progressDotActive: {
      width: 24,
    },
  });
