import { Stack } from 'expo-router';
import { OnboardingProvider } from '@/contexts/OnboardingContext';

export default function OnboardingLayout() {
  return (
    <OnboardingProvider>
      <Stack
        screenOptions={{
          headerShown: false,
          animation: 'slide_from_right',
          contentStyle: { backgroundColor: '#212121' },
        }}
      >
        <Stack.Screen name="welcome" />
        <Stack.Screen name="dietary-preferences" />
        <Stack.Screen name="household" />
        <Stack.Screen name="final-setup" />
      </Stack>
    </OnboardingProvider>
  );
}
