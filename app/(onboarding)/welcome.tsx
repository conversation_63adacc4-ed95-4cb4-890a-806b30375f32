import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useTheme } from '@/contexts/ThemeContext';
import { router } from 'expo-router';
import React from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';

export default function WelcomeScreen() {
  const { activeTheme } = useTheme();
  const colors = Colors[activeTheme];
  const styles = createStyles(colors);

  return (
    <ThemedView style={styles.container}>
      <View style={styles.content}>
        <View style={styles.iconContainer}>
          <IconSymbol name="chef-hat" size={80} color={colors.primary} />
        </View>

        <ThemedText
          type="title"
          style={[styles.title, { color: colors.primary }]}
        >
          Welcome to Menu Maker
        </ThemedText>

        <View style={styles.features}>
          <View style={styles.feature}>
            <IconSymbol
              name="calendar-check"
              size={24}
              color={colors.primary}
            />
            <ThemedText style={styles.featureText}>
              7 meals automatically planned for you every week
            </ThemedText>
          </View>

          <View style={styles.feature}>
            <IconSymbol name="cart-outline" size={24} color={colors.primary} />
            <ThemedText style={styles.featureText}>
              Shopping lists generated instantly
            </ThemedText>
          </View>

          <View style={styles.feature}>
            <IconSymbol name="shopping" size={24} color={colors.primary} />
            <ThemedText style={styles.featureText}>
              One-click Walmart checkout
            </ThemedText>
          </View>

          <View style={styles.feature}>
            <IconSymbol name="chef-hat" size={24} color={colors.primary} />
            <ThemedText style={styles.featureText}>
              Your personal assistant to help you craft meals
            </ThemedText>
          </View>
        </View>

        <ThemedText style={styles.subtitle}>
          Let&apos;s personalize your meal planning experience
        </ThemedText>
      </View>

      <View style={styles.footer}>
        <TouchableOpacity
          style={[styles.continueButton, { backgroundColor: colors.primary }]}
          onPress={() => router.push('/(onboarding)/dietary-preferences')}
        >
          <Text
            style={[styles.continueButtonText, { color: colors.background }]}
          >
            Get Started
          </Text>
        </TouchableOpacity>

        <View style={styles.progress}>
          <View
            style={[
              styles.progressDot,
              styles.progressDotActive,
              { backgroundColor: colors.primary },
            ]}
          />
          <View
            style={[styles.progressDot, { backgroundColor: colors.border }]}
          />
          <View
            style={[styles.progressDot, { backgroundColor: colors.border }]}
          />
          <View
            style={[styles.progressDot, { backgroundColor: colors.border }]}
          />
        </View>
      </View>
    </ThemedView>
  );
}

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    content: {
      flex: 1,
      paddingHorizontal: 24,
      paddingTop: 80,
      alignItems: 'center',
    },
    iconContainer: {
      marginBottom: 32,
    },
    title: {
      textAlign: 'center',
      marginBottom: 48,
    },
    features: {
      width: '100%',
      marginBottom: 48,
    },
    feature: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 24,
    },
    featureText: {
      flex: 1,
      marginLeft: 16,
      fontSize: 16,
      lineHeight: 24,
    },
    subtitle: {
      fontSize: 16,
      textAlign: 'center',
      opacity: 0.7,
    },
    footer: {
      paddingHorizontal: 24,
      paddingBottom: 48,
    },
    continueButton: {
      borderRadius: 8,
      paddingVertical: 14,
      alignItems: 'center',
      marginBottom: 24,
    },
    continueButtonText: {
      fontSize: 16,
      fontWeight: '600',
    },
    progress: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
    },
    progressDot: {
      width: 8,
      height: 8,
      borderRadius: 4,
      marginHorizontal: 4,
    },
    progressDotActive: {
      width: 24,
    },
  });
