import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { CustomAlert } from '@/components/CustomAlert';
import { Colors } from '@/constants/Colors';
import { useTheme } from '@/contexts/ThemeContext';
import { useOnboarding } from '@/contexts/OnboardingContext';
import {
  mapOnboardingToPreferences,
  saveUserPreferences,
} from '@/lib/userPreferences';
import { router } from 'expo-router';
import React, { useState } from 'react';
import {
  ActivityIndicator,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';

export default function FinalSetupScreen() {
  const { activeTheme } = useTheme();
  const colors = Colors[activeTheme];
  const styles = createStyles(colors);
  const { state, clearState } = useOnboarding();
  const [loading, setLoading] = useState(false);
  const [showAlert, setShowAlert] = useState(false);
  const [alertConfig, setAlertConfig] = useState({
    title: '',
    message: '',
    onConfirm: () => {},
  });

  const handleFinish = async () => {
    setLoading(true);

    try {
      const preferencesData = mapOnboardingToPreferences({
        dietary: state.dietary || [],
        allergies: state.allergies || [],
        cuisinePreferences: state.cuisinePreferences || [],
        householdSize: state.householdSize || 2, // Default to 2 people
        cookingTime: state.cookingTime || 'No preference',
        skillLevel: state.skillLevel || 'intermediate', // Default to intermediate
        kitchenEquipment: state.kitchenEquipment || [],
        seasonalPreference: state.seasonalPreference || 'automatic',
      });

      const result = await saveUserPreferences(preferencesData);

      if (result) {
        // Clear onboarding state
        clearState();
        // Navigate to main app
        router.replace('/(tabs)/meal-plan');
      } else {
        throw new Error('Failed to save preferences');
      }
    } catch (error) {
      console.error('Error saving preferences:', error);
      setAlertConfig({
        title: 'Error',
        message:
          'There was a problem saving your preferences. Please try again.',
        onConfirm: () => setShowAlert(false),
      });
      setShowAlert(true);
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    router.back();
  };

  return (
    <ThemedView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={[styles.backButton, { backgroundColor: colors.primary }]}
          onPress={handleBack}
        >
          <IconSymbol name="chevron-left" size={20} color={colors.background} />
        </TouchableOpacity>
      </View>

      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContainer}
      >
        <View style={styles.headerContent}>
          <ThemedText
            type="title"
            style={[styles.title, { color: colors.primary }]}
          >
            You&apos;re All Set!
          </ThemedText>

          <ThemedText style={styles.subtitle}>
            Your preferences have been saved and we&apos;re ready to create your personalized meal plans
          </ThemedText>
        </View>

        <View style={styles.benefits}>
          <ThemedText type="subtitle" style={styles.benefitsTitle}>
            You&apos;re all set to enjoy:
          </ThemedText>

          <View style={styles.benefit}>
            <IconSymbol name="star" size={20} color={colors.primary} />
            <ThemedText style={styles.benefitText}>
              Personalized meal plans every Friday
            </ThemedText>
          </View>

          <View style={styles.benefit}>
            <IconSymbol name="cart-outline" size={20} color={colors.primary} />
            <ThemedText style={styles.benefitText}>
              Smart shopping lists
            </ThemedText>
          </View>

          <View style={styles.benefit}>
            <IconSymbol name="heart" size={20} color={colors.primary} />
            <ThemedText style={styles.benefitText}>
              Meals tailored to your preferences
            </ThemedText>
          </View>
        </View>
      </ScrollView>

      <View style={styles.footer}>
        <TouchableOpacity
          style={[styles.finishButton, { backgroundColor: colors.primary }]}
          onPress={handleFinish}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator color={colors.background} />
          ) : (
            <Text
              style={[styles.finishButtonText, { color: colors.background }]}
            >
              Start Planning Meals
            </Text>
          )}
        </TouchableOpacity>

        <View style={styles.progress}>
          <View
            style={[styles.progressDot, { backgroundColor: colors.primary }]}
          />
          <View
            style={[styles.progressDot, { backgroundColor: colors.primary }]}
          />
          <View
            style={[styles.progressDot, { backgroundColor: colors.primary }]}
          />
          <View
            style={[
              styles.progressDot,
              styles.progressDotActive,
              { backgroundColor: colors.primary },
            ]}
          />
        </View>
      </View>

      <CustomAlert
        visible={showAlert}
        title={alertConfig.title}
        message={alertConfig.message}
        onConfirm={alertConfig.onConfirm}
        onCancel={() => setShowAlert(false)}
      />
    </ThemedView>
  );
}

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    header: {
      paddingTop: 80,
      paddingHorizontal: 24,
      paddingBottom: 8,
    },
    backButton: {
      width: 40,
      height: 40,
      borderRadius: 8,
      alignItems: 'center',
      justifyContent: 'center',
      elevation: 5,
      marginBottom: 20,
    },
    content: {
      flex: 1,
    },
    scrollContainer: {
      flexGrow: 1,
      paddingHorizontal: 24,
      paddingBottom: 20,
    },
    headerContent: {
      marginBottom: 48,
    },
    title: {
      textAlign: 'left',
      marginBottom: 12,
    },
    subtitle: {
      fontSize: 16,
      textAlign: 'left',
      opacity: 0.7,
    },
    section: {
      width: '100%',
      marginBottom: 48,
    },
    sectionTitle: {
      marginBottom: 20,
      textAlign: 'left',
    },
    optionsGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 12,
    },
    compactOption: {
      borderWidth: 1,
      borderRadius: 20,
      paddingHorizontal: 16,
      paddingVertical: 8,
      marginBottom: 8,
    },
    compactOptionText: {
      fontSize: 14,
      fontWeight: '500',
    },
    benefits: {
      width: '100%',
    },
    benefitsTitle: {
      textAlign: 'left',
      marginBottom: 24,
    },
    benefit: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 16,
    },
    benefitText: {
      flex: 1,
      marginLeft: 12,
      fontSize: 16,
    },
    footer: {
      paddingHorizontal: 24,
      paddingBottom: 48,
    },
    finishButton: {
      borderRadius: 8,
      paddingVertical: 14,
      alignItems: 'center',
      marginBottom: 24,
    },
    finishButtonText: {
      fontSize: 16,
      fontWeight: '600',
    },
    progress: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
    },
    progressDot: {
      width: 8,
      height: 8,
      borderRadius: 4,
      marginHorizontal: 4,
    },
    progressDotActive: {
      width: 24,
    },
  });
