import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Text,
  ScrollView,
} from 'react-native';
import { router } from 'expo-router';
import { ThemedView } from '@/components/ThemedView';
import { ThemedText } from '@/components/ThemedText';
import { Colors } from '@/constants/Colors';
import { useTheme } from '@/contexts/ThemeContext';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useOnboarding } from '@/contexts/OnboardingContext';
import { DIETARY_RESTRICTIONS_OPTIONS, ALLERGEN_FREE_OPTIONS, CUISINE_OPTIONS } from '@/lib/userPreferences';

// Convert database format to user-friendly labels for dietary restrictions
const formatDietaryOption = (option: string) => {
  const mapping: { [key: string]: string } = {
    'VEGETARIAN': 'Vegetarian',
    'VEGAN': 'Vegan', 
    'GLUTEN_FREE': 'Gluten-Free',
    'DAIRY_FREE': 'Dairy-Free',
    'KETO_FRIENDLY': 'Keto',
    'PALEO': 'Paleo',
    'KOSHER': 'Kosher',
    'PESCATARIAN': 'Pescatarian',
    'MEDITERRANEAN': 'Mediterranean',
    'SUGAR_CONSCIOUS': 'Sugar-Conscious',
    'NO_SUGAR_ADDED': 'No Sugar Added',
    'DASH': 'DASH Diet',
    'FODMAP_FREE': 'Low FODMAP'
  };
  return mapping[option] || option;
};

// Convert allergen-free database tags to user-friendly allergy labels
const formatAllergenOption = (option: string) => {
  const mapping: { [key: string]: string } = {
    'PEANUT_FREE': 'Peanuts',
    'TREE_NUT_FREE': 'Tree Nuts',
    'SHELLFISH_FREE': 'Shellfish',
    'FISH_FREE': 'Fish',
    'EGG_FREE': 'Eggs',
    'DAIRY_FREE': 'Dairy',
    'SOY_FREE': 'Soy',
    'WHEAT_FREE': 'Wheat',
    'SESAME_FREE': 'Sesame',
    'CELERY_FREE': 'Celery',
    'MUSTARD_FREE': 'Mustard',
    'LUPINE_FREE': 'Lupine',
    'MOLLUSK_FREE': 'Mollusks',
    'CRUSTACEAN_FREE': 'Crustaceans'
  };
  return mapping[option] || option;
};

// Convert cuisine database values to user-friendly labels
const formatCuisineOption = (option: string) => {
  const mapping: { [key: string]: string } = {
    'american': 'American',
    'italian': 'Italian',
    'mexican': 'Mexican',
    'mediterranean': 'Mediterranean',
    'french': 'French',
    'chinese': 'Chinese',
    'south american': 'South American',
    'asian': 'Asian',
    'japanese': 'Japanese',
    'british': 'British',
    'south east asian': 'South East Asian',
    'nordic': 'Nordic',
    'middle eastern': 'Middle Eastern',
    'indian': 'Indian',
    'central europe': 'Central European',
    'caribbean': 'Caribbean',
    'eastern europe': 'Eastern European',
    'greek': 'Greek',
    'korean': 'Korean'
  };
  return mapping[option] || option;
};

const DIETARY_OPTIONS = DIETARY_RESTRICTIONS_OPTIONS.map(formatDietaryOption);
const ALLERGY_OPTIONS = [...ALLERGEN_FREE_OPTIONS.map(formatAllergenOption), 'None'];
const CUISINE_OPTIONS_UI = CUISINE_OPTIONS.map(formatCuisineOption);

export default function DietaryPreferencesScreen() {
  const { activeTheme } = useTheme();
  const colors = Colors[activeTheme];
  const styles = createStyles(colors);
  const { state, updateDietary, updateAllergies, updateCuisinePreferences } =
    useOnboarding();
  const [selectedDietary, setSelectedDietary] = useState<string[]>(
    state.dietary
  );
  const [selectedAllergies, setSelectedAllergies] = useState<string[]>(
    state.allergies
  );
  const [selectedCuisines, setSelectedCuisines] = useState<string[]>(
    state.cuisinePreferences
  );

  useEffect(() => {
    setSelectedDietary(state.dietary);
    setSelectedAllergies(state.allergies);
    setSelectedCuisines(state.cuisinePreferences);
  }, [state.dietary, state.allergies, state.cuisinePreferences]);

  // Helper functions to map between UI labels and database values
  const mapDietaryToDatabase = (uiValue: string) => {
    const reverseMapping = Object.fromEntries(
      Object.entries({
        'VEGETARIAN': 'Vegetarian',
        'VEGAN': 'Vegan', 
        'GLUTEN_FREE': 'Gluten-Free',
        'DAIRY_FREE': 'Dairy-Free',
        'KETO_FRIENDLY': 'Keto',
        'PALEO': 'Paleo',
        'KOSHER': 'Kosher',
        'PESCATARIAN': 'Pescatarian',
        'MEDITERRANEAN': 'Mediterranean',
        'SUGAR_CONSCIOUS': 'Sugar-Conscious',
        'NO_SUGAR_ADDED': 'No Sugar Added',
        'DASH': 'DASH Diet',
        'FODMAP_FREE': 'Low FODMAP'
      }).map(([key, value]) => [value, key])
    );
    return reverseMapping[uiValue] || uiValue;
  };

  const mapAllergenToDatabase = (uiValue: string) => {
    if (uiValue === 'None') return 'None';
    const reverseMapping = Object.fromEntries(
      Object.entries({
        'PEANUT_FREE': 'Peanuts',
        'TREE_NUT_FREE': 'Tree Nuts',
        'SHELLFISH_FREE': 'Shellfish',
        'FISH_FREE': 'Fish',
        'EGG_FREE': 'Eggs',
        'DAIRY_FREE': 'Dairy',
        'SOY_FREE': 'Soy',
        'WHEAT_FREE': 'Wheat',
        'SESAME_FREE': 'Sesame',
        'CELERY_FREE': 'Celery',
        'MUSTARD_FREE': 'Mustard',
        'LUPINE_FREE': 'Lupine',
        'MOLLUSK_FREE': 'Mollusks',
        'CRUSTACEAN_FREE': 'Crustaceans'
      }).map(([key, value]) => [value, key])
    );
    return reverseMapping[uiValue] || uiValue;
  };

  const mapCuisineToDatabase = (uiValue: string) => {
    const reverseMapping = Object.fromEntries(
      Object.entries({
        'american': 'American',
        'italian': 'Italian',
        'mexican': 'Mexican',
        'mediterranean': 'Mediterranean',
        'french': 'French',
        'chinese': 'Chinese',
        'south american': 'South American',
        'asian': 'Asian',
        'japanese': 'Japanese',
        'british': 'British',
        'south east asian': 'South East Asian',
        'nordic': 'Nordic',
        'middle eastern': 'Middle Eastern',
        'indian': 'Indian',
        'central europe': 'Central European',
        'caribbean': 'Caribbean',
        'eastern europe': 'Eastern European',
        'greek': 'Greek',
        'korean': 'Korean'
      }).map(([key, value]) => [value, key])
    );
    return reverseMapping[uiValue] || uiValue;
  };

  const toggleSelection = (
    item: string,
    list: string[],
    setter: (list: string[]) => void
  ) => {
    if (list.includes(item)) {
      setter(list.filter((i) => i !== item));
    } else {
      setter([...list, item]);
    }
  };

  const handleContinue = () => {
    // Convert UI labels to database format before saving
    const dbDietary = selectedDietary.map(mapDietaryToDatabase);
    const dbAllergies = selectedAllergies.includes('None') ? [] : selectedAllergies.map(mapAllergenToDatabase);
    const dbCuisines = selectedCuisines.map(mapCuisineToDatabase);
    
    updateDietary(dbDietary);
    updateAllergies(dbAllergies);
    updateCuisinePreferences(dbCuisines);
    router.push('/(onboarding)/household');
  };

  const handleBack = () => {
    router.back();
  };

  return (
    <ThemedView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={[styles.backButton, { backgroundColor: colors.primary }]}
          onPress={handleBack}
        >
          <IconSymbol name="chevron-left" size={20} color={colors.background} />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <ThemedText
          type="title"
          style={[styles.title, { color: colors.primary }]}
        >
          Dietary Preferences
        </ThemedText>

        <ThemedText style={styles.subtitle}>
          Help us personalize your meal plans
        </ThemedText>

        <View style={styles.section}>
          <ThemedText type="subtitle" style={styles.sectionTitle}>
            Dietary Restrictions
          </ThemedText>

          <View style={styles.optionsGrid}>
            {DIETARY_OPTIONS.map((option) => (
              <TouchableOpacity
                key={option}
                style={[
                  styles.option,
                  { borderColor: colors.border },
                  selectedDietary.includes(option) && {
                    backgroundColor: colors.primary,
                    borderColor: colors.primary,
                  },
                ]}
                onPress={() =>
                  toggleSelection(option, selectedDietary, setSelectedDietary)
                }
              >
                <ThemedText
                  style={[
                    styles.optionText,
                    selectedDietary.includes(option) && {
                      color: colors.background,
                    },
                  ]}
                >
                  {option}
                </ThemedText>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <View style={styles.section}>
          <ThemedText type="subtitle" style={styles.sectionTitle}>
            Allergies
          </ThemedText>

          <View style={styles.optionsGrid}>
            {ALLERGY_OPTIONS.map((option) => (
              <TouchableOpacity
                key={option}
                style={[
                  styles.option,
                  { borderColor: colors.border },
                  selectedAllergies.includes(option) && {
                    backgroundColor: colors.primary,
                    borderColor: colors.primary,
                  },
                ]}
                onPress={() =>
                  toggleSelection(
                    option,
                    selectedAllergies,
                    setSelectedAllergies
                  )
                }
              >
                <ThemedText
                  style={[
                    styles.optionText,
                    selectedAllergies.includes(option) && {
                      color: colors.background,
                    },
                  ]}
                >
                  {option}
                </ThemedText>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <View style={styles.section}>
          <ThemedText type="subtitle" style={styles.sectionTitle}>
            Cuisine Preferences
          </ThemedText>
          <ThemedText style={styles.sectionSubtitle}>
            Select your favorite cuisines (optional)
          </ThemedText>

          <View style={styles.optionsGrid}>
            {CUISINE_OPTIONS_UI.map((option) => (
              <TouchableOpacity
                key={option}
                style={[
                  styles.option,
                  { borderColor: colors.border },
                  selectedCuisines.includes(option) && {
                    backgroundColor: colors.primary,
                    borderColor: colors.primary,
                  },
                ]}
                onPress={() =>
                  toggleSelection(option, selectedCuisines, setSelectedCuisines)
                }
              >
                <ThemedText
                  style={[
                    styles.optionText,
                    selectedCuisines.includes(option) && {
                      color: colors.background,
                    },
                  ]}
                >
                  {option}
                </ThemedText>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </ScrollView>

      <View style={styles.footer}>
        <TouchableOpacity
          style={[styles.continueButton, { backgroundColor: colors.primary }]}
          onPress={handleContinue}
        >
          <Text
            style={[styles.continueButtonText, { color: colors.background }]}
          >
            Continue
          </Text>
        </TouchableOpacity>

        <View style={styles.progress}>
          <View
            style={[styles.progressDot, { backgroundColor: colors.primary }]}
          />
          <View
            style={[
              styles.progressDot,
              styles.progressDotActive,
              { backgroundColor: colors.primary },
            ]}
          />
          <View
            style={[styles.progressDot, { backgroundColor: colors.border }]}
          />
          <View
            style={[styles.progressDot, { backgroundColor: colors.border }]}
          />
        </View>
      </View>
    </ThemedView>
  );
}

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    header: {
      paddingTop: 80,
      paddingHorizontal: 24,
      paddingBottom: 8,
    },
    backButton: {
      width: 40,
      height: 40,
      borderRadius: 8,
      alignItems: 'center',
      justifyContent: 'center',
      elevation: 5,
      marginBottom: 20,
    },
    content: {
      flex: 1,
      paddingHorizontal: 24,
    },
    title: {
      textAlign: 'left',
      marginBottom: 12,
    },
    subtitle: {
      fontSize: 16,
      textAlign: 'left',
      opacity: 0.7,
      marginBottom: 48,
    },
    section: {
      marginBottom: 48,
    },
    sectionTitle: {
      marginBottom: 20,
    },
    sectionSubtitle: {
      fontSize: 14,
      opacity: 0.7,
      marginBottom: 16,
    },
    optionsGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 12,
    },
    option: {
      borderWidth: 1,
      borderRadius: 20,
      paddingHorizontal: 16,
      paddingVertical: 8,
      marginBottom: 8,
    },
    optionText: {
      fontSize: 14,
      fontWeight: '500',
    },
    footer: {
      paddingHorizontal: 24,
      paddingBottom: 48,
    },
    continueButton: {
      borderRadius: 8,
      paddingVertical: 14,
      alignItems: 'center',
      marginBottom: 24,
    },
    continueButtonText: {
      fontSize: 16,
      fontWeight: '600',
    },
    progress: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
    },
    progressDot: {
      width: 8,
      height: 8,
      borderRadius: 4,
      marginHorizontal: 4,
    },
    progressDotActive: {
      width: 24,
    },
  });
