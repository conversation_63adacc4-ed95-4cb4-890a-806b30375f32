# MenuMaker Mobile Testing Guide

## Overview

This guide provides comprehensive testing strategies and implementation details for the MenuMaker mobile application. The testing infrastructure has been set up using Jest and React Testing Library, with patterns defined for integration and E2E testing.

## Testing Architecture

### Framework Stack
- **Unit Testing**: Jest + React Testing Library
- **Component Testing**: React Testing Library with custom render utilities  
- **Integration Testing**: Jest with Supabase mocks
- **E2E Testing**: Framework ready (Detox blocked by React 19 compatibility)
- **Performance Testing**: Custom utilities for meal plan generation

## Quick Start

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Generate coverage report
npm run test:coverage

# Run tests for CI
npm run test:ci
```

## Test Structure

### Utility Tests
Located in `__tests__/lib/`, these test core business logic:

```typescript
// Example: weekCalculation.test.ts
import { getWeekStartDate, isNewWeek } from '../../lib/weekCalculation';

describe('weekCalculation utilities', () => {
  it('should return correct week start date for Friday cycle', () => {
    mockDate('2024-01-05T10:00:00.000Z');
    const result = getWeekStartDate();
    expect(result).toBe('2023-12-31');
  });
});
```

### Component Tests
Located in `__tests__/components/`, these test UI components:

```typescript
// Example: ThemedText.test.tsx
import { render, screen } from '@testing-library/react-native';
import { ThemedText } from '../../components/ThemedText';

describe('ThemedText Component', () => {
  it('should render text content correctly', () => {
    render(<ThemedText>Hello World</ThemedText>);
    expect(screen.getByText('Hello World')).toBeTruthy();
  });
});
```

## Critical Test Cases

### High Priority
1. **Authentication Flow** (`app/(auth)/auth.tsx`)
   - Login/logout/signup validation
   - Error handling and user feedback
   - Session persistence and restoration

2. **Meal Plan Generation** (`app/(tabs)/meal-plan.tsx`)
   - Week calculation logic (`lib/weekCalculation.ts`)
   - Meal filtering and preferences (`lib/mealFiltering.ts`)
   - AI meal integration and storage

3. **Shopping List** (`app/(tabs)/shopping-list.tsx`)
   - Ingredient consolidation (`lib/simpleIngredientConsolidation.ts`)
   - Scaling calculations (`lib/ingredientScaling.ts`)
   - Instacart integration (`lib/instacartService.ts`)

4. **AI Recipe Generation** (`app/(tabs)/ai-chat.tsx`)
   - OpenAI integration (`lib/openaiProxy.ts`)
   - Recipe parsing and validation
   - Database storage patterns

### Medium Priority
1. **Theme System** (`components/ThemedText.tsx`, `components/ThemedView.tsx`)
2. **Navigation** (Expo Router file-based routing)
3. **User Preferences** (`lib/userPreferences.ts`)
4. **Form Validation** (`lib/inputValidation.ts`)

## Mock Strategy

### Supabase Mocking
```typescript
jest.mock('@/lib/supabase', () => ({
  supabase: {
    auth: {
      getSession: jest.fn(),
      signUp: jest.fn(),
      signInWithPassword: jest.fn(),
      signOut: jest.fn(),
    },
    from: jest.fn(() => ({
      select: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
      delete: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      single: jest.fn(),
    })),
  },
}));
```

### React Native Mocking
```typescript
jest.mock('react-native-reanimated', () => {
  const Reanimated = require('react-native-reanimated/mock');
  Reanimated.default.call = () => {};
  return Reanimated;
});
```

## Integration Testing Patterns

### Database Operations
```typescript
describe('User Preferences Integration', () => {
  beforeEach(() => {
    // Setup Supabase mocks
    const mockSupabase = require('@/lib/supabase').supabase;
    mockSupabase.from.mockImplementation(() => ({
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      single: jest.fn().mockResolvedValue({ data: mockUserPrefs, error: null }),
    }));
  });

  it('should fetch user preferences correctly', async () => {
    const result = await getUserPreferences('user-123');
    expect(result).toEqual(mockUserPrefs);
  });
});
```

### API Integration
```typescript
describe('OpenAI Integration', () => {
  it('should generate recipe with proper error handling', async () => {
    const mockResponse = { choices: [{ message: { content: 'Recipe data' } }] };
    global.fetch = jest.fn().mockResolvedValue({
      ok: true,
      json: () => Promise.resolve(mockResponse),
    });

    const result = await callOpenAIProxy([], 'Generate a recipe');
    expect(result).toBe('Recipe data');
  });
});
```

## Performance Testing

### Meal Plan Generation
```typescript
describe('Meal Plan Performance', () => {
  it('should generate meal plan within performance threshold', async () => {
    const startTime = performance.now();
    
    await loadWeeklyMeals();
    
    const endTime = performance.now();
    const executionTime = endTime - startTime;
    
    expect(executionTime).toBeLessThan(2000); // 2 second threshold
  });
});
```

## Design Consistency Testing

### Component Style Validation
```typescript
describe('Design Consistency', () => {
  it('should apply consistent button styles', () => {
    const { getByTestId } = render(<CustomButton testID="test-btn" />);
    const button = getByTestId('test-btn');
    
    expect(button.props.style).toMatchObject({
      backgroundColor: '#FFE598',
      borderRadius: expect.any(Number),
    });
  });
});
```

## Accessibility Testing

### Screen Reader Support
```typescript
describe('Accessibility', () => {
  it('should provide proper accessibility labels', () => {
    render(<ThemedText accessibilityLabel="Main title">Welcome</ThemedText>);
    
    expect(screen.getByLabelText('Main title')).toBeTruthy();
  });
});
```

## Error Handling Testing

### Network Failures
```typescript
describe('Error Handling', () => {
  it('should handle Supabase connection errors gracefully', async () => {
    const mockSupabase = require('@/lib/supabase').supabase;
    mockSupabase.from.mockImplementation(() => ({
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      single: jest.fn().mockResolvedValue({ 
        data: null, 
        error: { message: 'Connection failed' } 
      }),
    }));

    const result = await getUserPreferences('user-123');
    expect(result).toBeNull();
  });
});
```

## Coverage Goals

### Current Targets
- **Unit Tests**: 60% minimum coverage
- **Critical Paths**: 80% coverage for core business logic
- **Components**: 70% coverage for UI components
- **Integration**: 50% coverage for API interactions

### Coverage Commands
```bash
# Generate detailed coverage report
npm run test:coverage

# View coverage in browser
open coverage/lcov-report/index.html
```

## CI/CD Integration

### GitHub Actions Example
```yaml
name: Test Suite
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Setup Node
        uses: actions/setup-node@v3
        with:
          node-version: 18
      - name: Install dependencies
        run: npm install
      - name: Run tests with coverage
        run: npm run test:ci
      - name: Upload coverage
        uses: codecov/codecov-action@v3
```

## Known Issues & Workarounds

### React 19 Compatibility
- **Issue**: E2E testing libraries not compatible with React 19
- **Workaround**: Consider React 18 downgrade or alternative E2E solutions
- **Status**: Monitoring library updates for compatibility

### Mock Complexity
- **Issue**: Some React Native modules difficult to mock
- **Solution**: Use `transformIgnorePatterns` in Jest config
- **Example**: See `jest.config.js` for current patterns

## Test Data Management

### Sample Data Files
Create `__tests__/fixtures/` directory with:
- `userPreferences.json`: Mock user preference data
- `meals.json`: Sample meal data for testing
- `recipes.json`: Sample AI-generated recipes

### Dynamic Data Generation
```typescript
// Test utility for generating mock data
export const createMockMeal = (overrides = {}) => ({
  id: 'meal-123',
  name: 'Test Meal',
  prepTimeMin: 30,
  cookTimeMin: 45,
  servingSize: 4,
  ingredients: ['2 cups flour', '1 cup sugar'],
  ...overrides,
});
```

## Future Enhancements

### E2E Testing
Once React compatibility is resolved:
1. Install Detox or Maestro
2. Create user journey tests
3. Automate critical path validation
4. Cross-platform testing (iOS/Android)

### Visual Regression Testing
1. Implement screenshot comparison
2. Validate design consistency automatically
3. Catch unintended UI changes

### Load Testing
1. Test with large meal databases
2. Validate performance with many users
3. Monitor memory usage patterns

## Contributing

### Adding New Tests
1. Follow existing patterns in `__tests__/`
2. Use descriptive test names
3. Include both happy path and error cases
4. Update this guide with new patterns

### Mock Guidelines
1. Mock external dependencies
2. Keep mocks simple and focused
3. Document complex mock setups
4. Reuse mocks across similar tests

### Best Practices
1. Test behavior, not implementation
2. Use meaningful assertions
3. Keep tests independent
4. Clean up after tests (if needed)

This testing infrastructure provides a solid foundation for maintaining code quality and catching regressions in the MenuMaker mobile application.