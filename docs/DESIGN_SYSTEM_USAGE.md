# Design System Usage Guide

This guide demonstrates how to use the standardized UI components created to ensure design consistency across the MenuMaker app.

## Button Component

### Import
```typescript
import { Button } from '@/components/ui/Button';
```

### Usage Examples

```typescript
// Primary button (yellow background)
<Button
  title="Add to Cart"
  onPress={handleAddToCart}
  variant="primary"
  size="medium"
/>

// Secondary button
<Button
  title="Cancel"
  onPress={handleCancel}
  variant="secondary"
/>

// Outline button
<Button
  title="View Details"
  onPress={handleViewDetails}
  variant="outline"
/>

// Loading state
<Button
  title="Saving..."
  onPress={handleSave}
  loading={true}
  disabled={true}
/>

// Full width button
<Button
  title="Continue"
  onPress={handleContinue}
  fullWidth
/>
```

### Replacing Old Patterns

**Before:**
```typescript
<TouchableOpacity
  style={[
    styles.button,
    { backgroundColor: colors.primary }
  ]}
  onPress={handlePress}
>
  <Text style={styles.buttonText}>Click Me</Text>
</TouchableOpacity>
```

**After:**
```typescript
<Button
  title="Click Me"
  onPress={handlePress}
  variant="primary"
/>
```

## LoadingIndicator Component

### Import
```typescript
import { LoadingIndicator } from '@/components/ui/LoadingIndicator';
```

### Usage Examples

```typescript
// Simple loading indicator
<LoadingIndicator />

// With loading text
<LoadingIndicator text="Loading meals..." />

// Full screen loading
<LoadingIndicator
  fullScreen
  text="Generating your meal plan..."
/>

// Small inline indicator
<LoadingIndicator size="small" />

// Custom color
<LoadingIndicator color="#FFE598" />
```

### Replacing Old Patterns

**Before:**
```typescript
<View style={styles.loadingContainer}>
  <ActivityIndicator size="large" color={colors.primary} />
  <Text>Loading...</Text>
</View>
```

**After:**
```typescript
<LoadingIndicator text="Loading..." />
```

## Modal Component

### Import
```typescript
import { Modal } from '@/components/ui/Modal';
```

### Usage Examples

```typescript
// Basic modal
<Modal
  visible={isModalVisible}
  onClose={() => setIsModalVisible(false)}
  title="Edit Preferences"
>
  <View>
    {/* Modal content */}
  </View>
</Modal>

// Modal with footer
<Modal
  visible={showConfirm}
  onClose={handleClose}
  title="Confirm Action"
  footer={
    <View style={styles.modalFooter}>
      <Button
        title="Cancel"
        onPress={handleClose}
        variant="secondary"
        style={{ flex: 1, marginRight: 8 }}
      />
      <Button
        title="Confirm"
        onPress={handleConfirm}
        variant="primary"
        style={{ flex: 1 }}
      />
    </View>
  }
>
  <ThemedText>Are you sure you want to proceed?</ThemedText>
</Modal>

// Scrollable modal
<Modal
  visible={showDetails}
  onClose={handleClose}
  title="Recipe Details"
  scrollable
  size="large"
>
  {/* Long content */}
</Modal>
```

## Typography System

### Import
```typescript
import { ThemedText } from '@/components/ThemedText';
import { Typography } from '@/constants/Typography';
```

### Usage Examples

```typescript
// Headings
<ThemedText type="h1">Main Title</ThemedText>
<ThemedText type="h2">Section Title</ThemedText>
<ThemedText type="h3">Subsection</ThemedText>

// Body text
<ThemedText type="body">Regular paragraph text</ThemedText>
<ThemedText type="bodySmall">Small text for captions</ThemedText>
<ThemedText type="bodyLarge">Emphasized body text</ThemedText>

// Special text
<ThemedText type="caption">Helper text</ThemedText>
<ThemedText type="label">Form Label</ThemedText>
<ThemedText type="link">Clickable Link</ThemedText>
```

### Using Typography Constants

```typescript
// In StyleSheet
const styles = StyleSheet.create({
  customText: {
    ...Typography.styles.h4,
    color: colors.accent,
  },
  buttonText: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.semibold,
  },
});
```

## Error Handling

### Import
```typescript
import { displayError, handleAsync, ErrorMessages } from '@/lib/errorHandling';
```

### Usage Examples

```typescript
// Using handleAsync for clean error handling
const loadMeals = async () => {
  const [data, error] = await handleAsync(
    supabase.from('meals').select('*'),
    'Failed to load meals'
  );
  
  if (error) {
    displayError(error, setShowAlert, setAlertConfig);
    return;
  }
  
  setMeals(data);
};

// Using predefined error messages
if (!session) {
  displayError(
    ErrorMessages.AUTH_REQUIRED,
    setShowAlert,
    setAlertConfig
  );
  return;
}

// Custom error handling
try {
  await saveMealPlan();
} catch (error) {
  displayError(
    error,
    setShowAlert,
    setAlertConfig
  );
}
```

## Migration Guide

### Step 1: Update Imports
Add the new UI component imports to your files:

```typescript
import { Button, LoadingIndicator, Modal } from '@/components/ui';
import { ThemedText } from '@/components/ThemedText';
import { displayError, handleAsync } from '@/lib/errorHandling';
```

### Step 2: Replace TouchableOpacity Buttons
Find all `TouchableOpacity` components used as buttons and replace with `Button`:

```typescript
// Search for patterns like:
<TouchableOpacity style={styles.button} onPress={...}>
  <Text>...</Text>
</TouchableOpacity>

// Replace with:
<Button title="..." onPress={...} variant="primary" />
```

### Step 3: Replace ActivityIndicator
Find all `ActivityIndicator` usage and replace with `LoadingIndicator`:

```typescript
// Search for:
<ActivityIndicator size="large" color={colors.primary} />

// Replace with:
<LoadingIndicator />
```

### Step 4: Standardize Modals
Update custom modals to use the new `Modal` component:

```typescript
// Replace custom modal implementations with:
<Modal
  visible={isVisible}
  onClose={handleClose}
  title="Modal Title"
>
  {/* Content */}
</Modal>
```

### Step 5: Update Typography
Replace hardcoded font sizes and styles with Typography constants:

```typescript
// Before:
fontSize: 16,
fontWeight: '600',

// After:
...Typography.styles.body,
fontWeight: Typography.fontWeight.semibold,
```

### Step 6: Implement Error Handling
Replace try/catch blocks with consistent error handling:

```typescript
// Before:
try {
  const result = await someAsyncOperation();
} catch (error) {
  Alert.alert('Error', error.message);
}

// After:
const [result, error] = await handleAsync(someAsyncOperation());
if (error) {
  displayError(error, setShowAlert, setAlertConfig);
  return;
}
```

## Best Practices

1. **Always use the design system components** instead of creating custom implementations
2. **Maintain consistency** by using the same variant for similar actions
3. **Use semantic typography types** (h1-h6, body, caption, etc.) based on content hierarchy
4. **Handle errors consistently** using the error handling utilities
5. **Test on both light and dark themes** to ensure proper theming support

## Component Checklist

When reviewing or creating new screens, ensure:

- [ ] All buttons use the `Button` component
- [ ] Loading states use `LoadingIndicator`
- [ ] Modals use the standard `Modal` component
- [ ] Text uses `ThemedText` with appropriate type
- [ ] Error handling uses `displayError` utility
- [ ] Font sizes reference `Typography` constants
- [ ] Colors reference theme colors, not hardcoded values

This standardization ensures a consistent user experience throughout the app and makes maintenance much easier.