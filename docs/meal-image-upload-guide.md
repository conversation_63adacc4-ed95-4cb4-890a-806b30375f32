# Meal Image Upload Guide

This guide documents the process for uploading meal images to Supabase storage and linking them to meals in the database.

## Overview

The meal image system stores images in Supabase Storage (`meals` bucket) and links them to meal records via the `image` column in the `meals` table. Image URLs follow the format:

```
https://uxcynasomzhnrzxdhydj.supabase.co/storage/v1/object/public/meals/[normalized-meal-name]
```

## Directory Structure

- **Source Images**: `assets/meals/Batch1/` (or similar batch directories)
- **Storage Bucket**: Supabase `meals` bucket
- **Database**: `meals.image` column stores the full URL

## Scripts Available

### 1. Initial Upload Script (`upload-meal-images.js`)

- Uploads images from a batch directory to Supabase storage
- Matches image filenames to meal names in database
- Updates database with image URLs
- Handles filename normalization (spaces → hyphens, lowercase, special chars removed)

### 2. Batch Processing Script (`upload-meal-images-batch.js`)

- Processes images in smaller batches (100 at a time)
- Better for large volumes to avoid timeouts
- Includes progress tracking

### 3. Resume from Point Script (`upload-remaining-images.js`)

- Continues from a specific image file
- Useful when previous runs were interrupted
- Skips already processed images

### 4. Matching Script (`match-jpg-images-only.js`)

- Matches existing storage files to database meals
- Focuses only on original JPG files (ignores AI-generated files)
- Uses multiple matching strategies for better accuracy

## Process for New Batches

### Step 1: Prepare Images

1. Place new batch images in `assets/meals/BatchX/` directory
2. Ensure filenames exactly match meal names in database
3. Use JPG format for consistency

### Step 2: Upload and Match

1. **For new batch upload**:

   ```bash
   # Update the batchDir path in upload-meal-images-batch.js
   const batchDir = './assets/meals/BatchX';

   # Run the upload
   node upload-meal-images-batch.js
   ```

2. **If upload is interrupted**:

   ```bash
   # Update the startAfter variable in upload-remaining-images.js
   const startAfter = 'Last Successfully Processed Image.jpg';

   # Run remaining
   node upload-remaining-images.js
   ```

3. **To fix missing matches**:
   ```bash
   # Run the matching script
   node match-jpg-images-only.js
   ```

### Step 3: Verify Results

```bash
# Check final counts
node check-upload-results.js
```

## Filename Normalization Rules

The system normalizes filenames for storage using these rules:

- Convert to lowercase
- Remove special characters (keep only a-z, 0-9, spaces, hyphens)
- Replace spaces with hyphens
- Replace multiple hyphens with single hyphen
- Remove leading/trailing hyphens

**Example**: `"Bacon Wrapped Jalapeño Poppers.jpg"` → `"bacon-wrapped-jalapeno-poppers"`

## Matching Strategies

The matching system uses multiple strategies in order:

1. **Exact Name Match**: Direct comparison of normalized names
2. **Clean Text Match**: Remove all special characters and compare
3. **Fuzzy Word Match**: At least 80% of words must match (minimum 3 words)

## Troubleshooting

### Common Issues

1. **Storage file not found**:
   - Check if image was actually uploaded to storage
   - Verify filename normalization is correct

2. **No meal match found**:
   - Check if meal exists in database with exact name
   - Verify spelling and special characters in meal name
   - Consider if meal name has changed in database

3. **Upload errors**:
   - Check Supabase service key permissions
   - Verify storage bucket exists and is accessible
   - Check file size limits

### Environment Variables Required

```env
EXPO_PUBLIC_SUPABASE_URL=https://uxcynasomzhnrzxdhydj.supabase.co
EXPO_SUPABASE_SERVICE_KEY=your_service_role_key
```

## Database Schema

```sql
-- meals table structure (relevant columns)
CREATE TABLE meals (
  id UUID PRIMARY KEY,
  name TEXT NOT NULL,
  image TEXT, -- Stores full URL to image in storage
  -- ... other columns
);
```

## Storage Bucket Configuration

- **Bucket Name**: `meals`
- **Public Access**: Enabled for public viewing
- **File Types**: JPG, JPEG, PNG
- **Naming Convention**: Normalized meal names without extensions

## Success Metrics

After running the complete process for Batch1:

- **Total Images**: 890 original JPG files
- **Successfully Matched**: 889 images (99.9% success rate)
- **Final Database Count**: 889 meals with images
- **Storage Files**: All images uploaded successfully

## Future Batch Checklist

- [ ] Place images in new batch directory
- [ ] Update script paths to point to new directory
- [ ] Run upload script with batch processing
- [ ] Verify storage upload completion
- [ ] Run matching script to catch any missed connections
- [ ] Check final counts match expected numbers
- [ ] Update this documentation if process changes

## Script Maintenance

When adding new batches, you may need to:

1. Update directory paths in scripts
2. Adjust batch sizes if needed
3. Add new matching strategies if naming patterns change
4. Update environment variables if Supabase configuration changes
