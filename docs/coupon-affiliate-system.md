# Coupon-Based Affiliate System

The MenuMaker affiliate system has been updated to work with Stripe coupon codes instead of referral links. This provides a better user experience and more accurate tracking.

## How It Works

### 1. Customer Journey

1. **Affiliate shares coupon code** (e.g., "ROB20") with potential customers
2. **Customer visits checkout page** and enters the coupon code for a discount
3. **Stripe processes payment** with the applied coupon/promotion code
4. **Webhook triggers** when `checkout.session.completed` fires
5. **System attributes referral** to the affiliate based on the coupon code used

### 2. Technical Flow

#### Stripe Webhook Processing

The webhook (`supabase/functions/stripe-webhook/index.ts`) now:

1. **Extracts discount information** from `session.total_details.breakdown.discounts`
2. **Identifies coupon code** from the discount object
3. **Calls `process_affiliate_referral_coupon()`** with:
   - Coupon code used
   - Stripe subscription ID
   - Stripe customer ID
   - Stripe promotion code ID
   - Stripe coupon ID
   - Subscription amount
   - Discount amount
   - Commission amount ($1.00)

#### Database Changes

**New columns added to `affiliates` table:**

- `coupon_code VARCHAR(50) UNIQUE` - The Stripe coupon code (e.g., "ROB20")

**New columns added to `affiliate_referrals` table:**

- `stripe_promotion_code_id VARCHAR(255)` - Stripe promotion code ID
- `stripe_coupon_id VARCHAR(255)` - Stripe coupon ID
- `discount_amount DECIMAL(10,2)` - Amount of discount applied

#### New SQL Functions

- `process_affiliate_referral_coupon()` - Processes initial referral attribution
- `process_recurring_commission_coupon()` - Handles monthly recurring commissions
- `create_affiliate_with_coupon()` - Creates new affiliates with coupon codes

### 3. Setting Up New Affiliates

#### In Stripe Dashboard:

1. **Create coupon** (e.g., 20% off with code "ROB20")
2. **Create promotion code** that maps to the coupon
3. **Enable promotion codes** in your checkout sessions

#### In MenuMaker Database:

```sql
SELECT create_affiliate_with_coupon(
  '<EMAIL>',  -- User's email
  'ROB20',               -- Coupon code from Stripe
  1.00                   -- Commission rate per month
);
```

### 4. UI Changes

The affiliate section in the profile page now shows:

- **Coupon code** instead of referral link
- **"Copy Coupon"** button instead of "Copy Link"
- **Ticket icon** instead of share icon
- **Enhanced stats** including discount amounts

### 5. Testing

Use the test script at `admin-scripts/test-coupon-affiliate.sql` to:

1. Create test affiliate with coupon code
2. Simulate webhook processing
3. Test recurring commissions
4. Verify cancellation handling

### 6. Backwards Compatibility

The system maintains backwards compatibility by:

- Checking for `client_reference_id` if no coupon code is found
- Supporting both old referral link affiliates and new coupon code affiliates
- Graceful fallback in the UI to show affiliate_code if coupon_code is null

### 7. Benefits of Coupon System

1. **Better UX**: Customers get immediate discounts at checkout
2. **Accurate Attribution**: No need for complex URL tracking
3. **Stripe Native**: Uses Stripe's built-in promotion code system
4. **Simplified Sharing**: Affiliates just share a simple code
5. **Better Analytics**: Track both referrals and discount usage

### 8. Commission Structure

- **Initial Referral**: $1.00 commission when customer subscribes
- **Monthly Recurring**: $1.00 commission per month while subscription is active
- **Cancellation**: No further commissions after subscription ends

### 9. Deployment Steps

1. ✅ Update database schema with new columns
2. ✅ Deploy new SQL functions
3. ✅ Update Stripe webhook handler
4. ✅ Update mobile app UI
5. ✅ Deploy webhook function to Supabase
6. 🔄 Create coupon codes in Stripe dashboard
7. 🔄 Create affiliate accounts with coupon codes
8. 🔄 Test end-to-end flow

### 10. Monitoring

Monitor the system through:

- **Supabase logs**: Check webhook processing
- **Stripe dashboard**: Verify coupon code usage
- **Database queries**: Track referral attribution
- **App analytics**: Monitor affiliate performance

The new coupon-based system provides a more robust and user-friendly affiliate program while maintaining all existing functionality.
