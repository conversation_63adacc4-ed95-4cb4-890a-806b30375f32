# Seasonal Meal Tagging Guide

## Overview

This guide explains how to properly tag meals in the database to enable seasonal filtering. The seasonal filtering system ensures users get weather-appropriate meal suggestions (e.g., no soup in summer).

## Current Issue

- Seasonal filtering code is implemented and working properly
- **Problem**: All meals currently have empty `meal_tags` arrays (`[]`)
- **Result**: All meals are treated as "year-round compatible" by default
- **Impact**: Summer preference users still see soup meals in their weekly plans

## How Seasonal Filtering Works

### Filtering Logic Priority

1. **Explicit seasonal tags** (checked first)
2. **Year-round tags** (always allowed)
3. **Implicit detection** (keywords in meal names/tags)
4. **Empty tags fallback** (currently treats as year-round)

### User Preferences

- `automatic` - Uses current season (summer in July, winter in December, etc.)
- `summer_foods` - Only summer-appropriate meals
- `winter_foods` - Only winter-appropriate meals
- `spring_foods` - Only spring-appropriate meals
- `fall_foods` - Only fall-appropriate meals
- `no_preference` - No seasonal filtering applied

## Tagging System

### 🏷️ Explicit Seasonal Tags

Direct season identifiers (most reliable):

```json
["summer"]     // Summer-only meals
["winter"]     // Winter-only meals
["spring"]     // Spring-only meals
["fall"]       // Fall-only meals
["year_round"] // Always appropriate
["year-round"] // Always appropriate (alternative spelling)
["seasonal"]   // Always appropriate
```

### 🌞 Summer Food Indicators

For salads, grilled items, cold dishes, fresh produce:

```json
[
  "cold",
  "chilled",
  "frozen",
  "ice",
  "gazpacho",
  "salad",
  "grilled",
  "bbq",
  "barbecue",
  "fresh",
  "light",
  "raw",
  "smoothie",
  "sorbet",
  "cucumber",
  "watermelon",
  "tomato",
  "berries",
  "corn",
  "zucchini"
]
```

### ❄️ Winter Food Indicators

For soups, stews, hearty/comfort dishes:

```json
[
  "hot",
  "warm",
  "steaming",
  "soup",
  "stew",
  "chili",
  "roasted",
  "braised",
  "comfort",
  "hearty",
  "rich",
  "creamy",
  "casserole",
  "pot_roast",
  "mulled",
  "cinnamon",
  "nutmeg",
  "ginger",
  "pumpkin",
  "squash",
  "root_vegetables"
]
```

### 🌸 Spring Food Indicators

For fresh, light, green dishes:

```json
[
  "fresh",
  "light",
  "green",
  "asparagus",
  "peas",
  "artichoke",
  "spring_onion",
  "herbs",
  "lemony",
  "bright",
  "crisp",
  "tender",
  "new_potatoes"
]
```

### 🍂 Fall Food Indicators

For harvest, warming, spiced dishes:

```json
[
  "harvest",
  "warming",
  "spiced",
  "apple",
  "pear",
  "pumpkin",
  "squash",
  "sweet_potato",
  "brussels_sprouts",
  "cranberry",
  "cinnamon",
  "nutmeg",
  "maple",
  "roasted",
  "baked"
]
```

## Recommended Tagging Strategy

### Quick Tag Categories

#### Soups & Stews (Winter)

```sql
-- Examples from current problematic meals:
UPDATE meals SET meal_tags = ARRAY['soup', 'warm', 'comfort']
WHERE name = 'Zucchini Soup';

UPDATE meals SET meal_tags = ARRAY['soup', 'warm', 'hearty', 'comfort']
WHERE name = 'Instant Pot Chicken Tortellini and Vegetable Soup';
```

#### Salads (Summer)

```sql
UPDATE meals SET meal_tags = ARRAY['salad', 'fresh', 'light']
WHERE course = 'Main Course' AND name ILIKE '%salad%';
```

#### Grilled Items (Summer)

```sql
UPDATE meals SET meal_tags = ARRAY['grilled', 'bbq']
WHERE name ILIKE '%grilled%' OR name ILIKE '%bbq%';
```

#### Casseroles (Winter/Fall)

```sql
UPDATE meals SET meal_tags = ARRAY['casserole', 'hearty', 'comfort']
WHERE name ILIKE '%casserole%';
```

#### Year-Round Dishes

```sql
UPDATE meals SET meal_tags = ARRAY['year_round']
WHERE name ILIKE '%pasta%' OR name ILIKE '%rice%' OR name ILIKE '%chicken%';
```

## Bulk Tagging SQL Scripts

### 1. Tag All Soup Meals (Winter)

```sql
UPDATE meals
SET meal_tags = ARRAY['soup', 'warm', 'comfort']
WHERE name ILIKE '%soup%' AND meal_tags = '{}';
```

### 2. Tag All Salad Meals (Summer)

```sql
UPDATE meals
SET meal_tags = ARRAY['salad', 'fresh', 'light']
WHERE name ILIKE '%salad%' AND course = 'Main Course' AND meal_tags = '{}';
```

### 3. Tag Grilled Items (Summer)

```sql
UPDATE meals
SET meal_tags = ARRAY['grilled', 'bbq']
WHERE (name ILIKE '%grilled%' OR name ILIKE '%bbq%' OR name ILIKE '%barbecue%')
AND meal_tags = '{}';
```

### 4. Tag Casseroles (Winter/Fall)

```sql
UPDATE meals
SET meal_tags = ARRAY['casserole', 'hearty', 'comfort']
WHERE name ILIKE '%casserole%' AND meal_tags = '{}';
```

### 5. Tag Stews & Chili (Winter)

```sql
UPDATE meals
SET meal_tags = ARRAY['stew', 'warm', 'hearty']
WHERE (name ILIKE '%stew%' OR name ILIKE '%chili%') AND meal_tags = '{}';
```

### 6. Tag Cold/Frozen Items (Summer)

```sql
UPDATE meals
SET meal_tags = ARRAY['cold', 'light']
WHERE (name ILIKE '%cold%' OR name ILIKE '%frozen%' OR name ILIKE '%ice%')
AND meal_tags = '{}';
```

## Testing the Fix

After applying tags, test with:

```sql
-- Check if soup meals are properly tagged
SELECT name, meal_tags
FROM meals
WHERE name ILIKE '%soup%'
LIMIT 10;

-- Verify summer filtering would work
SELECT name, meal_tags
FROM meals
WHERE meal_tags && ARRAY['soup', 'warm', 'hot', 'hearty']
LIMIT 10;
```

## Validation Queries

### Count untagged meals

```sql
SELECT COUNT(*) as untagged_meals
FROM meals
WHERE meal_tags = '{}' OR meal_tags IS NULL;
```

### Count meals by season

```sql
-- Summer meals
SELECT COUNT(*) as summer_meals
FROM meals
WHERE meal_tags && ARRAY['summer', 'salad', 'grilled', 'cold', 'fresh', 'light'];

-- Winter meals
SELECT COUNT(*) as winter_meals
FROM meals
WHERE meal_tags && ARRAY['winter', 'soup', 'stew', 'warm', 'hearty', 'comfort'];
```

## Files to Reference

- **Filtering Logic**: `/lib/seasonalFiltering.ts`
- **Meal Filtering**: `/lib/mealFiltering.ts` (lines 305-315)
- **User Preferences**: `/lib/userPreferences.ts`
- **Database Types**: `/lib/database.types.ts`

## Recommended: Auto-Tagging Script

**Suggestion**: Create an automated script that intelligently tags meals based on name analysis and existing patterns. This script would:

### Auto-Tag Capabilities

- **Name-based Detection**: Scan meal names for obvious seasonal keywords
- **Pattern Recognition**: Identify common seasonal meal patterns
- **Confidence Scoring**: Only auto-tag when confidence is high (>90%)
- **Batch Processing**: Handle large datasets efficiently
- **Validation**: Include verification steps to prevent mis-tagging

### Script Features

```typescript
// Proposed script structure
interface AutoTagRule {
  pattern: RegExp;
  tags: string[];
  confidence: number;
  category: string;
}

const HIGH_CONFIDENCE_RULES = [
  {
    pattern: /soup/i,
    tags: ['soup', 'warm'],
    confidence: 95,
    category: 'winter',
  },
  {
    pattern: /salad/i,
    tags: ['salad', 'fresh'],
    confidence: 90,
    category: 'summer',
  },
  {
    pattern: /grilled|bbq/i,
    tags: ['grilled', 'bbq'],
    confidence: 95,
    category: 'summer',
  },
  {
    pattern: /casserole/i,
    tags: ['casserole', 'hearty'],
    confidence: 90,
    category: 'winter',
  },
  // ... more rules
];
```

### Benefits

- **Speed**: Tag thousands of meals in minutes vs. hours of manual work
- **Consistency**: Apply standardized tagging logic across all meals
- **Safety**: High confidence thresholds prevent incorrect tagging
- **Efficiency**: Focus manual effort on edge cases only

### Implementation Plan

1. **Phase 1**: Create auto-tagging script with high-confidence rules
2. **Phase 2**: Run script on meal database with dry-run mode first
3. **Phase 3**: Apply auto-tags to obvious cases (soups, salads, grilled items)
4. **Phase 4**: Manual review of remaining untagged meals
5. **Phase 5**: Validation and testing of seasonal filtering

**Note**: This script will be created in a future iteration to systematically handle the bulk tagging process.

## Next Steps

1. **Immediate Fix**: Run soup/salad tagging SQL scripts above
2. **Create Auto-Tagging Script**: Develop intelligent meal tagging automation (future task)
3. **Comprehensive Tagging**: Work through meal database systematically
4. **Validation**: Test seasonal filtering with tagged meals
5. **Monitoring**: Watch for user feedback on seasonal appropriateness

## Notes

- The seasonal filtering algorithm is sophisticated and handles edge cases well
- Tags are checked using `&&` (array overlap) operator in PostgreSQL
- Multiple tags per meal are encouraged for better categorization
- Case-insensitive matching is used for implicit detection
- Empty tags currently default to "year-round compatible" (line 75 in seasonalFiltering.ts)
