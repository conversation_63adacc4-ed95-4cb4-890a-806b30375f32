# JSONB to Normalized Ingredients Migration Plan

## Overview

This document outlines the comprehensive migration plan for transitioning the entire MenuMaker application from JSONB ingredient storage to normalized ingredient tables while maintaining full functionality and zero downtime.

## Current State Analysis

### Database Status

- ✅ 2,650 meals with normalized ingredients (99.96% complete)
- ✅ Normalized `ingredients` and `meal_ingredients` tables exist
- ❌ Frontend still dependent on JSONB `meals.ingredients` field
- ❌ 1 meal missing from normalized system ("Whipped Cottage Cheese")

### Critical Dependencies Found

1. **Shopping List Generation** (`app/(tabs)/shopping-list.tsx:412`) - Queries `meals.ingredients` directly
2. **Meal Detail Display** (`app/meal-detail.tsx:151`) - Parses JSONB ingredients for display
3. **AI Meal Display** (`app/ai-meal-detail.tsx`) - Handles JSONB ingredient formats
4. **Ingredient Filtering** (`lib/mealFiltering.ts`) - Uses JSONB for meal matching

### Current Data Structures

**JSONB Format (Legacy):**

```json
[
  { "name": "chicken breast", "amount": "2", "unit": "lbs", "emoji": "🍖" },
  { "name": "olive oil", "amount": "2", "unit": "tbsp", "emoji": "🫒" }
]
```

**Normalized Format (Target):**

- `ingredients` table: Core ingredient data (id, name, emoji, category)
- `meal_ingredients` table: Meal-ingredient relationships with amounts/units
- Join queries instead of JSONB parsing

## Migration Strategy: 4-Phase Approach

### Phase 1: Database Completion & API Updates (2-3 days)

**Database Tasks:**

1. Complete migration for the 1 missing meal ("Whipped Cottage Cheese")
2. Create optimized queries that join `meals` with `meal_ingredients`
3. Update database functions to return normalized ingredient data
4. Create transition API endpoints supporting both formats

**Files to Update:**

- `lib/optimizedQueries.ts` - Add normalized ingredient queries
- `lib/mealFiltering.ts` - Update to query `meal_ingredients` table
- `lib/database.types.ts` - Add normalized ingredient interfaces

**Key Changes:**

```typescript
// Before: Select JSONB ingredients
.select("id, name, ingredients")

// After: Join with normalized ingredients
.select(`
  id, name,
  meal_ingredients!inner(
    amount, unit,
    ingredients!inner(id, name, emoji, category)
  )
`)
```

### Phase 2: Frontend Core Updates (3-4 days)

**Shopping List Migration:**

- Update `app/(tabs)/shopping-list.tsx` (lines 410-464)
- Replace JSONB parsing with normalized ingredient processing
- Maintain ingredient consolidation functionality

**Meal Detail Pages:**

- Update `app/meal-detail.tsx` parseIngredients function
- Update `app/ai-meal-detail.tsx` for AI recipes
- Create backwards-compatible ingredient parsing

**Files to Update:**

- `app/(tabs)/shopping-list.tsx` - Core shopping list generation
- `app/meal-detail.tsx` - Main meal ingredient display
- `app/ai-meal-detail.tsx` - AI recipe ingredient handling
- `app/my-recipes.tsx` - User recipe creation

**Key Changes:**

```typescript
// Before: Parse JSONB ingredients
if (meal.ingredients) {
  ingredients = meal.ingredients;
}

// After: Use normalized ingredients
ingredients = meal.meal_ingredients?.map((mi) => ({
  name: mi.ingredients.name,
  amount: mi.amount,
  unit: mi.unit,
  emoji: mi.ingredients.emoji,
}));
```

### Phase 3: Transition Support & Testing (2-3 days)

**Dual Format Support:**

- Implement fallback logic for any remaining JSONB dependencies
- Add comprehensive error handling
- Create ingredient format detection utilities

**Testing Requirements:**

- Shopping list generation with various meal combinations
- Meal detail page rendering for all meal types
- AI recipe ingredient display
- User recipe creation and editing
- Ingredient filtering and search functionality

**Performance Testing:**

- Compare query performance (JSONB vs JOIN)
- Test shopping list generation speed
- Validate memory usage with joined queries

### Phase 4: Final Migration & Cleanup (1-2 days)

**JSONB Field Removal:**

- Remove `ingredients` field from `meals` table schema
- Remove `ingredients` field from `ai_generated_meals` table
- Clean up legacy parsing code
- Update admin panel to use normalized ingredients only

**Admin Panel Updates:**

- Update `menumaker_admin/src/components/meal-form.tsx`
- Modify ingredient editing interfaces
- Remove JSONB-specific admin functionality

## Implementation Details

### New Database Query Pattern

```sql
-- Optimized normalized ingredient query
SELECT
  m.id, m.name, m.image,
  json_agg(
    json_build_object(
      'name', i.name,
      'amount', mi.amount,
      'unit', mi.unit,
      'emoji', i.emoji,
      'category', i.category
    )
  ) as ingredients
FROM meals m
LEFT JOIN meal_ingredients mi ON m.id = mi.meal_id
LEFT JOIN ingredients i ON mi.ingredient_id = i.id
WHERE m.id = ANY($1)
GROUP BY m.id, m.name, m.image;
```

### Frontend Data Transformation

```typescript
// Standardized ingredient interface
interface NormalizedIngredient {
  name: string;
  amount: string;
  unit: string;
  emoji: string;
  category?: string;
}

// Transition utility function
function parseIngredientsFromNormalized(meal: any): NormalizedIngredient[] {
  return (
    meal.meal_ingredients?.map((mi) => ({
      name: mi.ingredients.name,
      amount: mi.amount.toString(),
      unit: mi.unit,
      emoji: mi.ingredients.emoji,
      category: mi.ingredients.category,
    })) || []
  );
}
```

## Risk Mitigation

### Backwards Compatibility

- Maintain JSONB fallback during transition
- Implement feature flags for gradual rollout
- Create rollback procedures for each phase

### Data Integrity

- Validate ingredient data completeness before migration
- Implement comprehensive error handling
- Monitor for data inconsistencies

### Performance Safeguards

- Index optimization for join queries
- Query performance monitoring
- Fallback to cached data if queries fail

## Success Criteria

### Functional Requirements

- [ ] Shopping lists generate correctly with all ingredients
- [ ] Meal detail pages display all ingredient information
- [ ] AI recipes show proper ingredient formatting
- [ ] User recipes create and save properly
- [ ] Admin panel manages ingredients via normalized tables

### Performance Requirements

- [ ] Meal loading time ≤ current JSONB performance
- [ ] Shopping list generation ≤ 2 seconds
- [ ] No increase in memory usage
- [ ] Database query efficiency maintained

### Data Requirements

- [ ] 100% ingredient data preserved
- [ ] All meal-ingredient relationships intact
- [ ] Ingredient consolidation functionality maintained
- [ ] Search and filtering capabilities preserved

## Timeline: 8-12 days total

- **Phase 1:** Days 1-3 (Database & API)
- **Phase 2:** Days 4-7 (Frontend Core)
- **Phase 3:** Days 8-10 (Testing & Transition)
- **Phase 4:** Days 11-12 (Cleanup & Launch)

## Post-Migration Benefits

- ✅ Consistent ingredient data across admin and mobile
- ✅ Easier ingredient management and merging
- ✅ Better data integrity and relationships
- ✅ Foundation for advanced features (nutrition tracking, substitutions)
- ✅ Improved ingredient search and filtering capabilities

## Notes

- Created: July 6, 2025
- Status: Approved, ready for implementation
- Priority: High - Required for data consistency
- Impact: Application-wide changes required

## Implementation Log

(To be updated as migration progresses)

### Phase 1 Progress

- [ ] Complete missing meal migration
- [ ] Update optimizedQueries.ts
- [ ] Update mealFiltering.ts
- [ ] Create normalized query functions

### Phase 2 Progress

- [ ] Update shopping list generation
- [ ] Update meal detail parsing
- [ ] Update AI meal handling
- [ ] Update user recipe creation

### Phase 3 Progress

- [ ] Implement dual format support
- [ ] Complete testing suite
- [ ] Performance validation
- [ ] Error handling implementation

### Phase 4 Progress

- [ ] Remove JSONB dependencies
- [ ] Update admin panel
- [ ] Clean up legacy code
- [ ] Final validation and launch
