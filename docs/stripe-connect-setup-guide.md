# Stripe Connect Payout Setup Guide

## How the "Setup Payout Account" Button Works

### **Current Status (Demo Mode)**

The button currently shows an informational alert explaining the process since it's in demo mode. To activate the full Stripe Connect flow:

1. **Add Stripe API Keys** to your Supabase Edge Function environment
2. **Uncomment the production code** in `setupStripeConnect()` function
3. **Configure deep linking** for mobile app returns

### **Production Flow Overview**

#### **Step 1: User Taps "Setup Payout Account"**

```javascript
// Calls Edge Function to create Stripe Connect account
const response = await fetch('/functions/v1/stripe-connect', {
  method: 'POST',
  body: JSON.stringify({
    action: 'create_account',
    affiliate_id: affiliate.id,
    email: user.email,
    business_type: 'individual',
  }),
});
```

#### **Step 2: Stripe Connect Account Creation**

```javascript
// Edge Function creates Express Connect account
const account = await stripe.accounts.create({
  type: 'express',
  country: 'US',
  email: email,
  business_type: 'individual',
  capabilities: {
    card_payments: { requested: true },
    transfers: { requested: true },
  },
});
```

#### **Step 3: Generate Onboarding Link**

```javascript
// Creates Stripe-hosted onboarding flow
const accountLink = await stripe.accountLinks.create({
  account: account.id,
  refresh_url: 'menumaker://profile',
  return_url: 'menumaker://profile?connect=success',
  type: 'account_onboarding',
});
```

#### **Step 4: Open Stripe Onboarding**

- **Mobile browser opens** with Stripe's onboarding flow
- **Affiliate completes**:
  - Business information (individual/company)
  - Tax ID (SSN for individuals)
  - Bank account details
  - Identity verification

#### **Step 5: Return to App**

- **User redirected** back to MenuMaker app
- **Status updates** automatically via database
- **Payout section shows** "Ready for Payouts"

### **What Affiliates See During Onboarding**

#### **Business Information**

- Personal/business name
- Address
- Phone number
- Business type selection

#### **Financial Information**

- Bank account (routing + account number)
- Tax ID (SSN for individuals)
- Industry classification

#### **Identity Verification**

- Photo ID upload
- Additional documentation if needed
- Address verification

### **Post-Setup Experience**

#### **For Affiliates**

- **Status badge** shows "Ready for Payouts"
- **Automatic payments** when reaching $25 threshold
- **Email notifications** for each payout
- **Stripe dashboard access** for detailed reports

#### **For You (Platform)**

- **Automated transfers** to affiliate accounts
- **Compliance handled** by Stripe (1099s, etc.)
- **Real-time status** monitoring
- **Failed payment** handling and retries

### **Required Environment Variables**

```bash
# Supabase Edge Function Environment
STRIPE_SECRET_KEY=sk_test_... (or sk_live_...)
STRIPE_SIGNING_SECRET=whsec_...
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=eyJ...
```

### **Mobile App Configuration**

```javascript
// app.json - Deep linking setup
{
  "expo": {
    "scheme": "menumaker",
    "ios": {
      "associatedDomains": ["applinks:yourdomain.com"]
    },
    "android": {
      "intentFilters": [
        {
          "action": "VIEW",
          "data": [{ "scheme": "menumaker" }],
          "category": ["BROWSABLE", "DEFAULT"]
        }
      ]
    }
  }
}
```

### **Testing the Flow**

#### **Test Mode Setup**

1. **Use Stripe test keys** in development
2. **Test bank account**: Use Stripe's test routing numbers
3. **Test SSN**: Use `***********` for testing
4. **Verification**: No real documents needed in test mode

#### **Production Checklist**

- [ ] Stripe Live API keys configured
- [ ] Domain verification for deep links
- [ ] Terms of service updated for Connect
- [ ] Webhook endpoints configured
- [ ] Tax form generation enabled

### **Error Handling**

#### **Common Issues**

- **Missing environment variables**: Check Supabase function config
- **Invalid deep link**: Verify app scheme configuration
- **Stripe API errors**: Check API key permissions
- **Network timeouts**: Implement retry logic

#### **User Experience**

- **Clear error messages** for recoverable issues
- **Support contact** for complex problems
- **Fallback options** when onboarding fails

### **Security Considerations**

#### **Data Protection**

- **PII encryption** in database
- **Stripe tokenization** for sensitive data
- **Audit logging** for all transactions
- **Access controls** on admin functions

#### **Compliance**

- **PCI DSS**: Handled by Stripe
- **KYC/AML**: Stripe's responsibility
- **Tax reporting**: Automatic 1099 generation
- **GDPR**: Data deletion workflows

The system is designed to be production-ready with minimal configuration once you add your Stripe credentials and enable the full flow!
