{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "Bash(npm run lint)", "mcp__supabase__list_projects", "mcp__supabase__list_tables", "Bash(npm install:*)", "mcp__supabase__get_project_url", "mcp__supabase__get_anon_key", "<PERSON><PERSON>(touch:*)", "mcp__supabase__apply_migration", "mcp__supabase__generate_typescript_types", "mcp__supabase__search_docs", "Bash(rm:*)", "Bash(grep:*)", "Bash(npm start)", "<PERSON><PERSON>(killall:*)", "mcp__supabase__execute_sql", "mcp__ide__getDiagnostics", "Bash(find:*)", "Bash(npx supabase exec:*)", "Bash(rg:*)", "<PERSON><PERSON>(tail:*)", "Bash(npx tsc:*)", "Bash(npm run lint:*)", "<PERSON><PERSON>(python:*)", "Bash(awk:*)", "Bash(ls:*)", "Bash(npx expo start:*)", "Bash(eas build:configure)", "Bash(eas project:init:*)", "WebFetch(domain:docs.anthropic.com)", "WebFetch(domain:docs.stripe.com)", "Bash(npx supabase functions deploy:*)", "<PERSON><PERSON>(npx expo-doctor:*)", "Bash(npx expo install:*)", "<PERSON><PERSON>(pkill:*)", "Bash(for file in \"/Users/<USER>/Sites/menumaker_mobile_v2/app/side-dish-detail.tsx\" )", "Bash(\"/Users/<USER>/Sites/menumaker_mobile_v2/app/(tabs)/ai-chat.tsx\" )", "Bash(\"/Users/<USER>/Sites/menumaker_mobile_v2/app/ai-meal-detail.tsx\" )", "Bash(\"/Users/<USER>/Sites/menumaker_mobile_v2/app/user-meal-detail.tsx\")", "Bash(do)", "Bash(if [ -f \"$file\" ])", "<PERSON><PERSON>(then)", "Bash(fi)", "Bash(done)", "Bash(git add:*)", "<PERSON><PERSON>(mv:*)", "mcp__supabase__get_advisors", "Bash(npm run build:*)", "Bash(git commit:*)", "Bash(git push:*)", "WebFetch(domain:docs.instacart.com)", "Bash(npm run ios:*)", "Bash(npx expo lint:*)", "Bash(npm audit:*)", "Bash(npm outdated:*)", "Bash(npx audit-ci:*)", "Bash(npx better-npm-audit:*)", "Bash(npx depcheck:*)", "Bash(npx webpack-bundle-analyzer:*)", "Bash(npx eslint:*)", "Bash(npx expo:*)", "Bash(npm run type-check:*)", "mcp__n8n-mcp__n8n_health_check", "mcp__n8n-mcp__n8n_get_workflow", "mcp__n8n-mcp__search_nodes", "mcp__n8n-mcp__get_node_essentials", "mcp__n8n-mcp__validate_node_operation", "Bash(npm run typecheck:*)", "Bash(npm run format:check:*)", "Bash(npm run format:fix:*)", "WebFetch(domain:easyfamilyrecipes.com)", "WebFetch(domain:abountifulkitchen.com)", "Bash(git -C /Users/<USER>/Sites/menumaker_admin commit -m \"Add .gitignore and remove large files from tracking\")", "Bash(git -C /Users/<USER>/Sites/menumaker_admin push)"]}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["supabase"]}