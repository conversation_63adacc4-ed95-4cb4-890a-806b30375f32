{"meta": {"instanceId": "n8n-local"}, "nodes": [{"parameters": {"resource": "sheet", "operation": "read", "documentId": {"__rl": true, "value": "YOUR_GOOGLE_SHEETS_URL_HERE", "mode": "url"}, "sheetName": {"__rl": true, "value": "meals_rows", "mode": "name"}, "options": {"header": "firstRow", "dataStructure": "object"}}, "id": "read-meals", "name": "Read Meals from Google Sheets", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [100, 200]}, {"parameters": {"resource": "sheet", "operation": "read", "documentId": {"__rl": true, "value": "YOUR_GOOGLE_SHEETS_URL_HERE", "mode": "url"}, "sheetName": {"__rl": true, "value": "ingredients_rows", "mode": "name"}, "options": {"header": "firstRow", "dataStructure": "object"}}, "id": "read-ingredients", "name": "Read Master Ingredients", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [100, 400]}, {"parameters": {"options": {"batchSize": 1}}, "id": "split-meals", "name": "Process Each Meal", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [400, 200]}, {"parameters": {"resource": "chat", "operation": "message", "modelId": "gpt-4", "messages": {"values": [{"role": "system", "content": "You are an expert chef and nutritionist. Your job is to normalize ingredient names to standard, consistent forms.\n\nRules:\n1. Convert to lowercase\n2. Use standard ingredient names (e.g., 'tomato' not 'roma tomato' or 'tomatoes')\n3. Remove quantities, measurements, and preparation methods\n4. Standardize plural/singular forms (use singular)\n5. Remove brand names\n6. Use common grocery store names\n\nExamples:\n- '2 cups diced tomatoes' → 'tomato'\n- 'Heinz <PERSON>' → 'ketchup'\n- 'fresh basil leaves' → 'basil'\n- '1 lb ground beef' → 'ground beef'\n\nReturn ONLY a JSON array of normalized ingredient names, nothing else."}, {"role": "user", "content": "Normalize these ingredients: {{ $json.ingredients }}"}]}, "options": {"maxTokens": 500, "temperature": 0.1}}, "id": "normalize-ai", "name": "Normalize Ingredients with AI", "type": "n8n-nodes-base.openAi", "typeVersion": 1.1, "position": [700, 200]}, {"parameters": {"language": "javascript", "jsCode": "// Parse the AI response and extract normalized ingredients\nconst aiResponse = $input.first().json.choices[0].message.content;\n\ntry {\n  // Try to parse as JSON\n  const normalizedIngredients = JSON.parse(aiResponse);\n  \n  // Return meal data with normalized ingredients\n  return [{\n    ...items[0].json,\n    original_ingredients: items[0].json.ingredients,\n    normalized_ingredients: normalizedIngredients,\n    processing_status: 'success'\n  }];\n} catch (error) {\n  // If parsing fails, return error\n  return [{\n    ...items[0].json,\n    original_ingredients: items[0].json.ingredients,\n    normalized_ingredients: [],\n    processing_status: 'failed',\n    error: error.message\n  }];\n}"}, "id": "parse-response", "name": "Parse AI Response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1000, 200]}, {"parameters": {"language": "javascript", "jsCode": "// Get master ingredients from second input\nconst masterIngredients = $('Read Master Ingredients').all().map(item => \n  item.json.name ? item.json.name.toLowerCase() : ''\n).filter(name => name);\n\nconst mealData = $input.first().json;\nconst normalizedIngredients = mealData.normalized_ingredients || [];\n\n// Check which ingredients are missing from master list\nconst missingIngredients = [];\nconst foundIngredients = [];\n\nnormalizedIngredients.forEach(ingredient => {\n  const ingredientLower = ingredient.toLowerCase();\n  if (masterIngredients.includes(ingredientLower)) {\n    foundIngredients.push(ingredient);\n  } else {\n    missingIngredients.push(ingredient);\n  }\n});\n\nreturn [{\n  ...mealData,\n  found_ingredients: foundIngredients,\n  missing_ingredients: missingIngredients,\n  needs_master_update: missingIngredients.length > 0\n}];"}, "id": "check-master", "name": "Check Against Master List", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1300, 200]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "1", "leftValue": "={{ $json.needs_master_update }}", "rightValue": true, "operator": {"type": "boolean", "operation": "equal"}}], "combinator": "and"}, "options": {}}, "id": "check-missing", "name": "Check if Missing Ingredients", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1600, 200]}, {"parameters": {"language": "javascript", "jsCode": "// Create rows for missing ingredients\nconst missingIngredients = $json.missing_ingredients || [];\nconst results = [];\n\nmissingIngredients.forEach(ingredient => {\n  results.push({\n    name: ingredient,\n    category: 'uncategorized',\n    created_by: 'ai_normalization',\n    created_date: new Date().toISOString(),\n    source_meal_id: $json.id\n  });\n});\n\nreturn results;"}, "id": "create-missing", "name": "Create Missing Ingredients", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1900, 100]}, {"parameters": {"resource": "sheet", "operation": "append", "documentId": {"__rl": true, "value": "YOUR_GOOGLE_SHEETS_URL_HERE", "mode": "url"}, "sheetName": {"__rl": true, "value": "ingredients_rows", "mode": "name"}, "columns": {"mappingMode": "defineBelow", "value": {"name": "={{ $json.name }}", "category": "={{ $json.category }}", "created_by": "={{ $json.created_by }}", "created_date": "={{ $json.created_date }}", "source_meal_id": "={{ $json.source_meal_id }}"}}}, "id": "append-ingredients", "name": "Append to Master Ingredients", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [2200, 100]}, {"parameters": {"resource": "sheet", "operation": "update", "documentId": {"__rl": true, "value": "YOUR_GOOGLE_SHEETS_URL_HERE", "mode": "url"}, "sheetName": {"__rl": true, "value": "meals_rows", "mode": "name"}, "columns": {"mappingMode": "defineBelow", "value": {"ingredients": "={{ JSON.stringify($json.normalized_ingredients) }}", "processing_status": "={{ $json.processing_status }}", "last_updated": "={{ $now.toISO() }}"}}, "options": {"lookupColumn": "id", "lookupValue": "={{ $json.id }}"}}, "id": "update-meal", "name": "Update Meal with Normalized Ingredients", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [1900, 300]}, {"parameters": {"language": "javascript", "jsCode": "console.log('✅ Processed meal:', $json.id);\nconsole.log('📝 Original ingredients:', $json.original_ingredients);\nconsole.log('🎯 Normalized ingredients:', $json.normalized_ingredients);\nconsole.log('✔️ Found in master:', $json.found_ingredients?.length || 0);\nconsole.log('➕ Added to master:', $json.missing_ingredients?.length || 0);\n\nreturn [$json];"}, "id": "log-results", "name": "Log Processing Results", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2200, 300]}], "connections": {"Read Meals from Google Sheets": {"main": [[{"node": "Process Each Meal", "type": "main", "index": 0}]]}, "Read Master Ingredients": {"main": [[{"node": "Check Against Master List", "type": "main", "index": 1}]]}, "Process Each Meal": {"main": [[{"node": "Normalize Ingredients with AI", "type": "main", "index": 0}]]}, "Normalize Ingredients with AI": {"main": [[{"node": "Parse AI Response", "type": "main", "index": 0}]]}, "Parse AI Response": {"main": [[{"node": "Check Against Master List", "type": "main", "index": 0}]]}, "Check Against Master List": {"main": [[{"node": "Check if Missing Ingredients", "type": "main", "index": 0}]]}, "Check if Missing Ingredients": {"main": [[{"node": "Create Missing Ingredients", "type": "main", "index": 0}, {"node": "Update Meal with Normalized Ingredients", "type": "main", "index": 0}], [{"node": "Update Meal with Normalized Ingredients", "type": "main", "index": 0}]]}, "Create Missing Ingredients": {"main": [[{"node": "Append to Master Ingredients", "type": "main", "index": 0}]]}, "Update Meal with Normalized Ingredients": {"main": [[{"node": "Log Processing Results", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2025-07-14T02:30:00.000Z", "updatedAt": "2025-07-14T02:30:00.000Z", "id": "1", "name": "ingredient-normalization"}], "triggerCount": 0, "updatedAt": "2025-07-14T02:30:00.000Z", "versionId": "1", "name": "MenuMaker Ingredient Fix - AI Powered", "active": false}