import React, { createContext, useContext, useState, ReactNode } from 'react';

interface OnboardingState {
  dietary: string[];
  allergies: string[];
  cuisinePreferences: string[];
  householdSize: number | null;
  cookingTime: string | null;
  skillLevel: string | null;
  kitchenEquipment: string[];
  seasonalPreference: string | null;
}

interface OnboardingContextType {
  state: OnboardingState;
  updateDietary: (dietary: string[]) => void;
  updateAllergies: (allergies: string[]) => void;
  updateCuisinePreferences: (cuisinePreferences: string[]) => void;
  updateHouseholdDetails: (
    householdSize: number,
    cookingTime: string,
    skillLevel: string
  ) => void;
  updateKitchenEquipment: (kitchenEquipment: string[]) => void;
  updateSeasonalPreference: (seasonalPreference: string) => void;
  clearState: () => void;
}

const OnboardingContext = createContext<OnboardingContextType | undefined>(
  undefined
);

const initialState: OnboardingState = {
  dietary: [],
  allergies: [],
  cuisinePreferences: [],
  householdSize: null,
  cookingTime: null,
  skillLevel: null,
  kitchenEquipment: [],
  seasonalPreference: null,
};

export function OnboardingProvider({ children }: { children: ReactNode }) {
  const [state, setState] = useState<OnboardingState>(initialState);

  const updateDietary = (dietary: string[]) => {
    setState((prev) => ({ ...prev, dietary }));
  };

  const updateAllergies = (allergies: string[]) => {
    setState((prev) => ({ ...prev, allergies }));
  };

  const updateCuisinePreferences = (cuisinePreferences: string[]) => {
    setState((prev) => ({ ...prev, cuisinePreferences }));
  };

  const updateHouseholdDetails = (
    householdSize: number,
    cookingTime: string,
    skillLevel: string
  ) => {
    setState((prev) => ({ ...prev, householdSize, cookingTime, skillLevel }));
  };

  const updateKitchenEquipment = (kitchenEquipment: string[]) => {
    setState((prev) => ({ ...prev, kitchenEquipment }));
  };

  const updateSeasonalPreference = (seasonalPreference: string) => {
    setState((prev) => ({ ...prev, seasonalPreference }));
  };

  const clearState = () => {
    setState(initialState);
  };

  return (
    <OnboardingContext.Provider
      value={{
        state,
        updateDietary,
        updateAllergies,
        updateCuisinePreferences,
        updateHouseholdDetails,
        updateKitchenEquipment,
        updateSeasonalPreference,
        clearState,
      }}
    >
      {children}
    </OnboardingContext.Provider>
  );
}

export function useOnboarding() {
  const context = useContext(OnboardingContext);
  if (context === undefined) {
    throw new Error('useOnboarding must be used within an OnboardingProvider');
  }
  return context;
}
