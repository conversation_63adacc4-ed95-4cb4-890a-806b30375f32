import { supabase } from '@/lib/supabase';
import {
  authRateLimiter,
  passwordResetRateLimiter,
  RateLimitError,
} from '@/lib/rateLimiter';
import { Session, User } from '@supabase/supabase-js';
import { router } from 'expo-router';
import React, { createContext, useContext, useEffect, useState } from 'react';

type AuthContextType = {
  user: User | null;
  session: Session | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, name?: string) => Promise<void>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  getUserDisplayName: () => string | null;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    let mounted = true;

    // Check for existing session with retry for development
    const getInitialSession = async () => {
      try {
        // In development, add a small delay to ensure AsyncStorage is ready
        if (__DEV__) {
          await new Promise((resolve) => setTimeout(resolve, 100));
        }

        const {
          data: { session },
          error,
        } = await supabase.auth.getSession();

        if (error) {
          // Error getting session
        }

        if (mounted) {
          setSession(session);
          setUser(session?.user ?? null);
          setLoading(false);
        }
      } catch (error) {
        if (mounted) {
          setLoading(false);
        }
      }
    };

    getInitialSession();

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((_event, session) => {
      if (mounted) {
        setSession(session);
        setUser(session?.user ?? null);
        setLoading(false);
      }
    });

    return () => {
      mounted = false;
      subscription.unsubscribe();
    };
  }, []);

  const signIn = async (email: string, password: string) => {
    // Check rate limiting
    if (!authRateLimiter.isAllowed(email)) {
      const remainingTime = authRateLimiter.getRemainingBlockTimeString(email);
      throw new RateLimitError(
        `Too many failed login attempts. Please try again in ${remainingTime}.`,
        remainingTime
      );
    }

    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) {
      // Record failed attempt
      authRateLimiter.recordAttempt(email);
      throw error;
    }

    // Clear rate limit on successful login
    authRateLimiter.clearAttempts(email);
  };

  const signUp = async (email: string, password: string, name?: string) => {
    // Check rate limiting for signup
    if (!authRateLimiter.isAllowed(email)) {
      const remainingTime = authRateLimiter.getRemainingBlockTimeString(email);
      throw new RateLimitError(
        `Too many signup attempts. Please try again in ${remainingTime}.`,
        remainingTime
      );
    }

    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          display_name: name,
        },
      },
    });

    if (error) {
      // Record failed attempt
      authRateLimiter.recordAttempt(email);
      throw error;
    }

    // Clear rate limit on successful signup
    authRateLimiter.clearAttempts(email);
  };

  const signOut = async () => {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;

    // Redirect to index.tsx to follow proper auth flow
    router.replace('/');
  };

  const resetPassword = async (email: string) => {
    // Check rate limiting for password reset (more restrictive)
    if (!passwordResetRateLimiter.isAllowed(email)) {
      const remainingTime =
        passwordResetRateLimiter.getRemainingBlockTimeString(email);
      throw new RateLimitError(
        `Too many password reset attempts. Please try again in ${remainingTime}.`,
        remainingTime
      );
    }

    const { error } = await supabase.auth.resetPasswordForEmail(email);

    if (error) {
      // Record failed attempt for password reset
      passwordResetRateLimiter.recordAttempt(email);
      throw error;
    }

    // Record attempt even on success to prevent abuse
    passwordResetRateLimiter.recordAttempt(email);
  };

  const getUserDisplayName = () => {
    if (!user?.user_metadata) return null;
    return user.user_metadata.display_name || null;
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        session,
        loading,
        signIn,
        signUp,
        signOut,
        resetPassword,
        getUserDisplayName,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
