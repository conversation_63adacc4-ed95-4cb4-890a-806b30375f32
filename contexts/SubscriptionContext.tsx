import { supabase } from '@/lib/supabase';
import React, { createContext, useContext, useEffect, useState } from 'react';
import { useAuth } from './AuthContext';

type SubscriptionStatus = 'active' | 'inactive' | 'cancelled' | 'expired';

type SubscriptionContextType = {
  subscriptionStatus: SubscriptionStatus;
  loading: boolean;
  isSubscribed: boolean;
  stripeCustomerId: string | null;
  refreshSubscription: () => Promise<void>;
  updateSubscriptionStatus: (status: SubscriptionStatus) => Promise<void>;
};

const SubscriptionContext = createContext<SubscriptionContextType | undefined>(
  undefined
);

export function SubscriptionProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const { user, loading: authLoading } = useAuth();
  const [subscriptionStatus, setSubscriptionStatus] =
    useState<SubscriptionStatus>('inactive');
  const [stripeCustomerId, setStripeCustomerId] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  const isSubscribed = subscriptionStatus === 'active';

  const fetchSubscriptionStatus = async () => {
    // Don't run subscription check if auth is still loading
    if (authLoading) {
      console.log('📊 SubscriptionContext: Auth still loading, waiting...');
      return;
    }

    if (!user) {
      console.log('📊 SubscriptionContext: No user, setting inactive');
      setSubscriptionStatus('inactive');
      setStripeCustomerId(null);
      setLoading(false);
      return;
    }

    console.log(
      '📊 SubscriptionContext: Fetching subscription for user:',
      user.id
    );

    try {
      const { data, error } = await supabase
        .from('user_preferences')
        .select('subscription_status, subscription_stripe_customer_id')
        .eq('user_id', user.id)
        .single();

      if (error) {
        console.log(
          '📊 SubscriptionContext: Error fetching subscription:',
          error.message
        );
        setSubscriptionStatus('inactive');
        setStripeCustomerId(null);
      } else if (data) {
        const status =
          (data.subscription_status as SubscriptionStatus) || 'inactive';
        console.log('📊 SubscriptionContext: Loaded subscription:', { status });
        setSubscriptionStatus(status);
        setStripeCustomerId(data.subscription_stripe_customer_id || null);
      }
    } catch (error) {
      console.error(
        '📊 SubscriptionContext: Error in fetchSubscriptionStatus:',
        error
      );
      setSubscriptionStatus('inactive');
      setStripeCustomerId(null);
    } finally {
      setLoading(false);
    }
  };

  const refreshSubscription = async () => {
    setLoading(true);
    await fetchSubscriptionStatus();
  };

  const updateSubscriptionStatus = async (status: SubscriptionStatus) => {
    if (!user) return;

    try {
      const updates: any = {
        subscription_status: status,
        subscription_tier: 'premium', // Always premium since no free tier
        updated_at: new Date().toISOString(),
      };

      if (status === 'active') {
        updates.subscription_start_date = new Date().toISOString();
      }

      const { error } = await supabase
        .from('user_preferences')
        .update(updates)
        .eq('user_id', user.id);

      if (error) {
        console.error('Error updating subscription status:', error);
        throw error;
      }

      setSubscriptionStatus(status);
    } catch (error) {
      console.error('Error in updateSubscriptionStatus:', error);
      throw error;
    }
  };

  useEffect(() => {
    // Reset loading state when user changes (login/logout)
    setLoading(true);
    fetchSubscriptionStatus();
  }, [user, authLoading]);

  return (
    <SubscriptionContext.Provider
      value={{
        subscriptionStatus,
        loading,
        isSubscribed,
        stripeCustomerId,
        refreshSubscription,
        updateSubscriptionStatus,
      }}
    >
      {children}
    </SubscriptionContext.Provider>
  );
}

export function useSubscription() {
  const context = useContext(SubscriptionContext);
  if (context === undefined) {
    throw new Error(
      'useSubscription must be used within a SubscriptionProvider'
    );
  }
  return context;
}
