import { createClient } from '@supabase/supabase-js';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Database } from './database.types';

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY!;

// Environment variables are configured properly

export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: AsyncStorage,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
    debug: false, // Disable auth debugging
  },
  db: {
    schema: 'public',
  },
  global: {
    headers: {
      'x-client-info': 'menumaker-mobile',
    },
  },
  // Connection pooling optimizations
  realtime: {
    params: {
      eventsPerSecond: 10,
    },
  },
});
