/**
 * Utility functions for personalized week calculation across the app
 * Each user has their own weekly cycle based on when they first created a meal plan
 */

/**
 * Get the current week's start date (Sunday) based on user's personalized cycle
 *
 * @param {number} userCycleStartDay - Day of week when user's cycle starts (0=Sunday, 1=Monday, etc.)
 * @returns {string} Week start date in YYYY-MM-DD format
 */
export const getWeekStartDate = (userCycleStartDay: number = 5): string => {
  const now = new Date();

  // Get current UTC time
  const utcYear = now.getUTCFullYear();
  const utcMonth = now.getUTCMonth();
  const utcDate = now.getUTCDate();
  const utcDay = now.getUTCDay();

  // Create a new UTC date
  const weekStart = new Date(Date.UTC(utcYear, utcMonth, utcDate));

  // Calculate days since user's cycle start day
  let daysSinceCycleStart = (utcDay - userCycleStartDay + 7) % 7;

  // If we're on or past the cycle start day, this is the current cycle
  // Otherwise, we need to go back to the previous cycle
  if (utcDay >= userCycleStartDay) {
    // Current cycle - find the Sunday that starts this week
    weekStart.setUTCDate(utcDate - utcDay);
  } else {
    // Previous cycle - find the Sunday that started last week
    weekStart.setUTCDate(utcDate - utcDay - 7);
  }

  // Return as YYYY-MM-DD format
  return weekStart.toISOString().split('T')[0];
};

/**
 * Check if we're in a new week compared to a given date
 *
 * @param {string} previousWeekStart - Previous week start date in YYYY-MM-DD format
 * @param {number} userCycleStartDay - User's cycle start day (0=Sunday, 1=Monday, etc.)
 * @returns {boolean} True if current week is different from previous week
 */
export const isNewWeek = (
  previousWeekStart: string,
  userCycleStartDay: number = 5
): boolean => {
  const currentWeekStart = getWeekStartDate(userCycleStartDay);
  return currentWeekStart !== previousWeekStart;
};

/**
 * Get human-readable description of when the next meal plan will be generated
 *
 * @param {number} userCycleStartDay - User's cycle start day (0=Sunday, 1=Monday, etc.)
 * @returns {string} Description of next meal plan generation time
 */
export const getNextMealPlanGenerationTime = (
  userCycleStartDay: number = 5
): string => {
  const now = new Date();
  const utcDay = now.getUTCDay();
  const utcHours = now.getUTCHours();

  let daysUntilCycleStart: number;

  if (utcDay < userCycleStartDay) {
    // Before cycle start day this week
    daysUntilCycleStart = userCycleStartDay - utcDay;
  } else if (utcDay === userCycleStartDay && utcHours < 0) {
    // On cycle start day before midnight UTC
    daysUntilCycleStart = 0;
  } else {
    // After cycle start day or after midnight on cycle start day
    daysUntilCycleStart = 7 - utcDay + userCycleStartDay;
  }

  const dayNames = [
    'Sunday',
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
    'Saturday',
  ];
  const cycleStartDayName = dayNames[userCycleStartDay];

  if (daysUntilCycleStart === 0) {
    return `Tonight at midnight UTC (${cycleStartDayName})`;
  } else if (daysUntilCycleStart === 1) {
    return `Tomorrow at midnight UTC (${cycleStartDayName})`;
  } else {
    return `In ${daysUntilCycleStart} days at midnight UTC (${cycleStartDayName})`;
  }
};

// Note: User-specific weekly cycle start days are not implemented in the current schema.
// All users use the global UTC Friday start cycle for consistency.

/**
 * Get the global weekly cycle start day (Friday)
 * All users globally get new meal plans on Friday at midnight UTC
 *
 * @returns {number} The global cycle start day (5 = Friday)
 */
export const getGlobalWeeklyCycleStartDay = (): number => {
  return 5; // Friday - all users use the same global cycle
};
