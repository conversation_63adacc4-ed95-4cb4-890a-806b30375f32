/**
 * Client-side rate limiter to prevent abuse of authentication endpoints
 * This provides basic protection against brute force attacks
 */

interface RateLimitAttempt {
  timestamp: number;
  count: number;
}

class RateLimiter {
  private attempts: Map<string, RateLimitAttempt> = new Map();
  private readonly maxAttempts: number;
  private readonly windowMs: number;
  private readonly blockDurationMs: number;

  constructor(
    maxAttempts: number = 5,
    windowMs: number = 15 * 60 * 1000,
    blockDurationMs: number = 15 * 60 * 1000
  ) {
    this.maxAttempts = maxAttempts;
    this.windowMs = windowMs; // 15 minutes default
    this.blockDurationMs = blockDurationMs; // 15 minutes block
  }

  /**
   * Check if an action is allowed for a given key (usually email or IP)
   */
  isAllowed(key: string): boolean {
    const now = Date.now();
    const attempt = this.attempts.get(key);

    if (!attempt) {
      return true;
    }

    // Check if the window has expired
    if (now - attempt.timestamp > this.windowMs) {
      this.attempts.delete(key);
      return true;
    }

    // Check if still blocked
    if (attempt.count >= this.maxAttempts) {
      return false;
    }

    return true;
  }

  /**
   * Record a failed attempt
   */
  recordAttempt(key: string): void {
    const now = Date.now();
    const attempt = this.attempts.get(key);

    if (!attempt || now - attempt.timestamp > this.windowMs) {
      // First attempt or window expired
      this.attempts.set(key, { timestamp: now, count: 1 });
    } else {
      // Increment count
      this.attempts.set(key, {
        timestamp: attempt.timestamp,
        count: attempt.count + 1,
      });
    }
  }

  /**
   * Clear attempts for a key (on successful authentication)
   */
  clearAttempts(key: string): void {
    this.attempts.delete(key);
  }

  /**
   * Get remaining time until unblocked (in milliseconds)
   */
  getRemainingBlockTime(key: string): number {
    const attempt = this.attempts.get(key);
    if (!attempt || attempt.count < this.maxAttempts) {
      return 0;
    }

    const elapsedTime = Date.now() - attempt.timestamp;
    const remainingTime = this.blockDurationMs - elapsedTime;

    return Math.max(0, remainingTime);
  }

  /**
   * Get a human-readable time string for remaining block time
   */
  getRemainingBlockTimeString(key: string): string {
    const remainingMs = this.getRemainingBlockTime(key);
    if (remainingMs === 0) {
      return '';
    }

    const minutes = Math.ceil(remainingMs / (60 * 1000));
    return `${minutes} minute${minutes !== 1 ? 's' : ''}`;
  }
}

// Create singleton instances for different rate limiting scenarios
export const authRateLimiter = new RateLimiter(5, 15 * 60 * 1000); // 5 attempts per 15 minutes
export const passwordResetRateLimiter = new RateLimiter(3, 60 * 60 * 1000); // 3 attempts per hour

export class RateLimitError extends Error {
  constructor(
    message: string,
    public remainingTime: string
  ) {
    super(message);
    this.name = 'RateLimitError';
  }
}
