/**
 * Smart ingredient consolidation with unit conversion support
 * Combines compatible units and optimizes for Instacart API
 */

import { getIngredientEmoji } from './ingredientEmoji';
import { 
  consolidateAmounts, 
  formatAmount, 
  getInstacartUnit,
  findUnitConfig,
  areUnitsCompatible
} from './unitConversion';

// Ingredient consolidation module ready for production use

export interface ParsedIngredient {
  amount: number;
  unit: string;
  name: string;
  originalText: string;
}

export interface ConsolidatedIngredient {
  name: string;
  amount: number;
  unit: string;
  displayText: string;
  emoji: string;
  sources: { mealId?: string; mealName?: string }[];
  // Support for multiple units of the same ingredient
  measurements: Array<{
    amount: number;
    unit: string;
    displayText: string;
    instacartUnit: string;
  }>;
  instacartUnit?: {
    code: string;
    name: string;
    original_unit: string;
  };
}


/**
 * Parse ingredient text to extract amount, unit, and name
 * Enhanced with better unit recognition using the unit conversion system
 */
export function parseIngredient(text: string): ParsedIngredient | null {
  if (!text || typeof text !== 'string') return null;

  const trimmed = text.trim();

  // Enhanced regex patterns for better parsing
  const amountRegex = /^(\d+\.?\d*|\d*\.?\d+)\s*/;
  const fractionRegex = /^(\d+\s+)?(\d+\/\d+)\s*/;
  const mixedFractionRegex = /^(\d+)\s+(\d+\/\d+)\s*/;

  let amount = 1;
  let remaining = trimmed;

  // Try to match mixed fractions first (e.g., "1 1/2")
  const mixedFractionMatch = remaining.match(mixedFractionRegex);
  if (mixedFractionMatch) {
    const whole = parseInt(mixedFractionMatch[1]);
    const [numerator, denominator] = mixedFractionMatch[2]
      .split('/')
      .map((n) => parseInt(n));
    amount = whole + numerator / denominator;
    remaining = remaining.substring(mixedFractionMatch[0].length);
  } else {
    // Try simple fraction (e.g., "1/2")
    const fractionMatch = remaining.match(fractionRegex);
    if (fractionMatch) {
      const whole = fractionMatch[1] ? parseInt(fractionMatch[1]) : 0;
      const [numerator, denominator] = fractionMatch[2]
        .split('/')
        .map((n) => parseInt(n));
      amount = whole + numerator / denominator;
      remaining = remaining.substring(fractionMatch[0].length);
    } else {
      // Try decimal number
      const decimalMatch = remaining.match(amountRegex);
      if (decimalMatch) {
        amount = parseFloat(decimalMatch[1]);
        remaining = remaining.substring(decimalMatch[0].length);
      }
    }
  }

  // Extract unit using the new unit conversion system
  let unit = '';
  const words = remaining.split(/\s+/).filter(Boolean);

  if (words.length > 0) {
    // First check if the ingredient name suggests a special unit (before checking for generic units)
    const fullText = remaining.toLowerCase();
    const isSpiceOrSeasoning = fullText.includes('salt') || fullText.includes('pepper') || 
                              fullText.includes('spice') || fullText.includes('seasoning') ||
                              fullText.includes('extract') || fullText.includes('powder') ||
                              fullText.includes('vanilla') || fullText.includes('cinnamon') ||
                              fullText.includes('paprika') || fullText.includes('oregano') ||
                              fullText.includes('basil') || fullText.includes('thyme');
    
    // Check if first word is a recognized unit
    const firstWord = words[0].toLowerCase();
    const unitConfig = findUnitConfig(firstWord);
    
    if (unitConfig && !isSpiceOrSeasoning) {
      // Use the recognized unit only if it's not a spice/seasoning
      unit = unitConfig.name;
      remaining = words.slice(1).join(' ');
    } else if (unitConfig && isSpiceOrSeasoning && firstWord === 'whole') {
      // Special case: "whole" + spice should become teaspoon
      unit = 'teaspoon';
      remaining = words.slice(1).join(' ');
    } else if (unitConfig) {
      // Use the recognized unit normally for non-whole units
      unit = unitConfig.name;
      remaining = words.slice(1).join(' ');
    } else {
      // Check for compound units like "fl oz"
      if (words.length > 1) {
        const twoWordUnit = `${firstWord} ${words[1].toLowerCase()}`;
        const compoundConfig = findUnitConfig(twoWordUnit);
        if (compoundConfig) {
          unit = compoundConfig.name;
          remaining = words.slice(2).join(' ');
        } else if (isSpiceOrSeasoning) {
          unit = 'teaspoon';
        } else {
          unit = 'each';
        }
      } else if (isSpiceOrSeasoning) {
        unit = 'teaspoon';
      } else {
        unit = 'each';
      }
    }
  } else {
    unit = 'each';
  }

  // Clean up the ingredient name
  const name = remaining
    .trim()
    .replace(/^(of|,)\s*/i, '') // Remove leading "of" or comma
    .trim();

  if (!name) return null;

  return {
    amount,
    unit,
    name: name.toLowerCase(), // Normalize name to lowercase for comparison
    originalText: text,
  };
}

/**
 * Multi-unit ingredient consolidation
 * Groups ingredients by name and shows all measurements together
 */
export function consolidateIngredients(
  ingredients: Array<{
    text: string;
    mealId?: string;
    mealName?: string;
  }>
): ConsolidatedIngredient[] {
  // Map to store consolidated ingredients by ingredient name only
  const consolidatedMap = new Map<string, ConsolidatedIngredient>();

  for (const ingredient of ingredients) {
    const parsed = parseIngredient(ingredient.text);

    if (!parsed) {
      // If we can't parse it, add it as-is
      const key = ingredient.text.toLowerCase();
      if (!consolidatedMap.has(key)) {
        consolidatedMap.set(key, {
          name: ingredient.text,
          amount: 1,
          unit: 'each',
          displayText: ingredient.text,
          emoji: getIngredientEmoji(ingredient.text),
          sources: [],
          measurements: [{
            amount: 1,
            unit: 'each',
            displayText: ingredient.text,
            instacartUnit: 'each'
          }],
          instacartUnit: {
            code: 'each',
            name: 'each',
            original_unit: ''
          }
        });
      }
      consolidatedMap.get(key)!.sources.push({
        mealId: ingredient.mealId,
        mealName: ingredient.mealName,
      });
      continue;
    }

    // Use ingredient name as key (without unit)
    const key = parsed.name;

    if (consolidatedMap.has(key)) {
      const existing = consolidatedMap.get(key)!;
      
      // Check if we already have this unit
      const existingMeasurement = existing.measurements.find(m => m.unit === parsed.unit);
      
      if (existingMeasurement) {
        // Add to existing measurement of same unit
        existingMeasurement.amount += parsed.amount;
        existingMeasurement.displayText = formatMeasurementDisplay(existingMeasurement.amount, existingMeasurement.unit);
      } else {
        // Add new measurement for different unit
        existing.measurements.push({
          amount: parsed.amount,
          unit: parsed.unit,
          displayText: formatMeasurementDisplay(parsed.amount, parsed.unit),
          instacartUnit: getInstacartUnit(parsed.unit)
        });
      }
      
      // Add source
      existing.sources.push({
        mealId: ingredient.mealId,
        mealName: ingredient.mealName,
      });
      
    } else {
      // Create new consolidated ingredient
      consolidatedMap.set(key, {
        name: parsed.name,
        amount: parsed.amount,
        unit: parsed.unit,
        displayText: '', // Will be set below
        emoji: getIngredientEmoji(parsed.name),
        sources: [
          {
            mealId: ingredient.mealId,
            mealName: ingredient.mealName,
          },
        ],
        measurements: [{
          amount: parsed.amount,
          unit: parsed.unit,
          displayText: formatMeasurementDisplay(parsed.amount, parsed.unit),
          instacartUnit: getInstacartUnit(parsed.unit)
        }],
        instacartUnit: {
          code: getInstacartUnit(parsed.unit),
          name: parsed.unit,
          original_unit: parsed.unit
        }
      });
    }
  }

  // Convert map to array and format display text
  const result = Array.from(consolidatedMap.values())
    // Filter out water - people don't buy water for recipes
    .filter((item) => {
      const name = item.name.toLowerCase().trim();
      return name !== 'water' && name !== 'tap water' && name !== 'cold water' && name !== 'hot water';
    })
    .map((item) => {
    // Sort measurements by unit type (weight, volume, count) and amount
    item.measurements.sort((a, b) => {
      const aConfig = findUnitConfig(a.unit);
      const bConfig = findUnitConfig(b.unit);
      
      // Sort by unit type priority: weight > volume > count
      const typePriority = { weight: 1, volume: 2, count: 3 };
      const aTypePriority = aConfig ? typePriority[aConfig.type] : 4;
      const bTypePriority = bConfig ? typePriority[bConfig.type] : 4;
      
      if (aTypePriority !== bTypePriority) {
        return aTypePriority - bTypePriority;
      }
      
      // Within same type, sort by amount (descending)
      return b.amount - a.amount;
    });

    // Create display text from all measurements
    item.displayText = item.measurements
      .map(m => m.displayText)
      .join(', ');

    // Set primary amount/unit to the first (largest/most important) measurement
    if (item.measurements.length > 0) {
      item.amount = item.measurements[0].amount;
      item.unit = item.measurements[0].unit;
      item.instacartUnit = {
        code: item.measurements[0].instacartUnit,
        name: item.measurements[0].unit,
        original_unit: item.measurements[0].unit
      };
    }

    // Capitalize the ingredient name for better display
    item.name = capitalizeWords(item.name);

    return item;
  });

  // Sort alphabetically by name for easy browsing
  return result.sort((a, b) => a.name.localeCompare(b.name));
}

/**
 * Format a single measurement for display
 */
function formatMeasurementDisplay(amount: number, unit: string): string {
  const formattedAmount = formatAmount(amount);
  const displayUnit = getInstacartUnit(unit);
  
  if (unit === 'each') {
    return amount > 1 ? `${formattedAmount} each` : '1 each';
  }
  
  return `${formattedAmount} ${displayUnit}`;
}

/**
 * Capitalize the first letter of each word in a string
 */
function capitalizeWords(str: string): string {
  return str
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
}


/**
 * Get ingredient category for grouping in shopping list
 */
export function getIngredientCategory(ingredientName: string): string {
  const name = ingredientName.toLowerCase();

  // Produce
  const produceKeywords = [
    'lettuce',
    'tomato',
    'onion',
    'garlic',
    'pepper',
    'carrot',
    'celery',
    'potato',
    'broccoli',
    'spinach',
    'kale',
    'cabbage',
    'cucumber',
    'zucchini',
    'apple',
    'banana',
    'orange',
    'lemon',
    'lime',
    'berries',
    'strawberry',
    'herbs',
    'basil',
    'parsley',
    'cilantro',
    'thyme',
    'oregano',
    'rosemary',
  ];

  // Meat & Seafood
  const meatKeywords = [
    'chicken',
    'beef',
    'pork',
    'turkey',
    'lamb',
    'fish',
    'salmon',
    'tuna',
    'shrimp',
    'bacon',
    'sausage',
    'ground',
    'steak',
    'breast',
    'thigh',
  ];

  // Dairy
  const dairyKeywords = [
    'milk',
    'cheese',
    'yogurt',
    'butter',
    'cream',
    'sour cream',
    'cottage cheese',
    'mozzarella',
    'cheddar',
    'parmesan',
    'egg',
    'eggs',
  ];

  // Pantry
  const pantryKeywords = [
    'rice',
    'pasta',
    'flour',
    'sugar',
    'salt',
    'pepper',
    'oil',
    'vinegar',
    'sauce',
    'spice',
    'seasoning',
    'honey',
    'syrup',
    'can',
    'canned',
    'beans',
    'stock',
    'broth',
    'bread crumbs',
    'nuts',
    'peanut butter',
  ];

  // Bakery
  const bakeryKeywords = [
    'bread',
    'rolls',
    'buns',
    'tortilla',
    'pita',
    'bagel',
    'croissant',
  ];

  // Frozen
  const frozenKeywords = [
    'frozen',
    'ice cream',
    'peas',
    'corn',
    'mixed vegetables',
  ];

  // Check categories
  if (produceKeywords.some((keyword) => name.includes(keyword)))
    return 'Produce';
  if (meatKeywords.some((keyword) => name.includes(keyword)))
    return 'Meat & Seafood';
  if (dairyKeywords.some((keyword) => name.includes(keyword))) return 'Dairy';
  if (bakeryKeywords.some((keyword) => name.includes(keyword))) return 'Bakery';
  if (frozenKeywords.some((keyword) => name.includes(keyword))) return 'Frozen';
  if (pantryKeywords.some((keyword) => name.includes(keyword))) return 'Pantry';

  return 'Other';
}
