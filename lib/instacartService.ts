import { ConsolidatedIngredient } from './simpleIngredientConsolidation';
import {
  createInstacartOrder,
  InstacartProxyError,
  InstacartOrderRequest,
  InstacartOrderResponse,
  InstacartItem,
} from '@/lib/instacartProxy';

// Legacy interface for backwards compatibility - now mapped to proxy format
export interface InstacartIngredient {
  name: string;
  display_text: string;
  measurements: Array<{
    quantity: number;
    unit: string;
  }>;
}

export interface AffiliateTrackingData {
  affiliateCode?: string;
  userId?: string;
  userEmail?: string;
}

/**
 * Converts our consolidated ingredients to Instacart proxy format
 */
export function convertToInstacartItems(
  ingredients: ConsolidatedIngredient[]
): InstacartItem[] {
  const result = ingredients
    .filter((ingredient) => {
      // Filter out invalid ingredients
      return (
        ingredient.name &&
        ingredient.name.trim() !== '' &&
        ingredient.amount > 0
      );
    })
    .map((ingredient) => {
      return {
        quantity: Math.round(ingredient.amount * 100) / 100, // Round to 2 decimal places
        item: {
          name: ingredient.name.trim(),
          size:
            ingredient.unit && ingredient.unit.trim() !== ''
              ? ingredient.unit.trim()
              : undefined,
        },
      };
    });

  return result;
}

/**
 * Legacy function for backwards compatibility - converts to old format
 */
export function convertToInstacartIngredients(
  ingredients: ConsolidatedIngredient[]
): InstacartIngredient[] {
  return ingredients.map((ingredient) => {
    return {
      name: ingredient.name,
      display_text: ingredient.displayText,
      measurements: [
        {
          quantity: ingredient.amount,
          unit: ingredient.unit || 'item',
        },
      ],
    };
  });
}

/**
 * Creates a shopping order on Instacart with ingredients and affiliate tracking
 * Now uses secure proxy to protect API credentials
 */
export async function createInstacartShoppingList(
  ingredients: ConsolidatedIngredient[],
  title: string = 'My Weekly Meal Plan',
  partnerLinkbackUrl?: string,
  affiliateData?: AffiliateTrackingData
): Promise<string> {
  try {
    // Convert ingredients to proxy format
    const instacartItems = convertToInstacartItems(ingredients);

    // Build the order request for the proxy
    const orderRequest: InstacartOrderRequest = {
      items: instacartItems,
      affiliateData:
        affiliateData?.affiliateCode && affiliateData?.userId
          ? {
              affiliateCode: affiliateData.affiliateCode,
              userId: affiliateData.userId,
            }
          : undefined,
    };

    // Create order through secure proxy
    const orderResponse = await createInstacartOrder(orderRequest);

    // Log the affiliate tracking for monitoring
    if (affiliateData?.affiliateCode) {
      console.log('🛒 Instacart order created with affiliate tracking:', {
        affiliateCode: affiliateData.affiliateCode,
        userId: affiliateData.userId,
        orderId: orderResponse.id,
        partnerUserId: orderResponse.partner_user_id,
      });
    }

    return orderResponse.url;
  } catch (error) {
    if (error instanceof InstacartProxyError) {
      console.error('Instacart proxy error:', error.message);
      throw new Error(`Shopping service error: ${error.message}`);
    }

    console.error('Error creating Instacart shopping list:', error);
    throw new Error('Failed to create shopping list. Please try again.');
  }
}

/**
 * Opens the Instacart shopping list URL in a web browser or WebView
 */
export function openInstacartShoppingList(url: string) {
  // For React Native, we'll handle this in the component using WebView or Linking
  return url;
}

/**
 * Track Instacart conversion for affiliate commission
 * This function should be called when we receive conversion data from Instacart webhooks
 */
export async function trackInstacartConversion(conversionData: {
  orderId: string;
  orderTotal: number;
  trackingId?: string;
  affiliateCode?: string;
  userId?: string;
}): Promise<void> {
  try {
    // Log the conversion
    console.log('📊 Instacart conversion tracked:', conversionData);

    // TODO: Store conversion data in database for affiliate commission calculation
    // This would typically involve:
    // 1. Recording the order in our database
    // 2. Calculating commission for the affiliate
    // 3. Updating affiliate earnings

    // For now, just log for monitoring
    if (conversionData.affiliateCode && conversionData.orderTotal > 0) {
      console.log(
        `💰 Commission earned for affiliate ${conversionData.affiliateCode}: Order total $${conversionData.orderTotal}`
      );
    }
  } catch (error) {
    console.error('Error tracking Instacart conversion:', error);
  }
}

/**
 * Get linkback URL for partner attribution
 */
export function getPartnerLinkbackUrl(): string {
  // This URL should be where Instacart sends users back to our app
  return 'https://yourdomain.com/instacart/return';
}

/**
 * Generate tracking URL with affiliate parameters
 */
export function generateTrackingUrl(
  baseUrl: string,
  affiliateCode?: string
): string {
  if (!affiliateCode) return baseUrl;

  const url = new URL(baseUrl);
  url.searchParams.set('ref', affiliateCode);
  url.searchParams.set('source', 'menumaker');

  return url.toString();
}
