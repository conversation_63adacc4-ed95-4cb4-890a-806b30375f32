/**
 * Secure Storage Utility
 * Uses SecureStore for sensitive data and AsyncStorage for non-sensitive data
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import * as SecureStore from 'expo-secure-store';

// Categories of data that require secure storage
const SENSITIVE_DATA_PATTERNS = [
  /user_.*_\d+/, // User-specific data with user IDs
  /session_/, // Session-related data
  /auth_/, // Authentication data
  /token_/, // Tokens and keys
  /credential_/, // Credentials
  /password_/, // Password-related data
  /payment_/, // Payment information
  /affiliate_/, // Affiliate data
  /checkedItems_/, // User's checked items (personal shopping data)
  /favorites_/, // User's favorites (personal data)
  /preferences_/, // User preferences (personal data)
];

// Non-sensitive data that can use AsyncStorage
const NON_SENSITIVE_PATTERNS = [
  /mealPlanUpdated/, // Global meal plan update timestamps
  /favoritesUpdated/, // Global favorites update timestamps
  /lastAutoScroll/, // UI state
  /theme_/, // Theme preferences (non-sensitive)
  /tutorial_/, // Tutorial completion states
  /app_version/, // App version info
];

/**
 * Determines if a key should use secure storage
 */
function shouldUseSecureStorage(key: string): boolean {
  // Check if it's explicitly non-sensitive first
  for (const pattern of NON_SENSITIVE_PATTERNS) {
    if (pattern.test(key)) {
      return false;
    }
  }

  // Check if it's sensitive
  for (const pattern of SENSITIVE_DATA_PATTERNS) {
    if (pattern.test(key)) {
      return true;
    }
  }

  // Default to secure storage for safety
  return true;
}

/**
 * Securely store an item
 */
export async function setSecureItem(key: string, value: string): Promise<void> {
  try {
    if (shouldUseSecureStorage(key)) {
      await SecureStore.setItemAsync(key, value);
    } else {
      await AsyncStorage.setItem(key, value);
    }
  } catch (error) {
    throw new Error(`Failed to store item securely: ${error}`);
  }
}

/**
 * Securely retrieve an item
 */
export async function getSecureItem(key: string): Promise<string | null> {
  try {
    if (shouldUseSecureStorage(key)) {
      return await SecureStore.getItemAsync(key);
    } else {
      return await AsyncStorage.getItem(key);
    }
  } catch (error) {
    throw new Error(`Failed to retrieve item securely: ${error}`);
  }
}

/**
 * Securely remove an item
 */
export async function removeSecureItem(key: string): Promise<void> {
  try {
    if (shouldUseSecureStorage(key)) {
      await SecureStore.deleteItemAsync(key);
    } else {
      await AsyncStorage.removeItem(key);
    }
  } catch (error) {
    throw new Error(`Failed to remove item securely: ${error}`);
  }
}

/**
 * Securely store JSON data
 */
export async function setSecureJSON<T>(key: string, value: T): Promise<void> {
  const jsonString = JSON.stringify(value);
  await setSecureItem(key, jsonString);
}

/**
 * Securely retrieve JSON data
 */
export async function getSecureJSON<T>(key: string): Promise<T | null> {
  const jsonString = await getSecureItem(key);
  if (!jsonString) return null;

  try {
    return JSON.parse(jsonString) as T;
  } catch (error) {
    throw new Error(`Failed to parse JSON for key ${key}: ${error}`);
  }
}

/**
 * Clear all secure storage (useful for logout)
 */
export async function clearSecureStorage(): Promise<void> {
  try {
    // Clear all SecureStore items (this is not possible directly with SecureStore)
    // So we'll track keys manually or migrate one by one

    // Clear sensitive AsyncStorage items by pattern
    const allKeys = await AsyncStorage.getAllKeys();
    const sensitiveKeys = allKeys.filter((key) => shouldUseSecureStorage(key));

    await AsyncStorage.multiRemove(sensitiveKeys);

    // Note: SecureStore doesn't have a clear all method
    // Individual keys need to be removed manually
  } catch (error) {
    throw new Error(`Failed to clear secure storage: ${error}`);
  }
}

/**
 * Migrate existing AsyncStorage data to SecureStore
 */
export async function migrateToSecureStorage(): Promise<void> {
  try {
    const allKeys = await AsyncStorage.getAllKeys();

    for (const key of allKeys) {
      if (shouldUseSecureStorage(key)) {
        const value = await AsyncStorage.getItem(key);
        if (value !== null) {
          // Store in SecureStore
          await SecureStore.setItemAsync(key, value);
          // Remove from AsyncStorage
          await AsyncStorage.removeItem(key);
        }
      }
    }
  } catch (error) {
    throw new Error(`Failed to migrate to secure storage: ${error}`);
  }
}

/**
 * Enhanced storage wrapper that automatically chooses the right storage method
 */
export const SecureStorage = {
  setItem: setSecureItem,
  getItem: getSecureItem,
  removeItem: removeSecureItem,
  setJSON: setSecureJSON,
  getJSON: getSecureJSON,
  clear: clearSecureStorage,
  migrate: migrateToSecureStorage,
};
