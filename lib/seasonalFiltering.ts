/**
 * Seasonal filtering utilities for meal recommendations
 * Ensures weather-appropriate meal suggestions (no soup in summer!)
 */

export type SeasonalPreference =
  | 'automatic'
  | 'summer_foods'
  | 'winter_foods'
  | 'spring_foods'
  | 'fall_foods'
  | 'no_preference';

export type SeasonalPreferences = SeasonalPreference[];

export type Season = 'spring' | 'summer' | 'fall' | 'winter';

/**
 * Determines the current season based on date and hemisphere
 * Uses meteorological seasons (Mar-May=Spring, Jun-Aug=Summer, etc.)
 */
export function getCurrentSeason(isNorthernHemisphere: boolean = true): Season {
  const now = new Date();
  const month = now.getMonth() + 1; // getMonth() returns 0-11, we want 1-12

  if (isNorthernHemisphere) {
    // Northern Hemisphere seasons
    if (month >= 3 && month <= 5) return 'spring'; // March - May
    if (month >= 6 && month <= 8) return 'summer'; // June - August
    if (month >= 9 && month <= 11) return 'fall'; // September - November
    return 'winter'; // December - February
  } else {
    // Southern Hemisphere seasons (opposite)
    if (month >= 3 && month <= 5) return 'fall'; // March - May
    if (month >= 6 && month <= 8) return 'winter'; // June - August
    if (month >= 9 && month <= 11) return 'spring'; // September - November
    return 'summer'; // December - February
  }
}

/**
 * Maps seasonal preferences to actual seasons
 */
export function getTargetSeasonFromPreference(
  preference: SeasonalPreference,
  isNorthernHemisphere: boolean = true
): Season | null {
  switch (preference) {
    case 'automatic':
      return getCurrentSeason(isNorthernHemisphere);
    case 'spring_foods':
      return 'spring';
    case 'summer_foods':
      return 'summer';
    case 'fall_foods':
      return 'fall';
    case 'winter_foods':
      return 'winter';
    case 'no_preference':
      return null; // No seasonal filtering
    default:
      return null;
  }
}

/**
 * Maps multiple seasonal preferences to actual seasons
 * Returns array of seasons that should be included
 */
export function getTargetSeasonsFromPreferences(
  preferences: SeasonalPreferences | null,
  isNorthernHemisphere: boolean = true
): Season[] | null {
  if (!preferences || preferences.length === 0) {
    return null; // No seasonal filtering
  }

  // If 'no_preference' is included, return null (no filtering)
  if (preferences.includes('no_preference')) {
    return null;
  }

  const seasons: Season[] = [];
  
  for (const preference of preferences) {
    const season = getTargetSeasonFromPreference(preference, isNorthernHemisphere);
    if (season && !seasons.includes(season)) {
      seasons.push(season);
    }
  }

  return seasons.length > 0 ? seasons : null;
}

/**
 * Checks if a meal is seasonally appropriate based on its meal_tags
 */
export function isSeasonallyCompatible(
  mealTags: string[] | null,
  targetSeason: Season | null
): boolean {
  // If no seasonal filtering requested, allow all meals
  if (!targetSeason) return true;

  // If meal has no tags, assume it's year-round compatible
  if (!mealTags || mealTags.length === 0) return true;

  const tags = mealTags.map((tag) => tag.toLowerCase());

  // Check for explicit seasonal tags
  if (tags.includes(targetSeason.toLowerCase())) return true;

  // Check for year-round compatibility
  if (
    tags.includes('year_round') ||
    tags.includes('year-round') ||
    tags.includes('seasonal')
  ) {
    return true;
  }

  // Check for seasonal compatibility based on common food patterns
  return isImplicitlySeasonallyCompatible(tags, targetSeason);
}

/**
 * Checks if a meal is seasonally appropriate for multiple target seasons
 */
export function isSeasonallyCompatibleWithMultiple(
  mealTags: string[] | null,
  targetSeasons: Season[] | null
): boolean {
  // If no seasonal filtering requested, allow all meals
  if (!targetSeasons || targetSeasons.length === 0) return true;

  // Check if meal is compatible with ANY of the target seasons
  return targetSeasons.some(season => 
    isSeasonallyCompatible(mealTags, season)
  );
}

/**
 * Determines seasonal compatibility based on common food patterns and keywords
 * This helps with meals that aren't explicitly tagged but are obviously seasonal
 */
function isImplicitlySeasonallyCompatible(
  tags: string[],
  targetSeason: Season
): boolean {
  // Summer-appropriate food indicators
  const summerIndicators = [
    'cold',
    'chilled',
    'frozen',
    'ice',
    'gazpacho',
    'salad',
    'grilled',
    'bbq',
    'barbecue',
    'fresh',
    'light',
    'raw',
    'smoothie',
    'sorbet',
    'cucumber',
    'watermelon',
    'tomato',
    'berries',
    'corn',
    'zucchini',
  ];

  // Winter-appropriate food indicators
  const winterIndicators = [
    'hot',
    'warm',
    'steaming',
    'soup',
    'stew',
    'chili',
    'roasted',
    'braised',
    'comfort',
    'hearty',
    'rich',
    'creamy',
    'casserole',
    'pot_roast',
    'mulled',
    'cinnamon',
    'nutmeg',
    'ginger',
    'pumpkin',
    'squash',
    'root_vegetables',
  ];

  // Spring-appropriate food indicators
  const springIndicators = [
    'fresh',
    'light',
    'green',
    'asparagus',
    'peas',
    'artichoke',
    'spring_onion',
    'herbs',
    'lemony',
    'bright',
    'crisp',
    'tender',
    'new_potatoes',
  ];

  // Fall-appropriate food indicators
  const fallIndicators = [
    'harvest',
    'warming',
    'spiced',
    'apple',
    'pear',
    'pumpkin',
    'squash',
    'sweet_potato',
    'brussels_sprouts',
    'cranberry',
    'cinnamon',
    'nutmeg',
    'maple',
    'roasted',
    'baked',
  ];

  const hasAnyTag = (indicators: string[]) =>
    indicators.some((indicator) => tags.some((tag) => tag.includes(indicator)));

  switch (targetSeason) {
    case 'summer':
      // Exclude obvious winter foods, include summer foods
      if (hasAnyTag(winterIndicators)) return false;
      return (
        hasAnyTag(summerIndicators) ||
        !hasAnyTag([...winterIndicators, ...fallIndicators])
      );

    case 'winter':
      // Exclude obvious summer foods, include winter foods
      if (hasAnyTag(summerIndicators)) return false;
      return (
        hasAnyTag(winterIndicators) ||
        !hasAnyTag([...summerIndicators, ...springIndicators])
      );

    case 'spring':
      // Include spring foods, allow light foods, exclude heavy winter foods
      if (
        hasAnyTag(
          winterIndicators.filter((i) =>
            ['rich', 'heavy', 'hearty'].includes(i)
          )
        )
      )
        return false;
      return (
        hasAnyTag(springIndicators) || hasAnyTag(['fresh', 'light', 'green'])
      );

    case 'fall':
      // Include fall foods, allow warming foods, exclude summer-only foods
      if (hasAnyTag(['gazpacho', 'ice', 'frozen', 'chilled'])) return false;
      return (
        hasAnyTag(fallIndicators) || hasAnyTag(['warming', 'spiced', 'roasted'])
      );

    default:
      return true;
  }
}

/**
 * Gets seasonal emoji indicator for UI display
 */
export function getSeasonalEmoji(season: Season): string {
  switch (season) {
    case 'spring':
      return '🌸';
    case 'summer':
      return '🌞';
    case 'fall':
      return '🍂';
    case 'winter':
      return '❄️';
    default:
      return '';
  }
}

/**
 * Gets user-friendly seasonal description
 */
export function getSeasonalDescription(preference: SeasonalPreference): string {
  switch (preference) {
    case 'automatic':
      return 'Seasonal (Automatic)';
    case 'spring_foods':
      return 'Spring Foods';
    case 'summer_foods':
      return 'Summer Foods';
    case 'fall_foods':
      return 'Fall Foods';
    case 'winter_foods':
      return 'Winter Foods';
    case 'no_preference':
      return 'Year-Round';
    default:
      return 'Year-Round';
  }
}
