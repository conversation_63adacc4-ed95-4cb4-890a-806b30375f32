import { supabase } from './supabase';
import type { Database } from './database.types';

type UserPreferences = Database['public']['Tables']['user_preferences']['Row'];
type UserPreferencesInsert =
  Database['public']['Tables']['user_preferences']['Insert'];
type UserPreferencesUpdate =
  Database['public']['Tables']['user_preferences']['Update'];

export interface OnboardingData {
  dietary_restrictions: string[];
  allergies: string[];
  cuisine_preferences: string[];
  household_size: number;
  cooking_skill_level: string;
  available_cooking_time: string;
  kitchen_equipment: string[];
  seasonal_preference: string[];
}

export async function saveUserPreferences(
  preferences: OnboardingData
): Promise<UserPreferences | null> {
  try {
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      throw new Error('No authenticated user');
    }

    // Check if user already has preferences
    const { data: existing } = await supabase
      .from('user_preferences')
      .select('*')
      .eq('user_id', user.id)
      .single();

    let result;

    if (existing) {
      // Update existing preferences
      const { data, error } = await supabase
        .from('user_preferences')
        .update({
          ...preferences,
          updated_at: new Date().toISOString(),
        })
        .eq('user_id', user.id)
        .select()
        .single();

      if (error) throw error;
      result = data;
    } else {
      // Create new preferences with default subscription values
      const { data, error } = await supabase
        .from('user_preferences')
        .insert({
          user_id: user.id,
          ...preferences,
          subscription_status: 'inactive',
          subscription_tier: 'free',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (error) throw error;
      result = data;
    }

    return result;
  } catch (error) {
    console.error('Error saving user preferences:', error);
    return null;
  }
}

export async function getUserPreferences(): Promise<UserPreferences | null> {
  try {
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      return null;
    }

    const { data, error } = await supabase
      .from('user_preferences')
      .select('*')
      .eq('user_id', user.id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        // No preferences found, return null
        return null;
      }
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error fetching user preferences:', error);
    return null;
  }
}

export async function updateUserPreferences(
  updates: Partial<UserPreferencesUpdate>
): Promise<UserPreferences | null> {
  try {
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      throw new Error('No authenticated user');
    }

    const { data, error } = await supabase
      .from('user_preferences')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('user_id', user.id)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error updating user preferences:', error);
    return null;
  }
}

// Helper function to map onboarding form values to database values
export function mapOnboardingToPreferences(onboardingData: {
  dietary: string[];
  allergies: string[];
  cuisinePreferences: string[];
  householdSize: number;
  cookingTime: string;
  skillLevel: string;
  kitchenEquipment: string[];
  seasonalPreference?: string | string[];
}): OnboardingData {
  return {
    dietary_restrictions: onboardingData.dietary,
    allergies: onboardingData.allergies,
    cuisine_preferences: onboardingData.cuisinePreferences,
    household_size: onboardingData.householdSize,
    cooking_skill_level: onboardingData.skillLevel.toLowerCase(),
    available_cooking_time: mapCookingTime(onboardingData.cookingTime),
    kitchen_equipment: onboardingData.kitchenEquipment,
    seasonal_preference: Array.isArray(onboardingData.seasonalPreference) 
      ? onboardingData.seasonalPreference 
      : onboardingData.seasonalPreference 
        ? [onboardingData.seasonalPreference] 
        : ['automatic'],
  };
}

// Helper function to map UI cooking time values to database values
function mapCookingTime(uiValue: string): string {
  const mapping: { [key: string]: string } = {
    '15 min': 'short',
    '30 min': 'medium',
    '45 min': 'long',
    '1+ hour': 'extended',
    'No preference': 'any',
  };
  return mapping[uiValue] || 'medium';
}


// Dietary restrictions options based on actual meal dietary_tags in database
export const DIETARY_RESTRICTIONS_OPTIONS = [
  'VEGETARIAN',
  'VEGAN', 
  'GLUTEN_FREE',
  'DAIRY_FREE',
  'KETO_FRIENDLY',
  'PALEO',
  'KOSHER',
  'PESCATARIAN',
  'MEDITERRANEAN',
  'SUGAR_CONSCIOUS',
  'NO_SUGAR_ADDED',
  'DASH',
  'FODMAP_FREE'
];

// Allergen-free tags based on actual meal dietary_tags in database
export const ALLERGEN_FREE_OPTIONS = [
  'PEANUT_FREE',
  'TREE_NUT_FREE', 
  'SHELLFISH_FREE',
  'FISH_FREE',
  'EGG_FREE',
  'DAIRY_FREE', // Also used for allergies
  'SOY_FREE',
  'WHEAT_FREE',
  'SESAME_FREE',
  'CELERY_FREE',
  'MUSTARD_FREE',
  'LUPINE_FREE',
  'MOLLUSK_FREE',
  'CRUSTACEAN_FREE'
];

// Cuisine options based on actual cuisine_type values in database (cleaned)
export const CUISINE_OPTIONS = [
  'american',
  'italian', 
  'mexican',
  'mediterranean',
  'french',
  'chinese',
  'south american',
  'asian',
  'japanese',
  'british',
  'south east asian',
  'nordic',
  'middle eastern',
  'indian',
  'central europe',
  'caribbean',
  'eastern europe',
  'greek',
  'korean'
];

// Meal tags for filtering (from meal_tags column)
export const MEAL_TAG_OPTIONS = [
  'LOW_CARB',
  'HIGH_FIBER',
  'BALANCED',
  'HIGH_PROTEIN',
  'LOW_FAT',
  'LOW_SODIUM'
];
