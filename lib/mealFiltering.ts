import type { Database } from './database.types';
import { supabase } from './supabase';
import {
  getTargetSeasonsFromPreferences,
  isSeasonallyCompatibleWithMultiple,
} from './seasonalFiltering';

type Meal = Database['public']['Tables']['meals']['Row'];
type UserMeal = Database['public']['Tables']['user_meals']['Row'];
type UserPreferences = Database['public']['Tables']['user_preferences']['Row'];

export interface MealFilterOptions {
  excludeMealIds?: string[];
  includeUserRecipes?: boolean;
  course?: string;
  maxResults?: number;
}

/**
 * Intelligently filters meals based on user preferences
 * Returns meals that match the user's dietary needs, allergen restrictions,
 * equipment availability, skill level, budget, and taste preferences
 * Now includes user-created recipes from the user_meals table
 */
export async function getFilteredMealsForUser(
  userPreferences: UserPreferences,
  options: MealFilterOptions = {}
): Promise<Meal[]> {
  const {
    excludeMealIds = [],
    includeUserRecipes = true,
    course,
    maxResults = 50, // Final result limit only
  } = options;

  try {
    // Get meals from both the main meals table and user_meals table
    const allMeals: Meal[] = [];

    // 1. Get meals from the main meals table with optimized query
    let mealsQuery = supabase
      .from('meals')
      .select(
        `
        id, name, prepTimeHour, prepTimeMin, cookTimeHour, cookTimeMin,
        servingSize, image, course, cuisine_type, prep_method,
        dietary_tags, allergen_contains, protein_type, required_equipment,
        meal_tags, calories, protein, carbs, fats
      `
      )
      .eq('status', 'approved'); // Only get approved meals

    // Exclude specific meal IDs (e.g., recently used meals)
    if (excludeMealIds.length > 0) {
      mealsQuery = mealsQuery.not('id', 'in', `(${excludeMealIds.join(',')})`);
    }

    // Filter by course if specified
    if (course) {
      mealsQuery = mealsQuery.eq('course', course);
    }

    // Filter by user recipe preference for main meals table
    if (!includeUserRecipes) {
      mealsQuery = mealsQuery.eq('isUserRecipe', false);
    }

    const { data: mainMeals, error: mainMealsError } = await mealsQuery;

    if (mainMealsError) {
      console.error('Error fetching main meals:', mainMealsError);
    }
    
    if (!mainMealsError && mainMeals) {
      allMeals.push(...mainMeals);
    }

    // 2. Get user-created recipes if includeUserRecipes is true
    if (includeUserRecipes) {
      let userMealsQuery = supabase
        .from('user_meals')
        .select('*')
        .eq('user_id', userPreferences.user_id); // Only get recipes from this user

      // Exclude specific meal IDs from user recipes too
      if (excludeMealIds.length > 0) {
        userMealsQuery = userMealsQuery.not(
          'id',
          'in',
          `(${excludeMealIds.join(',')})`
        );
      }

      // Filter by course if specified
      if (course) {
        userMealsQuery = userMealsQuery.eq('course', course);
      }

      const { data: userMeals, error: userMealsError } = await userMealsQuery;

      if (userMealsError) {
        console.error('Error fetching user meals:', userMealsError);
      }
      
      if (!userMealsError && userMeals) {
        // Convert user_meals to the same format as main meals
        const convertedUserMeals: Meal[] = userMeals.map(
          (userMeal: UserMeal) => {
            const convertedMeal: Meal = {
              // Copy common fields
              id: userMeal.id,
              name: userMeal.name,
              allergen_contains: userMeal.allergen_contains,
              calories: userMeal.calories,
              carbs: userMeal.carbs,
              course: userMeal.course,
              cuisine_type: userMeal.cuisine_type,
              description: userMeal.description,
              dietary_tags: userMeal.dietary_tags,
              fats: userMeal.fats,
              image: userMeal.image,
              ingredients: userMeal.ingredients,
              instructions: userMeal.instructions,
              protein: userMeal.protein,
              // Map user_meals field names to main meals field names
              cookTimeHour: userMeal.cook_time_hour,
              cookTimeMin: userMeal.cook_time_min,
              prepTimeHour: userMeal.prep_time_hour,
              prepTimeMin: userMeal.prep_time_min,
              servingSize: userMeal.serving_size,
              // Mark as user recipe for identification
              isUserRecipe: true,
              createdById: userMeal.user_id,
              createdAt: userMeal.created_at,
              updatedAt: userMeal.updated_at,
              // Set defaults for fields that don't exist in user_meals
              author: null,
              ingredient_sections: null,
              lastSelected: null,
              lastUsedDate: null,
              meal_tags: null,
              order: null,
              prep_method: null,
              protein_type: null,
              required_equipment: null,
              source_url: null,
              status: null
            };
            return convertedMeal;
          }
        );

        allMeals.push(...convertedUserMeals);
      }
    }

    // Apply smart filtering based on user preferences
    const filteredMeals = allMeals.filter((meal) => {
      const isCompatible = isCompatibleWithUserPreferences(
        meal,
        userPreferences
      );
      return isCompatible;
    });

    // Shuffle the results to ensure random distribution of user recipes among main meals
    const shuffledMeals = [...filteredMeals].sort(() => Math.random() - 0.5);

    // Return up to maxResults meals
    return shuffledMeals.slice(0, maxResults);
  } catch (error) {
    console.error('Error in getFilteredMealsForUser:', error);
    return [];
  }
}

/**
 * Checks if a meal is compatible with only critical safety preferences (allergens and dietary restrictions)
 * This is a more lenient filter for when strict filtering returns too few results
 */
function isCriticallyCompatible(
  meal: Meal,
  preferences: UserPreferences
): boolean {
  // 1. ALLERGEN SAFETY CHECK (Critical - must exclude if contains allergens)
  if (preferences.allergies && preferences.allergies.length > 0) {
    const userAllergies = preferences.allergies.filter(
      (a) => a.toLowerCase() !== 'none'
    );
    if (userAllergies.length > 0 && meal.allergen_contains) {
      const mealAllergens = meal.allergen_contains.map((a) => a.toLowerCase());
      const hasAllergen = userAllergies.some((allergen) =>
        mealAllergens.includes(allergen.toLowerCase())
      );
      if (hasAllergen) {
        return false; // EXCLUDE - contains user allergens
      }
    }
  }

  // 2. DIETARY RESTRICTIONS CHECK (Critical - must match dietary needs)
  if (
    preferences.dietary_restrictions &&
    preferences.dietary_restrictions.length > 0
  ) {
    const userDietaryNeeds = preferences.dietary_restrictions.filter(
      (d) => d.toLowerCase() !== 'no restrictions'
    );

    if (userDietaryNeeds.length > 0 && meal.dietary_tags) {
      const mealDietaryTags = meal.dietary_tags.map((t) => t.toLowerCase());

      // Check if meal satisfies user's dietary restrictions
      const meetsAllDietaryNeeds = userDietaryNeeds.every((need) => {
        const needLower = need.toLowerCase().replace('-', '_');
        return mealDietaryTags.some(
          (tag) => tag.includes(needLower) || needLower.includes(tag)
        );
      });

      if (!meetsAllDietaryNeeds) {
        return false; // EXCLUDE - doesn't meet dietary restrictions
      }
    }
  }

  // 3. SERVING SIZE CHECK (Critical for usability - don't serve meals for 20 people to family of 4)
  if (preferences.household_size && meal.servingSize) {
    if (
      !isServingSizeCompatible(preferences.household_size, meal.servingSize)
    ) {
      return false; // EXCLUDE - serving size doesn't match household needs
    }
  }

  return true; // INCLUDE - meets critical safety requirements
}

/**
 * Checks if a meal is compatible with user preferences
 */
function isCompatibleWithUserPreferences(
  meal: Meal,
  preferences: UserPreferences
): boolean {
  // 1. ALLERGEN SAFETY CHECK (Critical - must exclude if contains allergens)
  if (preferences.allergies && preferences.allergies.length > 0) {
    const userAllergies = preferences.allergies.filter(
      (a) => a.toLowerCase() !== 'none'
    );
    if (userAllergies.length > 0 && meal.allergen_contains) {
      const mealAllergens = meal.allergen_contains.map((a) => a.toLowerCase());
      const hasAllergen = userAllergies.some((allergen) =>
        mealAllergens.includes(allergen.toLowerCase())
      );
      if (hasAllergen) {
        return false; // EXCLUDE - contains user allergens
      }
    }
  }

  // 2. DIETARY RESTRICTIONS CHECK (Critical - must match dietary needs)
  if (
    preferences.dietary_restrictions &&
    preferences.dietary_restrictions.length > 0
  ) {
    const userDietaryNeeds = preferences.dietary_restrictions.filter(
      (d) => d.toLowerCase() !== 'no restrictions'
    );

    if (userDietaryNeeds.length > 0 && meal.dietary_tags) {
      const mealDietaryTags = meal.dietary_tags.map((t) => t.toLowerCase());

      // Check if meal satisfies user's dietary restrictions
      const meetsAllDietaryNeeds = userDietaryNeeds.every((need) => {
        const needLower = need.toLowerCase().replace('-', '_');
        return mealDietaryTags.some(
          (tag) => tag.includes(needLower) || needLower.includes(tag)
        );
      });

      if (!meetsAllDietaryNeeds) {
        return false; // EXCLUDE - doesn't meet dietary restrictions
      }
    }
  }

  // 3. SPICE TOLERANCE CHECK (removed - column no longer exists)

  // Note: Difficulty level filtering has been removed

  // 5. KITCHEN EQUIPMENT CHECK
  if (meal.required_equipment && meal.required_equipment.length > 0) {
    const userEquipment = preferences.kitchen_equipment || [];
    const hasRequiredEquipment = meal.required_equipment.every(
      (equipment) =>
        userEquipment.includes(equipment) || isBasicEquipment(equipment)
    );

    if (!hasRequiredEquipment) {
      return false; // EXCLUDE - user doesn't have required equipment
    }
  }

  // 6. BUDGET CHECK - Disabled (no cost data in schema)
  // Budget filtering skipped - estimated_cost_per_serving not available

  // 7. COOKING TIME CHECK
  if (
    preferences.available_cooking_time &&
    meal.cookTimeMin &&
    meal.prepTimeMin
  ) {
    const totalTime =
      (meal.cookTimeMin || 0) +
      (meal.prepTimeMin || 0) +
      ((meal.cookTimeHour || 0) + (meal.prepTimeHour || 0)) * 60;

    if (!isTimeCompatible(preferences.available_cooking_time, totalTime)) {
      return false; // EXCLUDE - takes too long for user's available time
    }
  }

  // 8. SERVING SIZE CHECK (Household Size Compatibility)
  if (preferences.household_size && meal.servingSize) {
    if (
      !isServingSizeCompatible(preferences.household_size, meal.servingSize)
    ) {
      return false; // EXCLUDE - serving size doesn't match household needs
    }
  }

  // 9. SEASONAL COMPATIBILITY CHECK
  if (preferences.seasonal_preference && preferences.seasonal_preference.length > 0) {
    // Assume Northern Hemisphere by default (could be enhanced with user location data)
    const targetSeasons = getTargetSeasonsFromPreferences(
      preferences.seasonal_preference as import('./seasonalFiltering').SeasonalPreferences,
      true
    );

    if (!isSeasonallyCompatibleWithMultiple(meal.meal_tags, targetSeasons)) {
      return false; // EXCLUDE - not seasonally appropriate (e.g., soup in summer)
    }
  }

  return true; // INCLUDE - compatible with all user preferences
}

/**
 * Checks if equipment is considered basic (assumed to be available)
 */
function isBasicEquipment(equipment: string): boolean {
  const basicEquipment = [
    'oven',
    'stovetop',
    'microwave',
    'basic_utensils',
    'cutting_board',
    'knife',
    'pot',
    'pan',
    'baking_sheet',
  ];
  return basicEquipment.includes(equipment.toLowerCase());
}

/**
 * Checks if meal cost is compatible with user budget
 */
function isBudgetCompatible(userBudget: string, mealCost: number): boolean {
  const budgetLimits = {
    budget_friendly: 8.0,
    moderate: 15.0,
    premium: 25.0,
    no_preference: 100.0,
  };

  const limit = budgetLimits[userBudget as keyof typeof budgetLimits] || 15.0;
  return mealCost <= limit;
}

/**
 * Checks if cooking time is compatible with user's available time
 */
function isTimeCompatible(
  userTimePreference: string,
  totalMinutes: number
): boolean {
  const timeLimits = {
    short: 30, // 15 min preference -> allow up to 30 min
    medium: 45, // 30 min preference -> allow up to 45 min
    long: 60, // 45 min preference -> allow up to 60 min
    extended: 120, // 1+ hour preference -> allow up to 2 hours
    any: 300, // No preference -> allow up to 5 hours
  };

  const limit = timeLimits[userTimePreference as keyof typeof timeLimits] || 60;
  return totalMinutes <= limit;
}

/**
 * Checks if meal serving size is compatible with household size
 * Allows meals that serve within a reasonable range of the household size
 */
function isServingSizeCompatible(
  householdSize: number,
  mealServingSize: number
): boolean {
  // Allow meals that serve within 50% below to 100% above household size
  // Example: For household of 4, allow meals serving 2-8 people
  const minAcceptable = Math.max(1, Math.floor(householdSize * 0.5));
  const maxAcceptable = householdSize * 2;

  return mealServingSize >= minAcceptable && mealServingSize <= maxAcceptable;
}

/**
 * Sorts meals by relevance to user preferences
 * Meals that match more preferences will be ranked higher
 */
function sortMealsByRelevance(
  meals: Meal[],
  preferences: UserPreferences
): Meal[] {
  return meals.sort((a, b) => {
    const scoreA = calculateRelevanceScore(a, preferences);
    const scoreB = calculateRelevanceScore(b, preferences);
    return scoreB - scoreA; // Higher score first
  });
}

/**
 * Calculates a relevance score for a meal based on user preferences
 */
function calculateRelevanceScore(
  meal: Meal,
  preferences: UserPreferences
): number {
  let score = 0;

  // Cuisine preference match (high weight)
  if (
    preferences.cuisine_preferences &&
    preferences.cuisine_preferences.length > 0 &&
    meal.cuisine_type
  ) {
    const userCuisines = preferences.cuisine_preferences.map((c) =>
      c.toLowerCase()
    );
    if (userCuisines.includes(meal.cuisine_type.toLowerCase())) {
      score += 20;
    }
  }

  // Exact spice level match (removed - column no longer exists)

  // Note: Skill level scoring has been removed

  // Budget sweet spot (disabled - no cost data in schema)
  // Budget scoring skipped - estimated_cost_per_serving not available

  // Time preference match
  if (
    preferences.available_cooking_time &&
    meal.cookTimeMin &&
    meal.prepTimeMin
  ) {
    const totalTime =
      (meal.cookTimeMin || 0) +
      (meal.prepTimeMin || 0) +
      ((meal.cookTimeHour || 0) + (meal.prepTimeHour || 0)) * 60;

    const timeScores = {
      short: totalTime <= 20 ? 10 : totalTime <= 30 ? 5 : 0,
      medium: totalTime <= 35 ? 10 : totalTime <= 45 ? 5 : 0,
      long: totalTime <= 50 ? 10 : totalTime <= 60 ? 5 : 0,
      extended: totalTime >= 60 ? 10 : 5,
      any: 5,
    };

    score +=
      timeScores[
        preferences.available_cooking_time as keyof typeof timeScores
      ] || 5;
  }

  // Kitchen equipment utilization (bonus for using user's special equipment)
  if (preferences.kitchen_equipment && meal.required_equipment) {
    const userSpecialEquipment = preferences.kitchen_equipment;
    const mealUsesSpecialEquipment = meal.required_equipment.some((eq) =>
      userSpecialEquipment.includes(eq)
    );
    if (mealUsesSpecialEquipment) {
      score += 8;
    }
  }

  // Serving size preference (prefer meals closer to household size)
  if (preferences.household_size && meal.servingSize) {
    const sizeDifference = Math.abs(
      meal.servingSize - preferences.household_size
    );
    if (sizeDifference === 0) {
      score += 15; // Perfect match
    } else if (sizeDifference <= 1) {
      score += 10; // Very close
    } else if (sizeDifference <= 2) {
      score += 5; // Acceptable
    }
    // No bonus for larger differences
  }

  return score;
}

/**
 * Gets meal suggestions for swapping based on user preferences
 * Excludes the current meal and recently used meals
 */
export async function getMealSwapSuggestions(
  currentMealId: string,
  userPreferences: UserPreferences,
  recentMealIds: string[] = [],
  course?: string
): Promise<Meal[]> {
  const excludeIds = [currentMealId, ...recentMealIds];

  // Try with smart filtering on the full database
  let results = await getFilteredMealsForUser(userPreferences, {
    excludeMealIds: excludeIds,
    course,
    maxResults: 1000, // Return all compatible meals (up to 1000)
  });

  // If we have less than 20 results, try with more lenient filtering on full database
  if (results.length < 20) {
    try {
      const fallbackMeals: Meal[] = [];

      // Get ALL meals (no limit) and apply only critical filtering
      let mealsQuery = supabase
        .from('meals')
        .select('*')
        .not('id', 'in', `(${excludeIds.join(',')})`);

      // Apply course filtering if specified
      if (course) {
        mealsQuery = mealsQuery.eq('course', course);
      }

      const { data: allMeals, error } = await mealsQuery;

      if (!error && allMeals) {
        // Apply only critical filters (allergens and dietary restrictions)
        const criticallyFiltered = allMeals.filter((meal) =>
          isCriticallyCompatible(meal, userPreferences)
        );
        fallbackMeals.push(...criticallyFiltered);
      }

      // Also get user recipes for fallback
      let userMealsQuery = supabase
        .from('user_meals')
        .select('*')
        .eq('user_id', userPreferences.user_id)
        .not('id', 'in', `(${excludeIds.join(',')})`);

      // Apply course filtering if specified
      if (course) {
        userMealsQuery = userMealsQuery.eq('course', course);
      }

      const { data: userMeals, error: userMealsError } = await userMealsQuery;

      if (!userMealsError && userMeals) {
        // Convert user_meals to the same format as main meals
        const convertedUserMeals: Meal[] = userMeals.map(
          (userMeal: UserMeal) => {
            const convertedMeal: Meal = {
              // Copy common fields
              id: userMeal.id,
              name: userMeal.name,
              allergen_contains: userMeal.allergen_contains,
              calories: userMeal.calories,
              carbs: userMeal.carbs,
              course: userMeal.course,
              cuisine_type: userMeal.cuisine_type,
              description: userMeal.description,
              dietary_tags: userMeal.dietary_tags,
              fats: userMeal.fats,
              image: userMeal.image,
              ingredients: userMeal.ingredients,
              instructions: userMeal.instructions,
              protein: userMeal.protein,
              // Map user_meals field names to main meals field names
              cookTimeHour: userMeal.cook_time_hour,
              cookTimeMin: userMeal.cook_time_min,
              prepTimeHour: userMeal.prep_time_hour,
              prepTimeMin: userMeal.prep_time_min,
              servingSize: userMeal.serving_size,
              // Mark as user recipe for identification
              isUserRecipe: true,
              createdById: userMeal.user_id,
              createdAt: userMeal.created_at,
              updatedAt: userMeal.updated_at,
              // Set defaults for fields that don't exist in user_meals
              author: null,
              ingredient_sections: null,
              lastSelected: null,
              lastUsedDate: null,
              meal_tags: null,
              order: null,
              prep_method: null,
              protein_type: null,
              required_equipment: null,
              source_url: null,
              status: null
            };
            return convertedMeal;
          }
        );

        // Apply critical filtering to user recipes too
        const criticallyFilteredUserMeals = convertedUserMeals.filter((meal) =>
          isCriticallyCompatible(meal, userPreferences)
        );
        fallbackMeals.push(...criticallyFilteredUserMeals);
      }

      // If we have more results now, use them
      if (fallbackMeals.length > results.length) {
        // Return all critically filtered meals (no artificial limit)
        results = fallbackMeals;
      }
    } catch (error) {
      console.error('Error in lenient filtering:', error);
    }
  }

  return results;
}

/**
 * Gets personalized meal recommendations for meal planning
 */
export async function getPersonalizedMealRecommendations(
  userPreferences: UserPreferences,
  numberOfMeals: number = 7,
  excludeMealIds: string[] = [],
  course?: string
): Promise<Meal[]> {
  const meals = await getFilteredMealsForUser(userPreferences, {
    excludeMealIds,
    course,
    maxResults: numberOfMeals * 3, // Get more options to ensure variety
  });

  // Ensure variety by limiting same cuisine type
  const varietyMeals = ensureCuisineVariety(meals, numberOfMeals);

  return varietyMeals.slice(0, numberOfMeals);
}

/**
 * Ensures variety in cuisine types when selecting meals
 */
function ensureCuisineVariety(meals: Meal[], targetCount: number): Meal[] {
  const selected: Meal[] = [];
  const selectedIds = new Set<string>(); // Track IDs to prevent duplicates
  const cuisineCount: { [key: string]: number } = {};
  const maxPerCuisine = Math.max(1, Math.floor(targetCount / 4)); // Allow max 25% from same cuisine

  // FIRST: Shuffle the input meals to ensure randomness
  const shuffledMeals = [...meals].sort(() => Math.random() - 0.5);

  for (const meal of shuffledMeals) {
    if (selected.length >= targetCount) break;

    // Skip if already selected (prevent duplicates)
    if (selectedIds.has(meal.id)) continue;

    const cuisine = meal.cuisine_type || 'unknown';
    const currentCount = cuisineCount[cuisine] || 0;

    if (currentCount < maxPerCuisine) {
      selected.push(meal);
      selectedIds.add(meal.id);
      cuisineCount[cuisine] = currentCount + 1;
    }
  }

  // If we still need more meals, add remaining without cuisine restriction
  if (selected.length < targetCount) {
    for (const meal of shuffledMeals) {
      if (selected.length >= targetCount) break;
      if (!selectedIds.has(meal.id)) {
        selected.push(meal);
        selectedIds.add(meal.id);
      }
    }
  }

  return selected;
}

/**
 * Unified function to fetch meals by IDs from both meals and user_meals tables
 * Converts user_meals to the same format as main meals for consistent handling
 */
export async function getMealsByIds(
  mealIds: string[],
  userId?: string,
  selectFields?: string
): Promise<Meal[]> {
  if (mealIds.length === 0) return [];

  const allMeals: Meal[] = [];
  const defaultFields =
    'id, name, "prepTimeHour", "prepTimeMin", "cookTimeHour", "cookTimeMin", "servingSize", image, calories, protein, carbs, fats, course, cuisine_type, prep_method, ingredients, instructions, allergen_contains, dietary_tags, description';
  const fields = selectFields || defaultFields;

  try {
    // 1. Fetch from main meals table
    const { data: mainMeals, error: mainMealsError } = await supabase
      .from('meals')
      .select(fields)
      .in('id', mealIds);

    if (mainMealsError) {
      console.error('Error fetching main meals:', mainMealsError);
    }
    
    // Only process main meals if there's no error and data exists
    if (!mainMealsError && mainMeals) {
      allMeals.push(...mainMeals);
    }

    // 2. Fetch from user_meals table if userId is provided
    if (userId) {
      // Check which meal IDs are not found in main meals
      const validMainMeals = (!mainMealsError && mainMeals) ? mainMeals : [];
      const foundMainMealIds = new Set(validMainMeals.map((m) => m.id));
      const missingMealIds = mealIds.filter((id) => !foundMainMealIds.has(id));

      if (missingMealIds.length > 0) {
        // Convert select fields for user_meals table (different field names)
        // Remove fields that don't exist in user_meals table
        const userMealFields = fields
          .replace('"prepTimeHour"', 'prep_time_hour')
          .replace('"prepTimeMin"', 'prep_time_min')
          .replace('"cookTimeHour"', 'cook_time_hour')
          .replace('"cookTimeMin"', 'cook_time_min')
          .replace('"servingSize"', 'serving_size')
          .replace(', prep_method', '') // Remove prep_method as it doesn't exist in user_meals
          .replace('prep_method, ', '') // Remove prep_method if it's at the beginning
          .replace('prep_method', ''); // Remove prep_method if it's standalone

        const { data: userMeals, error: userMealsError } = await supabase
          .from('user_meals')
          .select(userMealFields)
          .eq('user_id', userId)
          .in('id', missingMealIds);

        if (userMealsError) {
          console.error('Error fetching user meals:', userMealsError);
        }
        
        if (!userMealsError && userMeals) {
          // Convert user_meals to the same format as main meals
          const convertedUserMeals: Meal[] = userMeals.map(
            (userMeal: UserMeal) => {
              const convertedMeal: Meal = {
                // Copy common fields
                id: userMeal.id,
                name: userMeal.name,
                allergen_contains: userMeal.allergen_contains,
                calories: userMeal.calories,
                carbs: userMeal.carbs,
                course: userMeal.course,
                cuisine_type: userMeal.cuisine_type,
                description: userMeal.description,
                dietary_tags: userMeal.dietary_tags,
                fats: userMeal.fats,
                image: userMeal.image,
                ingredients: userMeal.ingredients,
                instructions: userMeal.instructions,
                protein: userMeal.protein,
                // Map user_meals field names to main meals field names
                cookTimeHour: userMeal.cook_time_hour,
                cookTimeMin: userMeal.cook_time_min,
                prepTimeHour: userMeal.prep_time_hour,
                prepTimeMin: userMeal.prep_time_min,
                servingSize: userMeal.serving_size,
                // Mark as user recipe for identification
                isUserRecipe: true,
                createdById: userMeal.user_id,
                createdAt: userMeal.created_at,
                updatedAt: userMeal.updated_at,
                // Set defaults for fields that don't exist in user_meals
                author: null,
                ingredient_sections: null,
                lastSelected: null,
                lastUsedDate: null,
                meal_tags: null,
                order: null,
                prep_method: null,
                protein_type: null,
                required_equipment: null,
                source_url: null,
                status: null
              };
              return convertedMeal;
            }
          );

          allMeals.push(...convertedUserMeals);
        }
      }
    }

    return allMeals;
  } catch (error) {
    console.error('Error in getMealsByIds:', error);
    return [];
  }
}
