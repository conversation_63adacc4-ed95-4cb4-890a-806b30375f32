/**
 * Instacart API Proxy Client
 * Uses Supabase Edge Function to make secure Instacart API calls
 * Keeps API keys server-side and adds rate limiting
 */

import { supabase } from './supabase';

export interface InstacartItem {
  quantity: number;
  item: {
    name: string;
    size?: string;
    image_url?: string;
  };
}

export interface InstacartOrderRequest {
  items: InstacartItem[];
  affiliateData?: {
    affiliateCode: string;
    userId: string;
  };
}

export interface InstacartOrderResponse {
  id: string;
  url: string;
  items: InstacartItem[];
  partner_user_id: string;
}

export interface InstacartRetailer {
  id: string;
  name: string;
  logo_url?: string;
  available: boolean;
}

export class InstacartProxyError extends Error {
  constructor(
    message: string,
    public status?: number
  ) {
    super(message);
    this.name = 'InstacartProxyError';
  }
}

/**
 * Create an Instacart order through secure proxy
 */
export async function createInstacartOrder(
  orderRequest: InstacartOrderRequest
): Promise<InstacartOrderResponse> {
  try {
    // Get the current session for authentication
    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.getSession();

    if (sessionError || !session) {
      throw new InstacartProxyError('Authentication required', 401);
    }

    // Make request to our secure proxy
    const url = `${process.env.EXPO_PUBLIC_SUPABASE_URL}/functions/v1/proxy-instacart/create-order`;

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${session.access_token}`,
        'Content-Type': 'application/json',
        apikey: process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY!,
      },
      body: JSON.stringify(orderRequest),
    });

    if (!response.ok) {
      const errorText = await response.text();

      if (response.status === 429) {
        throw new InstacartProxyError(
          'Daily order limit exceeded. Please try again tomorrow.',
          429
        );
      }

      if (response.status === 401) {
        throw new InstacartProxyError(
          'Authentication failed. Please log in again.',
          401
        );
      }

      if (response.status === 503) {
        throw new InstacartProxyError(
          'Shopping service temporarily unavailable. Please try again later.',
          503
        );
      }

      throw new InstacartProxyError(
        errorText || 'Shopping service unavailable',
        response.status
      );
    }

    const data = await response.json();
    return data as InstacartOrderResponse;
  } catch (error) {
    if (error instanceof InstacartProxyError) {
      throw error;
    }

    throw new InstacartProxyError(`Failed to create shopping order: ${error}`);
  }
}

/**
 * Get available retailers through secure proxy
 */
export async function getInstacartRetailers(): Promise<InstacartRetailer[]> {
  try {
    // Get the current session for authentication
    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.getSession();

    if (sessionError || !session) {
      throw new InstacartProxyError('Authentication required', 401);
    }

    // Make request to our secure proxy
    const response = await fetch(
      `${process.env.EXPO_PUBLIC_SUPABASE_URL}/functions/v1/proxy-instacart/retailers`,
      {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${session.access_token}`,
          apikey: process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY!,
        },
      }
    );

    if (!response.ok) {
      const errorText = await response.text();

      if (response.status === 401) {
        throw new InstacartProxyError(
          'Authentication failed. Please log in again.',
          401
        );
      }

      throw new InstacartProxyError(
        errorText || 'Retailer service unavailable',
        response.status
      );
    }

    const data = await response.json();
    return data.retailers || [];
  } catch (error) {
    if (error instanceof InstacartProxyError) {
      throw error;
    }

    throw new InstacartProxyError(`Failed to get retailers: ${error}`);
  }
}
