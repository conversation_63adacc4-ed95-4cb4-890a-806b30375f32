// Global food emoji definitions
export interface FoodEmoji {
  emoji: string;
  name: string;
  category: string;
}

export const FOOD_EMOJIS: FoodEmoji[] = [
  // Vegetables
  { emoji: '🥗', name: 'Salad', category: 'Vegetables' },
  { emoji: '🍅', name: '<PERSON><PERSON>', category: 'Vegetables' },
  { emoji: '🥕', name: 'Carrot', category: 'Vegetables' },
  { emoji: '🌽', name: 'Corn', category: 'Vegetables' },
  { emoji: '🥒', name: '<PERSON><PERSON>um<PERSON>', category: 'Vegetables' },
  { emoji: '🥬', name: 'Leafy Greens', category: 'Vegetables' },
  { emoji: '🥔', name: 'Potato', category: 'Vegetables' },
  { emoji: '🍠', name: 'Sweet Potato', category: 'Vegetables' },
  { emoji: '🧄', name: 'Garlic', category: 'Vegetables' },
  { emoji: '🧅', name: 'Onion', category: 'Vegetables' },
  { emoji: '🫒', name: 'Olive', category: 'Vegetables' },
  { emoji: '🌶️', name: 'Hot Pepper', category: 'Vegetables' },
  { emoji: '🫑', name: 'Bell Pepper', category: 'Vegetables' },
  { emoji: '🥑', name: 'Avocado', category: 'Vegetables' },
  { emoji: '🍆', name: 'Eggplant', category: 'Vegetables' },
  { emoji: '🥦', name: 'Broccoli', category: 'Vegetables' },
  { emoji: '🍄', name: 'Mushroom', category: 'Vegetables' },

  // Fruits
  { emoji: '🍋', name: 'Lemon', category: 'Fruits' },
  { emoji: '🍊', name: 'Orange', category: 'Fruits' },
  { emoji: '🍎', name: 'Apple', category: 'Fruits' },
  { emoji: '🍌', name: 'Banana', category: 'Fruits' },
  { emoji: '🍓', name: 'Strawberry', category: 'Fruits' },
  { emoji: '🫐', name: 'Blueberries', category: 'Fruits' },
  { emoji: '🍇', name: 'Grapes', category: 'Fruits' },
  { emoji: '🍑', name: 'Peach', category: 'Fruits' },
  { emoji: '🍒', name: 'Cherry', category: 'Fruits' },
  { emoji: '🥭', name: 'Mango', category: 'Fruits' },
  { emoji: '🍍', name: 'Pineapple', category: 'Fruits' },
  { emoji: '🥝', name: 'Kiwi', category: 'Fruits' },
  { emoji: '🍈', name: 'Melon', category: 'Fruits' },
  { emoji: '🍉', name: 'Watermelon', category: 'Fruits' },

  // Nuts & Seeds
  { emoji: '🥜', name: 'Peanuts', category: 'Nuts & Seeds' },
  { emoji: '🌰', name: 'Chestnut', category: 'Nuts & Seeds' },
  { emoji: '🥥', name: 'Coconut', category: 'Nuts & Seeds' },

  // Dairy & Eggs
  { emoji: '🧈', name: 'Butter', category: 'Dairy & Eggs' },
  { emoji: '🥛', name: 'Milk', category: 'Dairy & Eggs' },
  { emoji: '🧀', name: 'Cheese', category: 'Dairy & Eggs' },
  { emoji: '🥚', name: 'Egg', category: 'Dairy & Eggs' },

  // Meat & Seafood
  { emoji: '🍖', name: 'Meat', category: 'Meat & Seafood' },
  { emoji: '🥩', name: 'Steak', category: 'Meat & Seafood' },
  { emoji: '🥓', name: 'Bacon', category: 'Meat & Seafood' },
  { emoji: '🍗', name: 'Chicken', category: 'Meat & Seafood' },
  { emoji: '🐟', name: 'Fish', category: 'Meat & Seafood' },
  { emoji: '🦐', name: 'Shrimp', category: 'Meat & Seafood' },
  { emoji: '🦞', name: 'Lobster', category: 'Meat & Seafood' },
  { emoji: '🦀', name: 'Crab', category: 'Meat & Seafood' },
  { emoji: '🐙', name: 'Octopus', category: 'Meat & Seafood' },
  { emoji: '🦑', name: 'Squid', category: 'Meat & Seafood' },

  // Grains & Carbs
  { emoji: '🍞', name: 'Bread', category: 'Grains & Carbs' },
  { emoji: '🥖', name: 'Baguette', category: 'Grains & Carbs' },
  { emoji: '🥨', name: 'Pretzel', category: 'Grains & Carbs' },
  { emoji: '🥯', name: 'Bagel', category: 'Grains & Carbs' },
  { emoji: '🧇', name: 'Waffle', category: 'Grains & Carbs' },
  { emoji: '🥞', name: 'Pancakes', category: 'Grains & Carbs' },
  { emoji: '🍝', name: 'Pasta', category: 'Grains & Carbs' },
  { emoji: '🍜', name: 'Noodles', category: 'Grains & Carbs' },
  { emoji: '🍲', name: 'Stew', category: 'Grains & Carbs' },
  { emoji: '🍱', name: 'Bento', category: 'Grains & Carbs' },
  { emoji: '🍘', name: 'Rice Cracker', category: 'Grains & Carbs' },
  { emoji: '🍙', name: 'Rice Ball', category: 'Grains & Carbs' },
  { emoji: '🍚', name: 'Rice', category: 'Grains & Carbs' },
  { emoji: '🍛', name: 'Curry', category: 'Grains & Carbs' },
  { emoji: '🫘', name: 'Beans', category: 'Grains & Carbs' },

  // Herbs & Seasonings
  { emoji: '🌿', name: 'Herbs', category: 'Herbs & Seasonings' },
  { emoji: '🍃', name: 'Leaves', category: 'Herbs & Seasonings' },
  { emoji: '🧂', name: 'Salt', category: 'Herbs & Seasonings' },
  { emoji: '🍯', name: 'Honey', category: 'Herbs & Seasonings' },

  // Beverages
  { emoji: '🥤', name: 'Cup with Straw', category: 'Beverages' },
  { emoji: '☕', name: 'Coffee', category: 'Beverages' },
  { emoji: '🫖', name: 'Teapot', category: 'Beverages' },
  { emoji: '🍷', name: 'Wine', category: 'Beverages' },
  { emoji: '🍺', name: 'Beer', category: 'Beverages' },
  { emoji: '🥃', name: 'Tumbler Glass', category: 'Beverages' },

  // Kitchen & Misc
  {
    emoji: '🍽️',
    name: 'Fork and Knife with Plate',
    category: 'Kitchen & Misc',
  },
  { emoji: '🥄', name: 'Spoon', category: 'Kitchen & Misc' },
  { emoji: '🔪', name: 'Kitchen Knife', category: 'Kitchen & Misc' },
  { emoji: '🫙', name: 'Jar', category: 'Kitchen & Misc' },
  { emoji: '🫗', name: 'Pouring Liquid', category: 'Kitchen & Misc' },
];

// Helper functions
export const getEmojisByCategory = () => {
  const categories: Record<string, FoodEmoji[]> = {};

  FOOD_EMOJIS.forEach((item) => {
    if (!categories[item.category]) {
      categories[item.category] = [];
    }
    categories[item.category].push(item);
  });

  return categories;
};

export const findEmojiByEmoji = (emoji: string): FoodEmoji | undefined => {
  return FOOD_EMOJIS.find((item) => item.emoji === emoji);
};

export const getRandomFoodEmoji = (): FoodEmoji => {
  return FOOD_EMOJIS[Math.floor(Math.random() * FOOD_EMOJIS.length)];
};
