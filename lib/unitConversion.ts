/**
 * Unit conversion utility for ingredient consolidation
 * Supports Instacart-compatible units and smart unit conversion
 */

export interface UnitConversionConfig {
  name: string;
  abbreviations: string[];
  type: 'volume' | 'weight' | 'count';
  baseUnit: string;
  baseMultiplier: number; // How many base units this equals
}

// Base units for each type
const BASE_UNITS = {
  volume: 'ml', // milliliters as base for volume
  weight: 'g',  // grams as base for weight
  count: 'each' // each as base for count
};

// Unit configurations with Instacart-compatible mappings
export const UNIT_CONFIGS: UnitConversionConfig[] = [
  // Volume units (base: ml)
  { name: 'teaspoon', abbreviations: ['tsp', 't', 'teaspoon', 'teaspoons'], type: 'volume', baseUnit: 'ml', baseMultiplier: 4.93 },
  { name: 'tablespoon', abbreviations: ['tbs', 'tbsp', 'T', 'tablespoon', 'tablespoons'], type: 'volume', baseUnit: 'ml', baseMultiplier: 14.79 },
  { name: 'fluid ounce', abbreviations: ['fl oz', 'fl. oz', 'fluid ounce', 'fluid ounces'], type: 'volume', baseUnit: 'ml', baseMultiplier: 29.57 },
  { name: 'cup', abbreviations: ['c', 'cup', 'cups'], type: 'volume', baseUnit: 'ml', baseMultiplier: 236.59 },
  { name: 'pint', abbreviations: ['pt', 'pint', 'pints'], type: 'volume', baseUnit: 'ml', baseMultiplier: 473.18 },
  { name: 'quart', abbreviations: ['qt', 'quart', 'quarts'], type: 'volume', baseUnit: 'ml', baseMultiplier: 946.35 },
  { name: 'gallon', abbreviations: ['gal', 'gallon', 'gallons'], type: 'volume', baseUnit: 'ml', baseMultiplier: 3785.41 },
  { name: 'milliliter', abbreviations: ['ml', 'milliliter', 'milliliters'], type: 'volume', baseUnit: 'ml', baseMultiplier: 1 },
  { name: 'liter', abbreviations: ['l', 'liter', 'liters'], type: 'volume', baseUnit: 'ml', baseMultiplier: 1000 },

  // Weight units (base: g)
  { name: 'gram', abbreviations: ['g', 'gram', 'grams'], type: 'weight', baseUnit: 'g', baseMultiplier: 1 },
  { name: 'kilogram', abbreviations: ['kg', 'kilogram', 'kilograms'], type: 'weight', baseUnit: 'g', baseMultiplier: 1000 },
  { name: 'ounce', abbreviations: ['oz', 'ounce', 'ounces'], type: 'weight', baseUnit: 'g', baseMultiplier: 28.35 },
  { name: 'pound', abbreviations: ['lb', 'lbs', 'pound', 'pounds'], type: 'weight', baseUnit: 'g', baseMultiplier: 453.59 },

  // Count units (base: each)
  { name: 'each', abbreviations: ['each', 'whole', 'item', 'items'], type: 'count', baseUnit: 'each', baseMultiplier: 1 },
  { name: 'bunch', abbreviations: ['bunch', 'bunches'], type: 'count', baseUnit: 'each', baseMultiplier: 1 },
  { name: 'head', abbreviations: ['head', 'heads'], type: 'count', baseUnit: 'each', baseMultiplier: 1 },
  { name: 'can', abbreviations: ['can', 'cans'], type: 'count', baseUnit: 'each', baseMultiplier: 1 },
  { name: 'package', abbreviations: ['pkg', 'package', 'packages', 'packet', 'packets'], type: 'count', baseUnit: 'each', baseMultiplier: 1 },
  { name: 'large', abbreviations: ['lg', 'large'], type: 'count', baseUnit: 'each', baseMultiplier: 1 },
  { name: 'medium', abbreviations: ['med', 'medium'], type: 'count', baseUnit: 'each', baseMultiplier: 1 },
  { name: 'small', abbreviations: ['sm', 'small'], type: 'count', baseUnit: 'each', baseMultiplier: 1 },
  { name: 'clove', abbreviations: ['clove', 'cloves'], type: 'count', baseUnit: 'each', baseMultiplier: 1 },
  { name: 'slice', abbreviations: ['slice', 'slices'], type: 'count', baseUnit: 'each', baseMultiplier: 1 },
  { name: 'piece', abbreviations: ['piece', 'pieces'], type: 'count', baseUnit: 'each', baseMultiplier: 1 },
  { name: 'stick', abbreviations: ['stick', 'sticks'], type: 'count', baseUnit: 'each', baseMultiplier: 1 },
  { name: 'ears', abbreviations: ['ear', 'ears'], type: 'count', baseUnit: 'each', baseMultiplier: 1 },
];

// Create lookup maps for fast access
const unitByAbbreviation = new Map<string, UnitConversionConfig>();
const unitByName = new Map<string, UnitConversionConfig>();

// Initialize lookup maps
UNIT_CONFIGS.forEach(config => {
  unitByName.set(config.name, config);
  config.abbreviations.forEach(abbr => {
    unitByAbbreviation.set(abbr.toLowerCase(), config);
  });
});

/**
 * Find unit configuration by string (case-insensitive)
 */
export function findUnitConfig(unitStr: string): UnitConversionConfig | null {
  const normalized = unitStr.toLowerCase().trim();
  return unitByAbbreviation.get(normalized) || null;
}

/**
 * Check if two units are compatible for conversion
 */
export function areUnitsCompatible(unit1: string, unit2: string): boolean {
  const config1 = findUnitConfig(unit1);
  const config2 = findUnitConfig(unit2);
  
  if (!config1 || !config2) return false;
  
  return config1.type === config2.type;
}

/**
 * Convert amount from one unit to another
 * Returns null if units are incompatible
 */
export function convertUnits(
  amount: number,
  fromUnit: string,
  toUnit: string
): { amount: number; unit: string } | null {
  const fromConfig = findUnitConfig(fromUnit);
  const toConfig = findUnitConfig(toUnit);
  
  if (!fromConfig || !toConfig || fromConfig.type !== toConfig.type) {
    return null;
  }
  
  // Convert to base units, then to target units
  const baseAmount = amount * fromConfig.baseMultiplier;
  const convertedAmount = baseAmount / toConfig.baseMultiplier;
  
  // Round to avoid floating point precision errors
  const roundedAmount = Math.round(convertedAmount * 10000) / 10000;
  
  return {
    amount: roundedAmount,
    unit: toConfig.name
  };
}

/**
 * Get the best display unit for a given amount and type
 * Prioritizes Instacart-friendly units and reasonable amounts
 */
export function getBestDisplayUnit(
  amount: number,
  unitType: 'volume' | 'weight' | 'count',
  baseAmount: number, // amount in base units (ml, g, each)
): string {
  if (unitType === 'count') {
    // For count items, always use 'each' unless it's a very large number
    return baseAmount > 20 ? 'package' : 'each';
  }
  
  if (unitType === 'volume') {
    // Volume unit preferences based on amount (more conservative thresholds)
    if (baseAmount >= 3785) return 'gallon';      // 1+ gallons
    if (baseAmount >= 946) return 'quart';        // 1+ quarts
    if (baseAmount >= 473) return 'pint';         // 1+ pints
    if (baseAmount >= 118) return 'cup';          // 0.5+ cups (more cups, less fl oz)
    if (baseAmount >= 44) return 'fl oz';         // 1.5+ fl oz
    if (baseAmount >= 10) return 'tablespoon';    // 2/3+ tablespoons
    return 'teaspoon';                            // Less than 2/3 tablespoons
  }
  
  if (unitType === 'weight') {
    // Weight unit preferences based on amount
    if (baseAmount >= 454) return 'pound';        // 1+ pounds
    if (baseAmount >= 28) return 'ounce';         // 1+ ounces
    if (baseAmount >= 1000) return 'kilogram';    // 1+ kg (metric preference)
    return 'gram';                                // Less than 1 ounce
  }
  
  return 'each'; // fallback
}

/**
 * Smart consolidation of two amounts with different units
 * Returns the consolidated amount in the best display unit
 */
export function consolidateAmounts(
  amount1: number,
  unit1: string,
  amount2: number,
  unit2: string
): { amount: number; unit: string; success: boolean } {
  const config1 = findUnitConfig(unit1);
  const config2 = findUnitConfig(unit2);
  
  // If units are incompatible, can't consolidate
  if (!config1 || !config2 || config1.type !== config2.type) {
    return { amount: amount1, unit: unit1, success: false };
  }
  
  // Convert both to base units and sum
  const baseAmount1 = amount1 * config1.baseMultiplier;
  const baseAmount2 = amount2 * config2.baseMultiplier;
  const totalBaseAmount = baseAmount1 + baseAmount2;
  
  // Get the best display unit for the total
  const bestUnit = getBestDisplayUnit(0, config1.type, totalBaseAmount);
  const bestConfig = findUnitConfig(bestUnit);
  
  if (!bestConfig) {
    return { amount: amount1, unit: unit1, success: false };
  }
  
  // Convert total back to best display unit
  const finalAmount = totalBaseAmount / bestConfig.baseMultiplier;
  
  // Round to avoid floating point precision errors
  const roundedFinalAmount = Math.round(finalAmount * 10000) / 10000;
  
  return {
    amount: roundedFinalAmount,
    unit: bestConfig.name,
    success: true
  };
}

/**
 * Format amount for display (handles fractions nicely)
 */
export function formatAmount(amount: number): string {
  // Handle whole numbers
  if (Math.abs(amount - Math.round(amount)) < 0.001) {
    return Math.round(amount).toString();
  }
  
  // Common decimal to fraction mappings
  const fractionMap: { [key: string]: string } = {
    '0.125': '1/8',
    '0.25': '1/4',
    '0.333': '1/3',
    '0.375': '3/8',
    '0.5': '1/2',
    '0.625': '5/8',
    '0.667': '2/3',
    '0.75': '3/4',
    '0.875': '7/8',
  };
  
  const whole = Math.floor(amount);
  const decimal = amount - whole;
  
  // Round decimal to 3 places for comparison
  const roundedDecimal = Math.round(decimal * 1000) / 1000;
  const decimalStr = roundedDecimal.toFixed(3);
  
  if (fractionMap[decimalStr]) {
    if (whole > 0) {
      return `${whole} ${fractionMap[decimalStr]}`;
    }
    return fractionMap[decimalStr];
  }
  
  // For other decimals, round to 2 places and clean up trailing zeros
  const rounded = Math.round(amount * 100) / 100; // Round to 2 decimal places
  return rounded.toString().replace(/\.00$/, '');
}

/**
 * Get Instacart-compatible unit abbreviation
 */
export function getInstacartUnit(unit: string): string {
  const config = findUnitConfig(unit);
  if (!config) return unit;
  
  // Return the most common/shortest abbreviation for Instacart
  const instacartPreferred: { [key: string]: string } = {
    'teaspoon': 'tsp',
    'tablespoon': 'tbs',
    'fluid ounce': 'fl oz',
    'cup': 'c',
    'pint': 'pt',
    'quart': 'qt',
    'gallon': 'gal',
    'milliliter': 'ml',
    'liter': 'l',
    'gram': 'g',
    'kilogram': 'kg',
    'ounce': 'oz',
    'pound': 'lb',
    'each': 'each',
    'bunch': 'bunch',
    'head': 'head',
    'can': 'can',
    'package': 'package',
    'large': 'large',
    'medium': 'medium',
    'small': 'small',
  };
  
  return instacartPreferred[config.name] || config.abbreviations[0];
}