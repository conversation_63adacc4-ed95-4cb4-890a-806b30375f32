export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instanciate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "12.2.3 (519615d)"
  }
  public: {
    Tables: {
      affiliate_payouts: {
        Row: {
          affiliate_id: string
          amount: number
          created_at: string
          failure_reason: string | null
          id: string
          paid_at: string | null
          payout_method: string | null
          period_end: string
          period_start: string
          referral_ids: string[]
          retry_count: number | null
          status: string
          stripe_connect_payout_id: string | null
          stripe_connect_transfer_id: string | null
          stripe_transfer_id: string | null
        }
        Insert: {
          affiliate_id: string
          amount: number
          created_at?: string
          failure_reason?: string | null
          id?: string
          paid_at?: string | null
          payout_method?: string | null
          period_end: string
          period_start: string
          referral_ids: string[]
          retry_count?: number | null
          status?: string
          stripe_connect_payout_id?: string | null
          stripe_connect_transfer_id?: string | null
          stripe_transfer_id?: string | null
        }
        Update: {
          affiliate_id?: string
          amount?: number
          created_at?: string
          failure_reason?: string | null
          id?: string
          paid_at?: string | null
          payout_method?: string | null
          period_end?: string
          period_start?: string
          referral_ids?: string[]
          retry_count?: number | null
          status?: string
          stripe_connect_payout_id?: string | null
          stripe_connect_transfer_id?: string | null
          stripe_transfer_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "affiliate_payouts_affiliate_id_fkey"
            columns: ["affiliate_id"]
            isOneToOne: false
            referencedRelation: "affiliates"
            referencedColumns: ["id"]
          },
        ]
      }
      affiliate_referrals: {
        Row: {
          affiliate_id: string
          commission_amount: number
          conversion_date: string
          created_at: string
          discount_amount: number | null
          id: string
          referred_user_id: string | null
          status: string
          stripe_coupon_id: string | null
          stripe_customer_id: string | null
          stripe_promotion_code_id: string | null
          stripe_subscription_id: string | null
          subscription_amount: number
        }
        Insert: {
          affiliate_id: string
          commission_amount: number
          conversion_date?: string
          created_at?: string
          discount_amount?: number | null
          id?: string
          referred_user_id?: string | null
          status?: string
          stripe_coupon_id?: string | null
          stripe_customer_id?: string | null
          stripe_promotion_code_id?: string | null
          stripe_subscription_id?: string | null
          subscription_amount: number
        }
        Update: {
          affiliate_id?: string
          commission_amount?: number
          conversion_date?: string
          created_at?: string
          discount_amount?: number | null
          id?: string
          referred_user_id?: string | null
          status?: string
          stripe_coupon_id?: string | null
          stripe_customer_id?: string | null
          stripe_promotion_code_id?: string | null
          stripe_subscription_id?: string | null
          subscription_amount?: number
        }
        Relationships: [
          {
            foreignKeyName: "affiliate_referrals_affiliate_id_fkey"
            columns: ["affiliate_id"]
            isOneToOne: false
            referencedRelation: "affiliates"
            referencedColumns: ["id"]
          },
        ]
      }
      affiliates: {
        Row: {
          affiliate_code: string
          commission_rate: number
          coupon_code: string | null
          created_at: string
          id: string
          minimum_payout_threshold: number | null
          payment_method_details: Json | null
          referral_link: string | null
          status: string
          stripe_connect_account_id: string | null
          stripe_connect_charges_enabled: boolean | null
          stripe_connect_created_at: string | null
          stripe_connect_details_submitted: boolean | null
          stripe_connect_onboard_url: string | null
          stripe_connect_payouts_enabled: boolean | null
          stripe_connect_status: string | null
          total_commission_earned: number
          total_referrals: number
          updated_at: string
          user_id: string
        }
        Insert: {
          affiliate_code: string
          commission_rate?: number
          coupon_code?: string | null
          created_at?: string
          id?: string
          minimum_payout_threshold?: number | null
          payment_method_details?: Json | null
          referral_link?: string | null
          status?: string
          stripe_connect_account_id?: string | null
          stripe_connect_charges_enabled?: boolean | null
          stripe_connect_created_at?: string | null
          stripe_connect_details_submitted?: boolean | null
          stripe_connect_onboard_url?: string | null
          stripe_connect_payouts_enabled?: boolean | null
          stripe_connect_status?: string | null
          total_commission_earned?: number
          total_referrals?: number
          updated_at?: string
          user_id: string
        }
        Update: {
          affiliate_code?: string
          commission_rate?: number
          coupon_code?: string | null
          created_at?: string
          id?: string
          minimum_payout_threshold?: number | null
          payment_method_details?: Json | null
          referral_link?: string | null
          status?: string
          stripe_connect_account_id?: string | null
          stripe_connect_charges_enabled?: boolean | null
          stripe_connect_created_at?: string | null
          stripe_connect_details_submitted?: boolean | null
          stripe_connect_onboard_url?: string | null
          stripe_connect_payouts_enabled?: boolean | null
          stripe_connect_status?: string | null
          total_commission_earned?: number
          total_referrals?: number
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      ai_generated_meals: {
        Row: {
          admin_notes: string | null
          allergen_contains: string[] | null
          approved_for_main_db: boolean | null
          cook_time: string | null
          course: string | null
          cuisine_type: string | null
          dietary_tags: string[] | null
          estimated_cook_time_min: number | null
          estimated_difficulty: string | null
          estimated_prep_time_min: number | null
          generated_at: string | null
          id: string
          ingredients: Json
          instructions: Json
          last_generated_at: string | null
          name: string
          prep_time: string | null
          reviewed: boolean | null
          reviewed_at: string | null
          reviewed_by: string | null
          spice_level: string | null
          times_generated: number | null
          user_id: string | null
          user_query: string | null
        }
        Insert: {
          admin_notes?: string | null
          allergen_contains?: string[] | null
          approved_for_main_db?: boolean | null
          cook_time?: string | null
          course?: string | null
          cuisine_type?: string | null
          dietary_tags?: string[] | null
          estimated_cook_time_min?: number | null
          estimated_difficulty?: string | null
          estimated_prep_time_min?: number | null
          generated_at?: string | null
          id?: string
          ingredients: Json
          instructions: Json
          last_generated_at?: string | null
          name: string
          prep_time?: string | null
          reviewed?: boolean | null
          reviewed_at?: string | null
          reviewed_by?: string | null
          spice_level?: string | null
          times_generated?: number | null
          user_id?: string | null
          user_query?: string | null
        }
        Update: {
          admin_notes?: string | null
          allergen_contains?: string[] | null
          approved_for_main_db?: boolean | null
          cook_time?: string | null
          course?: string | null
          cuisine_type?: string | null
          dietary_tags?: string[] | null
          estimated_cook_time_min?: number | null
          estimated_difficulty?: string | null
          estimated_prep_time_min?: number | null
          generated_at?: string | null
          id?: string
          ingredients?: Json
          instructions?: Json
          last_generated_at?: string | null
          name?: string
          prep_time?: string | null
          reviewed?: boolean | null
          reviewed_at?: string | null
          reviewed_by?: string | null
          spice_level?: string | null
          times_generated?: number | null
          user_id?: string | null
          user_query?: string | null
        }
        Relationships: []
      }
      ai_usage_tracking: {
        Row: {
          created_at: string | null
          feature_type: string
          id: string
          updated_at: string | null
          usage_count: number
          usage_date: string
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          feature_type: string
          id?: string
          updated_at?: string | null
          usage_count?: number
          usage_date?: string
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          feature_type?: string
          id?: string
          updated_at?: string | null
          usage_count?: number
          usage_date?: string
          user_id?: string | null
        }
        Relationships: []
      }
      ingredient_cache: {
        Row: {
          category: string | null
          created_at: string | null
          emoji: string | null
          id: string
          original_text: string
          parsed_name: string
          quantity: number | null
          unit: string | null
          unit_type: string | null
        }
        Insert: {
          category?: string | null
          created_at?: string | null
          emoji?: string | null
          id?: string
          original_text: string
          parsed_name: string
          quantity?: number | null
          unit?: string | null
          unit_type?: string | null
        }
        Update: {
          category?: string | null
          created_at?: string | null
          emoji?: string | null
          id?: string
          original_text?: string
          parsed_name?: string
          quantity?: number | null
          unit?: string | null
          unit_type?: string | null
        }
        Relationships: []
      }
      ingredient_migration_log: {
        Row: {
          confidence_score: number | null
          created_at: string | null
          id: string
          match_type: string
          matched_ingredient_id: string | null
          meal_id: string
          original_ingredient_name: string
        }
        Insert: {
          confidence_score?: number | null
          created_at?: string | null
          id?: string
          match_type: string
          matched_ingredient_id?: string | null
          meal_id: string
          original_ingredient_name: string
        }
        Update: {
          confidence_score?: number | null
          created_at?: string | null
          id?: string
          match_type?: string
          matched_ingredient_id?: string | null
          meal_id?: string
          original_ingredient_name?: string
        }
        Relationships: []
      }
      ingredients: {
        Row: {
          category: string | null
          created_at: string | null
          emoji: string | null
          id: string
          name: string
          needs_review: boolean | null
          original_name: string | null
          updated_at: string | null
        }
        Insert: {
          category?: string | null
          created_at?: string | null
          emoji?: string | null
          id?: string
          name: string
          needs_review?: boolean | null
          original_name?: string | null
          updated_at?: string | null
        }
        Update: {
          category?: string | null
          created_at?: string | null
          emoji?: string | null
          id?: string
          name?: string
          needs_review?: boolean | null
          original_name?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      ingredients_for_review: {
        Row: {
          created_at: string | null
          id: number
          ingredient_name: string
          processed_at: string | null
        }
        Insert: {
          created_at?: string | null
          id?: number
          ingredient_name: string
          processed_at?: string | null
        }
        Update: {
          created_at?: string | null
          id?: number
          ingredient_name?: string
          processed_at?: string | null
        }
        Relationships: []
      }
      meal_ingredients: {
        Row: {
          amount: string | null
          created_at: string | null
          id: string
          ingredient_id: string | null
          meal_id: string | null
          unit: string | null
          updated_at: string | null
        }
        Insert: {
          amount?: string | null
          created_at?: string | null
          id?: string
          ingredient_id?: string | null
          meal_id?: string | null
          unit?: string | null
          updated_at?: string | null
        }
        Update: {
          amount?: string | null
          created_at?: string | null
          id?: string
          ingredient_id?: string | null
          meal_id?: string | null
          unit?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "meal_ingredients_ingredient_id_fkey"
            columns: ["ingredient_id"]
            isOneToOne: false
            referencedRelation: "ingredients"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "meal_ingredients_meal_id_fkey"
            columns: ["meal_id"]
            isOneToOne: false
            referencedRelation: "meals"
            referencedColumns: ["id"]
          },
        ]
      }
      meals: {
        Row: {
          allergen_contains: string[] | null
          author: string | null
          calories: number | null
          carbs: number | null
          cookTimeHour: number | null
          cookTimeMin: number | null
          course: string | null
          createdAt: string | null
          createdById: string | null
          cuisine_type: string | null
          description: string | null
          dietary_tags: string[] | null
          fats: number | null
          id: string
          image: string | null
          ingredients: Json | null
          instructions: Json | null
          isUserRecipe: boolean | null
          lastSelected: string | null
          lastUsedDate: string | null
          meal_tags: string[] | null
          name: string
          order: number | null
          prep_method: string | null
          prepTimeHour: number | null
          prepTimeMin: number | null
          protein: number | null
          protein_type: string | null
          required_equipment: string[] | null
          servingSize: number | null
          source_url: string | null
          status: string | null
          updatedAt: string | null
        }
        Insert: {
          allergen_contains?: string[] | null
          author?: string | null
          calories?: number | null
          carbs?: number | null
          cookTimeHour?: number | null
          cookTimeMin?: number | null
          course?: string | null
          createdAt?: string | null
          createdById?: string | null
          cuisine_type?: string | null
          description?: string | null
          dietary_tags?: string[] | null
          fats?: number | null
          id?: string
          image?: string | null
          ingredients?: Json | null
          instructions?: Json | null
          isUserRecipe?: boolean | null
          lastSelected?: string | null
          lastUsedDate?: string | null
          meal_tags?: string[] | null
          name: string
          order?: number | null
          prep_method?: string | null
          prepTimeHour?: number | null
          prepTimeMin?: number | null
          protein?: number | null
          protein_type?: string | null
          required_equipment?: string[] | null
          servingSize?: number | null
          source_url?: string | null
          status?: string | null
          updatedAt?: string | null
        }
        Update: {
          allergen_contains?: string[] | null
          author?: string | null
          calories?: number | null
          carbs?: number | null
          cookTimeHour?: number | null
          cookTimeMin?: number | null
          course?: string | null
          createdAt?: string | null
          createdById?: string | null
          cuisine_type?: string | null
          description?: string | null
          dietary_tags?: string[] | null
          fats?: number | null
          id?: string
          image?: string | null
          ingredients?: Json | null
          instructions?: Json | null
          isUserRecipe?: boolean | null
          lastSelected?: string | null
          lastUsedDate?: string | null
          meal_tags?: string[] | null
          name?: string
          order?: number | null
          prep_method?: string | null
          prepTimeHour?: number | null
          prepTimeMin?: number | null
          protein?: number | null
          protein_type?: string | null
          required_equipment?: string[] | null
          servingSize?: number | null
          source_url?: string | null
          status?: string | null
          updatedAt?: string | null
        }
        Relationships: []
      }
      meals_backup: {
        Row: {
          allergen_contains: string[] | null
          author: string | null
          calories: number | null
          carbs: number | null
          cookTimeHour: number | null
          cookTimeMin: number | null
          course: string | null
          createdAt: string | null
          createdById: string | null
          cuisine_type: string | null
          description: string | null
          dietary_tags: string[] | null
          fats: number | null
          id: string | null
          image: string | null
          ingredients: Json | null
          instructions: Json | null
          isUserRecipe: boolean | null
          lastSelected: string | null
          lastUsedDate: string | null
          meal_tags: string[] | null
          name: string | null
          order: number | null
          prep_method: string | null
          prepTimeHour: number | null
          prepTimeMin: number | null
          protein: number | null
          protein_type: string | null
          required_equipment: string[] | null
          servingSize: number | null
          source_url: string | null
          status: string | null
          updatedAt: string | null
        }
        Insert: {
          allergen_contains?: string[] | null
          author?: string | null
          calories?: number | null
          carbs?: number | null
          cookTimeHour?: number | null
          cookTimeMin?: number | null
          course?: string | null
          createdAt?: string | null
          createdById?: string | null
          cuisine_type?: string | null
          description?: string | null
          dietary_tags?: string[] | null
          fats?: number | null
          id?: string | null
          image?: string | null
          ingredients?: Json | null
          instructions?: Json | null
          isUserRecipe?: boolean | null
          lastSelected?: string | null
          lastUsedDate?: string | null
          meal_tags?: string[] | null
          name?: string | null
          order?: number | null
          prep_method?: string | null
          prepTimeHour?: number | null
          prepTimeMin?: number | null
          protein?: number | null
          protein_type?: string | null
          required_equipment?: string[] | null
          servingSize?: number | null
          source_url?: string | null
          status?: string | null
          updatedAt?: string | null
        }
        Update: {
          allergen_contains?: string[] | null
          author?: string | null
          calories?: number | null
          carbs?: number | null
          cookTimeHour?: number | null
          cookTimeMin?: number | null
          course?: string | null
          createdAt?: string | null
          createdById?: string | null
          cuisine_type?: string | null
          description?: string | null
          dietary_tags?: string[] | null
          fats?: number | null
          id?: string | null
          image?: string | null
          ingredients?: Json | null
          instructions?: Json | null
          isUserRecipe?: boolean | null
          lastSelected?: string | null
          lastUsedDate?: string | null
          meal_tags?: string[] | null
          name?: string | null
          order?: number | null
          prep_method?: string | null
          prepTimeHour?: number | null
          prepTimeMin?: number | null
          protein?: number | null
          protein_type?: string | null
          required_equipment?: string[] | null
          servingSize?: number | null
          source_url?: string | null
          status?: string | null
          updatedAt?: string | null
        }
        Relationships: []
      }
      meals_backup_20250630: {
        Row: {
          allergen_contains: string[] | null
          author: string | null
          calories: number | null
          carbs: number | null
          cookTimeHour: number | null
          cookTimeMin: number | null
          course: string | null
          createdAt: string | null
          createdById: string | null
          cuisine_type: string | null
          description: string | null
          dietary_tags: string[] | null
          estimated_cost_per_serving: number | null
          fats: number | null
          id: string | null
          image: string | null
          ingredients: Json | null
          instructions: Json | null
          isUserRecipe: boolean | null
          lastSelected: string | null
          lastUsedDate: string | null
          meal_tags: string[] | null
          name: string | null
          order: number | null
          prep_method: string | null
          prepTimeHour: number | null
          prepTimeMin: number | null
          protein: number | null
          protein_type: string | null
          required_equipment: string[] | null
          servingSize: number | null
          source_url: string | null
          spice_level: string | null
          status: string | null
          updatedAt: string | null
        }
        Insert: {
          allergen_contains?: string[] | null
          author?: string | null
          calories?: number | null
          carbs?: number | null
          cookTimeHour?: number | null
          cookTimeMin?: number | null
          course?: string | null
          createdAt?: string | null
          createdById?: string | null
          cuisine_type?: string | null
          description?: string | null
          dietary_tags?: string[] | null
          estimated_cost_per_serving?: number | null
          fats?: number | null
          id?: string | null
          image?: string | null
          ingredients?: Json | null
          instructions?: Json | null
          isUserRecipe?: boolean | null
          lastSelected?: string | null
          lastUsedDate?: string | null
          meal_tags?: string[] | null
          name?: string | null
          order?: number | null
          prep_method?: string | null
          prepTimeHour?: number | null
          prepTimeMin?: number | null
          protein?: number | null
          protein_type?: string | null
          required_equipment?: string[] | null
          servingSize?: number | null
          source_url?: string | null
          spice_level?: string | null
          status?: string | null
          updatedAt?: string | null
        }
        Update: {
          allergen_contains?: string[] | null
          author?: string | null
          calories?: number | null
          carbs?: number | null
          cookTimeHour?: number | null
          cookTimeMin?: number | null
          course?: string | null
          createdAt?: string | null
          createdById?: string | null
          cuisine_type?: string | null
          description?: string | null
          dietary_tags?: string[] | null
          estimated_cost_per_serving?: number | null
          fats?: number | null
          id?: string | null
          image?: string | null
          ingredients?: Json | null
          instructions?: Json | null
          isUserRecipe?: boolean | null
          lastSelected?: string | null
          lastUsedDate?: string | null
          meal_tags?: string[] | null
          name?: string | null
          order?: number | null
          prep_method?: string | null
          prepTimeHour?: number | null
          prepTimeMin?: number | null
          protein?: number | null
          protein_type?: string | null
          required_equipment?: string[] | null
          servingSize?: number | null
          source_url?: string | null
          spice_level?: string | null
          status?: string | null
          updatedAt?: string | null
        }
        Relationships: []
      }
      meals_current_backup: {
        Row: {
          allergen_contains: string[] | null
          author: string | null
          calories: number | null
          carbs: number | null
          cookTimeHour: number | null
          cookTimeMin: number | null
          course: string | null
          createdAt: string | null
          createdById: string | null
          cuisine_type: string | null
          description: string | null
          dietary_tags: string[] | null
          fats: number | null
          id: string | null
          image: string | null
          ingredients: Json | null
          instructions: Json | null
          isUserRecipe: boolean | null
          lastSelected: string | null
          lastUsedDate: string | null
          meal_tags: string[] | null
          name: string | null
          order: number | null
          prep_method: string | null
          prepTimeHour: number | null
          prepTimeMin: number | null
          protein: number | null
          protein_type: string | null
          required_equipment: string[] | null
          servingSize: number | null
          source_url: string | null
          status: string | null
          updatedAt: string | null
        }
        Insert: {
          allergen_contains?: string[] | null
          author?: string | null
          calories?: number | null
          carbs?: number | null
          cookTimeHour?: number | null
          cookTimeMin?: number | null
          course?: string | null
          createdAt?: string | null
          createdById?: string | null
          cuisine_type?: string | null
          description?: string | null
          dietary_tags?: string[] | null
          fats?: number | null
          id?: string | null
          image?: string | null
          ingredients?: Json | null
          instructions?: Json | null
          isUserRecipe?: boolean | null
          lastSelected?: string | null
          lastUsedDate?: string | null
          meal_tags?: string[] | null
          name?: string | null
          order?: number | null
          prep_method?: string | null
          prepTimeHour?: number | null
          prepTimeMin?: number | null
          protein?: number | null
          protein_type?: string | null
          required_equipment?: string[] | null
          servingSize?: number | null
          source_url?: string | null
          status?: string | null
          updatedAt?: string | null
        }
        Update: {
          allergen_contains?: string[] | null
          author?: string | null
          calories?: number | null
          carbs?: number | null
          cookTimeHour?: number | null
          cookTimeMin?: number | null
          course?: string | null
          createdAt?: string | null
          createdById?: string | null
          cuisine_type?: string | null
          description?: string | null
          dietary_tags?: string[] | null
          fats?: number | null
          id?: string | null
          image?: string | null
          ingredients?: Json | null
          instructions?: Json | null
          isUserRecipe?: boolean | null
          lastSelected?: string | null
          lastUsedDate?: string | null
          meal_tags?: string[] | null
          name?: string | null
          order?: number | null
          prep_method?: string | null
          prepTimeHour?: number | null
          prepTimeMin?: number | null
          protein?: number | null
          protein_type?: string | null
          required_equipment?: string[] | null
          servingSize?: number | null
          source_url?: string | null
          status?: string | null
          updatedAt?: string | null
        }
        Relationships: []
      }
      unit_conversions: {
        Row: {
          conversion_factor: number
          created_at: string | null
          from_unit: string
          id: string
          to_unit: string
          unit_type: string
        }
        Insert: {
          conversion_factor: number
          created_at?: string | null
          from_unit: string
          id?: string
          to_unit: string
          unit_type: string
        }
        Update: {
          conversion_factor?: number
          created_at?: string | null
          from_unit?: string
          id?: string
          to_unit?: string
          unit_type?: string
        }
        Relationships: []
      }
      user_favorites: {
        Row: {
          created_at: string | null
          id: string
          meal_id: string
          user_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          meal_id: string
          user_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          meal_id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_favorites_meal_id_fkey"
            columns: ["meal_id"]
            isOneToOne: false
            referencedRelation: "meals"
            referencedColumns: ["id"]
          },
        ]
      }
      user_meals: {
        Row: {
          allergen_contains: string[] | null
          calories: number | null
          carbs: number | null
          cook_time_hour: number | null
          cook_time_min: number | null
          course: string | null
          created_at: string | null
          cuisine_type: string | null
          description: string | null
          dietary_tags: string[] | null
          fats: number | null
          id: string
          image: string | null
          ingredients: Json | null
          instructions: Json | null
          name: string
          prep_time_hour: number | null
          prep_time_min: number | null
          protein: number | null
          serving_size: number | null
          spice_level: string | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          allergen_contains?: string[] | null
          calories?: number | null
          carbs?: number | null
          cook_time_hour?: number | null
          cook_time_min?: number | null
          course?: string | null
          created_at?: string | null
          cuisine_type?: string | null
          description?: string | null
          dietary_tags?: string[] | null
          fats?: number | null
          id?: string
          image?: string | null
          ingredients?: Json | null
          instructions?: Json | null
          name: string
          prep_time_hour?: number | null
          prep_time_min?: number | null
          protein?: number | null
          serving_size?: number | null
          spice_level?: string | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          allergen_contains?: string[] | null
          calories?: number | null
          carbs?: number | null
          cook_time_hour?: number | null
          cook_time_min?: number | null
          course?: string | null
          created_at?: string | null
          cuisine_type?: string | null
          description?: string | null
          dietary_tags?: string[] | null
          fats?: number | null
          id?: string
          image?: string | null
          ingredients?: Json | null
          instructions?: Json | null
          name?: string
          prep_time_hour?: number | null
          prep_time_min?: number | null
          protein?: number | null
          serving_size?: number | null
          spice_level?: string | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      user_preferences: {
        Row: {
          allergies: string[] | null
          available_cooking_time: string | null
          budget_preference: string | null
          cooking_skill_level: string | null
          created_at: string | null
          cuisine_preferences: string[] | null
          daily_messages_limit: number | null
          daily_messages_used: number | null
          dietary_restrictions: string[] | null
          household_size: number | null
          id: string
          kitchen_equipment: string[] | null
          last_message_reset_date: string | null
          referral_tracked_at: string | null
          referred_by_affiliate_code: string | null
          seasonal_preference: string | null
          spice_tolerance: string | null
          subscription_end_date: string | null
          subscription_start_date: string | null
          subscription_status: string | null
          subscription_stripe_customer_id: string | null
          subscription_tier: string | null
          total_messages_used: number | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          allergies?: string[] | null
          available_cooking_time?: string | null
          budget_preference?: string | null
          cooking_skill_level?: string | null
          created_at?: string | null
          cuisine_preferences?: string[] | null
          daily_messages_limit?: number | null
          daily_messages_used?: number | null
          dietary_restrictions?: string[] | null
          household_size?: number | null
          id?: string
          kitchen_equipment?: string[] | null
          last_message_reset_date?: string | null
          referral_tracked_at?: string | null
          referred_by_affiliate_code?: string | null
          seasonal_preference?: string | null
          spice_tolerance?: string | null
          subscription_end_date?: string | null
          subscription_start_date?: string | null
          subscription_status?: string | null
          subscription_stripe_customer_id?: string | null
          subscription_tier?: string | null
          total_messages_used?: number | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          allergies?: string[] | null
          available_cooking_time?: string | null
          budget_preference?: string | null
          cooking_skill_level?: string | null
          created_at?: string | null
          cuisine_preferences?: string[] | null
          daily_messages_limit?: number | null
          daily_messages_used?: number | null
          dietary_restrictions?: string[] | null
          household_size?: number | null
          id?: string
          kitchen_equipment?: string[] | null
          last_message_reset_date?: string | null
          referral_tracked_at?: string | null
          referred_by_affiliate_code?: string | null
          seasonal_preference?: string | null
          spice_tolerance?: string | null
          subscription_end_date?: string | null
          subscription_start_date?: string | null
          subscription_status?: string | null
          subscription_stripe_customer_id?: string | null
          subscription_tier?: string | null
          total_messages_used?: number | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      webhook_logs: {
        Row: {
          created_at: string | null
          event_type: string
          id: string
          processed_at: string | null
          source_ip: string | null
          webhook_data: Json
        }
        Insert: {
          created_at?: string | null
          event_type: string
          id?: string
          processed_at?: string | null
          source_ip?: string | null
          webhook_data: Json
        }
        Update: {
          created_at?: string | null
          event_type?: string
          id?: string
          processed_at?: string | null
          source_ip?: string | null
          webhook_data?: Json
        }
        Relationships: []
      }
      weekly_meal_plans: {
        Row: {
          additional_ingredients: Json | null
          ai_generated_meals: Json | null
          archived_at: string | null
          created_at: string | null
          disabled_days: number[] | null
          id: string
          is_active: boolean | null
          meal_ids: string[]
          selected_side_dishes: Json | null
          updated_at: string | null
          user_id: string
          week_start_date: string
        }
        Insert: {
          additional_ingredients?: Json | null
          ai_generated_meals?: Json | null
          archived_at?: string | null
          created_at?: string | null
          disabled_days?: number[] | null
          id?: string
          is_active?: boolean | null
          meal_ids: string[]
          selected_side_dishes?: Json | null
          updated_at?: string | null
          user_id: string
          week_start_date: string
        }
        Update: {
          additional_ingredients?: Json | null
          ai_generated_meals?: Json | null
          archived_at?: string | null
          created_at?: string | null
          disabled_days?: number[] | null
          id?: string
          is_active?: boolean | null
          meal_ids?: string[]
          selected_side_dishes?: Json | null
          updated_at?: string | null
          user_id?: string
          week_start_date?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      archive_previous_meal_plans: {
        Args: { p_user_id: string; p_new_week_start: string }
        Returns: undefined
      }
      clean_ingredient_name_aggressive: {
        Args: { input_name: string }
        Returns: string
      }
      clean_remaining_ingredients: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      cleanup_old_archived_meal_plans: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      create_affiliate_payout: {
        Args: {
          affiliate_id_param: string
          amount_param: number
          period_start_param: string
          period_end_param: string
          referral_ids_param?: string[]
        }
        Returns: Json
      }
      create_affiliate_with_coupon: {
        Args: {
          user_email: string
          coupon_code_param: string
          commission_rate_param?: number
        }
        Returns: Json
      }
      create_stripe_connect_account: {
        Args: {
          affiliate_user_id: string
          email_param: string
          business_type_param?: string
        }
        Returns: Json
      }
      extract_core_ingredient: {
        Args: { raw_name: string }
        Returns: string
      }
      generate_affiliate_code: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      get_affiliate_referral_stats: {
        Args: { affiliate_user_id: string }
        Returns: Json
      }
      get_affiliates_ready_for_payout: {
        Args: { minimum_amount_param?: number }
        Returns: {
          affiliate_id: string
          user_id: string
          stripe_connect_account_id: string
          coupon_code: string
          total_commission_earned: number
          unpaid_amount: number
          minimum_payout_threshold: number
          last_payout_date: string
        }[]
      }
      get_ai_usage_today: {
        Args: {
          p_user_id: string
          p_feature_type: string
          p_usage_date?: string
        }
        Returns: number
      }
      get_current_week_range: {
        Args: Record<PropertyKey, never>
        Returns: {
          start_date: string
          end_date: string
        }[]
      }
      get_ingredient_groups: {
        Args: { search_term?: string }
        Returns: {
          base_name: string
          count: number
          ingredients: Json
        }[]
      }
      get_meals_batch: {
        Args: { p_meal_ids: string[] }
        Returns: {
          id: string
          name: string
          prep_time_hour: number
          prep_time_min: number
          cook_time_hour: number
          cook_time_min: number
          serving_size: number
          image: string
          course: string
          cuisine_type: string
          prep_method: string
          calories: number
          protein: number
          carbs: number
          fats: number
        }[]
      }
      get_parsed_ingredient: {
        Args: { p_original_text: string }
        Returns: {
          parsed_name: string
          category: string
          emoji: string
        }[]
      }
      get_preparation_method_id: {
        Args: { unit_text: string; ingredient_name: string }
        Returns: number
      }
      get_unit_id: {
        Args: { unit_text: string }
        Returns: number
      }
      increment_affiliate_stats: {
        Args: { affiliate_id: string; commission: number }
        Returns: undefined
      }
      increment_ai_usage: {
        Args: {
          p_user_id: string
          p_feature_type: string
          p_usage_date?: string
        }
        Returns: number
      }
      iterative_ingredient_cleanup: {
        Args: Record<PropertyKey, never>
        Returns: {
          iteration: number
          duplicates_found: number
          ingredients_merged: number
          total_ingredients_remaining: number
        }[]
      }
      link_user_to_affiliate_referral: {
        Args: { user_id: string; stripe_customer_id: string }
        Returns: Json
      }
      merge_duplicate_ingredients: {
        Args: Record<PropertyKey, never>
        Returns: {
          cleaned_name: string
          kept_ingredient_id: string
          merged_ingredient_ids: string[]
          total_merged: number
        }[]
      }
      merge_duplicate_ingredients_aggressive: {
        Args: Record<PropertyKey, never>
        Returns: {
          cleaned_name: string
          kept_ingredient_id: string
          merged_ingredient_ids: string[]
          total_merged: number
        }[]
      }
      migrate_meal_ingredients_batch: {
        Args: { batch_offset?: number; batch_size?: number }
        Returns: Json
      }
      normalize_ingredient_name: {
        Args: { ingredient_name: string }
        Returns: string
      }
      parse_ingredient_with_quantity: {
        Args: { p_ingredient_text: string }
        Returns: {
          quantity: number
          unit: string
          ingredient_name: string
          category: string
          emoji: string
        }[]
      }
      process_affiliate_referral: {
        Args: {
          affiliate_code: string
          stripe_subscription_id: string
          stripe_customer_id: string
          subscription_amount?: number
          commission_amount?: number
        }
        Returns: Json
      }
      process_affiliate_referral_coupon: {
        Args: {
          coupon_code_param: string
          stripe_subscription_id_param: string
          stripe_customer_id_param: string
          stripe_promotion_code_id_param: string
          stripe_coupon_id_param: string
          subscription_amount_param: number
          discount_amount_param: number
          commission_amount_param?: number
        }
        Returns: Json
      }
      process_recurring_commission: {
        Args: {
          stripe_subscription_id: string
          billing_period_start: string
          billing_period_end: string
        }
        Returns: Json
      }
      process_recurring_commission_coupon: {
        Args: {
          stripe_subscription_id_param: string
          billing_period_start_param: string
          billing_period_end_param: string
        }
        Returns: Json
      }
      process_stripe_webhook: {
        Args: { webhook_data: Json }
        Returns: Json
      }
      process_stripe_webhook_secure: {
        Args: { payload: string; signature_header: string; webhook_data: Json }
        Returns: Json
      }
      process_stripe_webhook_with_auth: {
        Args: { webhook_data: Json; api_key?: string }
        Returns: Json
      }
      should_reset_meal_plan: {
        Args: { plan_created_at: string }
        Returns: boolean
      }
      update_affiliate_connect_account: {
        Args: {
          affiliate_id_param: string
          stripe_connect_account_id_param: string
          connect_status_param: string
          details_submitted_param?: boolean
          charges_enabled_param?: boolean
          payouts_enabled_param?: boolean
          onboard_url_param?: string
        }
        Returns: Json
      }
      update_payout_status: {
        Args: {
          payout_id_param: string
          status_param: string
          stripe_connect_transfer_id_param?: string
          stripe_connect_payout_id_param?: string
          failure_reason_param?: string
        }
        Returns: Json
      }
      verify_stripe_signature: {
        Args: {
          payload: string
          signature_header: string
          webhook_secret: string
          tolerance_seconds?: number
        }
        Returns: Json
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const