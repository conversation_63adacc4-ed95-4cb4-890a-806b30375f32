interface ParsedIngredient {
  amount: number;
  unit: string;
  name: string;
  isRange?: boolean;
  rangeMax?: number;
}

interface ScaledIngredient {
  name: string;
  amount: string;
  emoji: string;
}

// Common unit variations and their standardized forms
const UNIT_VARIATIONS: Record<string, string> = {
  tablespoon: 'tbsp',
  tablespoons: 'tbsp',
  teaspoon: 'tsp',
  teaspoons: 'tsp',
  cup: 'cup',
  cups: 'cups',
  pound: 'lb',
  pounds: 'lbs',
  ounce: 'oz',
  ounces: 'oz',
  gram: 'g',
  grams: 'g',
  kilogram: 'kg',
  kilograms: 'kg',
  milliliter: 'ml',
  milliliters: 'ml',
  liter: 'l',
  liters: 'l',
  clove: 'clove',
  cloves: 'cloves',
  piece: 'piece',
  pieces: 'pieces',
  slice: 'slice',
  slices: 'slices',
  pinch: 'pinch',
  dash: 'dash',
  can: 'can',
  jar: 'jar',
  bunch: 'bunch',
  head: 'head',
  stalk: 'stalk',
  stalks: 'stalks',
  whole: 'whole',
  half: 'half',
  quarter: 'quarter',
  third: 'third',
};

// Units that should always be rounded to whole numbers
const WHOLE_UNITS = new Set([
  'whole',
  'piece',
  'pieces',
  'slice',
  'slices',
  'clove',
  'cloves',
  'can',
  'jar',
  'bunch',
  'head',
  'stalk',
  'stalks',
  'egg',
  'eggs',
]);

// Units that use fractional display
const FRACTIONAL_UNITS = new Set(['cup', 'cups', 'tbsp', 'tsp']);

// Parse ingredient string to extract amount, unit, and name
export function parseIngredientAmount(
  ingredientStr: string
): ParsedIngredient | null {
  // Handle "to taste" or ingredients without amounts
  if (
    ingredientStr.toLowerCase().includes('to taste') ||
    ingredientStr.toLowerCase().includes('as needed') ||
    ingredientStr.toLowerCase().includes('optional') ||
    ingredientStr.toLowerCase().includes('for serving') ||
    ingredientStr.toLowerCase().includes('for garnish')
  ) {
    return null;
  }

  // Clean up the string first
  const cleanStr = ingredientStr.trim();

  // Match patterns like "1-2 cups", "1 to 2 cups", "1/2 cup", "1.5 lbs", "2 cloves", etc.
  const rangePattern =
    /^(\d+\.?\d*)\s*(?:[-–]|to)\s*(\d+\.?\d*)\s*([\w/]+)?\s*(.*)$/;
  const fractionPattern = /^(\d+)\/(\d+)\s*([\w/]+)?\s*(.*)$/;
  const mixedFractionPattern = /^(\d+)\s+(\d+)\/(\d+)\s*([\w/]+)?\s*(.*)$/;
  const decimalPattern = /^(\d+\.?\d*)\s*([\w/]+)?\s*(.*)$/;

  let match;
  let amount = 0;
  let unit = '';
  let name = '';
  let isRange = false;
  let rangeMax = 0;

  // Try range pattern first
  match = ingredientStr.match(rangePattern);
  if (match) {
    amount = parseFloat(match[1]);
    rangeMax = parseFloat(match[2]);
    unit = match[3] || '';
    name = match[4].trim();
    isRange = true;
  } else {
    // Try mixed fraction (e.g., "1 1/2")
    match = ingredientStr.match(mixedFractionPattern);
    if (match) {
      amount = parseInt(match[1]) + parseInt(match[2]) / parseInt(match[3]);
      unit = match[4] || '';
      name = match[5].trim();
    } else {
      // Try simple fraction
      match = ingredientStr.match(fractionPattern);
      if (match) {
        amount = parseInt(match[1]) / parseInt(match[2]);
        unit = match[3] || '';
        name = match[4].trim();
      } else {
        // Try decimal
        match = ingredientStr.match(decimalPattern);
        if (match) {
          amount = parseFloat(match[1]);
          unit = match[2] || '';
          name = match[3].trim();
        } else {
          // No amount found
          return null;
        }
      }
    }
  }

  // Standardize unit
  unit = UNIT_VARIATIONS[unit.toLowerCase()] || unit;

  return { amount, unit, name, isRange, rangeMax };
}

// Convert decimal to fraction string
function decimalToFraction(decimal: number): string {
  const tolerance = 0.01;

  // Common fractions
  const fractions = [
    { decimal: 0.25, fraction: '1/4' },
    { decimal: 0.33, fraction: '1/3' },
    { decimal: 0.5, fraction: '1/2' },
    { decimal: 0.66, fraction: '2/3' },
    { decimal: 0.75, fraction: '3/4' },
  ];

  // Check for whole number
  if (Math.abs(decimal - Math.round(decimal)) < tolerance) {
    return Math.round(decimal).toString();
  }

  // Check for mixed fractions
  const wholePart = Math.floor(decimal);
  const fractionalPart = decimal - wholePart;

  for (const { decimal: frac, fraction } of fractions) {
    if (Math.abs(fractionalPart - frac) < tolerance) {
      return wholePart > 0 ? `${wholePart} ${fraction}` : fraction;
    }
  }

  // Default to 1 decimal place
  return decimal.toFixed(1);
}

// Apply intelligent rounding based on unit type
function roundAmount(amount: number, unit: string): number {
  // Ensure we never return 0 for any amount
  const MIN_AMOUNTS: Record<string, number> = {
    cup: 0.25,
    cups: 0.25,
    tbsp: 0.25,
    tsp: 0.25,
    lb: 0.1,
    lbs: 0.1,
    oz: 0.1,
    g: 1,
    kg: 0.1,
    ml: 5,
    l: 0.1,
  };

  // Units that should be whole numbers
  if (
    WHOLE_UNITS.has(unit.toLowerCase()) ||
    unit.toLowerCase().includes('egg')
  ) {
    const rounded = Math.round(amount);
    return rounded === 0 ? 1 : rounded;
  }

  // Get minimum amount for this unit
  const minAmount = MIN_AMOUNTS[unit.toLowerCase()] || 0.1;

  // Cups - round to nearest 1/4
  if (unit === 'cup' || unit === 'cups') {
    const rounded = Math.round(amount * 4) / 4;
    return rounded < minAmount ? minAmount : rounded;
  }

  // Tablespoons and teaspoons - round to nearest 1/4
  if (unit === 'tbsp' || unit === 'tsp') {
    if (amount < 1) {
      const rounded = Math.round(amount * 4) / 4;
      return rounded < minAmount ? minAmount : rounded;
    }
    const rounded = Math.round(amount * 2) / 2; // Round to nearest 0.5 for larger amounts
    return rounded < minAmount ? minAmount : rounded;
  }

  // Weight units - keep 1 decimal place
  if (['lb', 'lbs', 'oz', 'g', 'kg'].includes(unit)) {
    const rounded = Math.round(amount * 10) / 10;
    return rounded < minAmount ? minAmount : rounded;
  }

  // Volume units - keep 1 decimal place
  if (['ml', 'l', 'fl oz'].includes(unit)) {
    const rounded = Math.round(amount * 10) / 10;
    return rounded < minAmount ? minAmount : rounded;
  }

  // Default - round to nearest 0.5, ensure minimum of 0.1
  const rounded = Math.round(amount * 2) / 2;
  return rounded < 0.1 ? 0.1 : rounded;
}

// Format amount for display
function formatAmount(amount: number, unit: string): string {
  // Use fractions for cup, tbsp, tsp
  if (FRACTIONAL_UNITS.has(unit)) {
    return decimalToFraction(amount);
  }

  // Whole numbers - no decimal
  if (amount === Math.floor(amount)) {
    return amount.toString();
  }

  // Default to 1 decimal place
  return amount.toFixed(1);
}

// Scale a single ingredient
export function scaleIngredient(
  ingredient: { name: string; amount: string; emoji: string },
  originalServings: number,
  targetServings: number
): ScaledIngredient {
  // No scaling needed
  if (originalServings === targetServings) {
    return ingredient;
  }

  const parsed = parseIngredientAmount(
    `${ingredient.amount} ${ingredient.name}`
  );

  // Can't parse or special ingredients - return as-is
  if (!parsed) {
    return ingredient;
  }

  const scaleFactor = targetServings / originalServings;
  let scaledAmount = parsed.amount * scaleFactor;
  let scaledRangeMax = parsed.rangeMax ? parsed.rangeMax * scaleFactor : 0;

  // Apply intelligent rounding
  scaledAmount = roundAmount(scaledAmount, parsed.unit);
  if (scaledRangeMax) {
    scaledRangeMax = roundAmount(scaledRangeMax, parsed.unit);
  }

  // Format the amount
  let formattedAmount = formatAmount(scaledAmount, parsed.unit);
  if (parsed.isRange && scaledRangeMax) {
    const formattedMax = formatAmount(scaledRangeMax, parsed.unit);
    formattedAmount = `${formattedAmount}-${formattedMax}`;
  }

  // Adjust unit plurality based on the scaled amount
  const adjustedUnit = adjustUnitPlurality(scaledAmount, parsed.unit);

  // Reconstruct the amount string
  const newAmount = adjustedUnit
    ? `${formattedAmount} ${adjustedUnit}`
    : formattedAmount;

  return {
    name: parsed.name,
    amount: newAmount,
    emoji: ingredient.emoji,
  };
}

// Scale all ingredients in a list
export function scaleIngredients(
  ingredients: { name: string; amount: string; emoji: string }[],
  originalServings: number,
  targetServings: number
): ScaledIngredient[] {
  return ingredients.map((ingredient) =>
    scaleIngredient(ingredient, originalServings, targetServings)
  );
}

// Handle plural/singular unit conversion
export function adjustUnitPlurality(amount: number, unit: string): string {
  const shouldBeSingular = amount <= 1;

  const pluralToSingular: Record<string, string> = {
    cups: 'cup',
    lbs: 'lb',
    cloves: 'clove',
    pieces: 'piece',
    slices: 'slice',
    stalks: 'stalk',
  };

  const singularToPlural: Record<string, string> = {
    cup: 'cups',
    lb: 'lbs',
    clove: 'cloves',
    piece: 'pieces',
    slice: 'slices',
    stalk: 'stalks',
  };

  if (shouldBeSingular && pluralToSingular[unit]) {
    return pluralToSingular[unit];
  } else if (!shouldBeSingular && singularToPlural[unit]) {
    return singularToPlural[unit];
  }

  return unit;
}
