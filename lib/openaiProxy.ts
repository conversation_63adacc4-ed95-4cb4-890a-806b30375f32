/**
 * OpenAI API Proxy Client
 * Uses Supabase Edge Function to make secure OpenAI API calls
 * Keeps API keys server-side and adds rate limiting
 */

import { supabase } from './supabase';

export interface OpenAIMessage {
  role: 'system' | 'user' | 'assistant';
  content:
    | string
    | {
        type: 'text' | 'image_url';
        text?: string;
        image_url?: {
          url: string;
          detail?: 'low' | 'high' | 'auto';
        };
      }[];
}

export interface OpenAITool {
  type: string;
  function: {
    name: string;
    description: string;
    parameters: any;
  };
}

export interface OpenAIRequest {
  messages: OpenAIMessage[];
  model?: string;
  max_tokens?: number;
  temperature?: number;
  tools?: OpenAITool[];
  tool_choice?: string | any;
}

export interface OpenAIResponse {
  choices: {
    message: {
      role: string;
      content: string;
      tool_calls?: {
        id: string;
        type: string;
        function: {
          name: string;
          arguments: string;
        };
      }[];
    };
    finish_reason: string;
  }[];
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export class OpenAIProxyError extends Error {
  constructor(
    message: string,
    public status?: number
  ) {
    super(message);
    this.name = 'OpenAIProxyError';
  }
}

/**
 * Make a secure OpenAI API call through Supabase Edge Function
 */
export async function callOpenAI(
  request: OpenAIRequest | OpenAIMessage[]
): Promise<OpenAIResponse> {
  // Handle both old signature (messages array) and new signature (full request object)
  const fullRequest: OpenAIRequest = Array.isArray(request)
    ? { messages: request }
    : request;
  try {
    // Get the current session for authentication
    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.getSession();

    if (sessionError || !session) {
      throw new OpenAIProxyError('Authentication required', 401);
    }

    // Make request to our secure proxy
    const response = await fetch(
      `${process.env.EXPO_PUBLIC_SUPABASE_URL}/functions/v1/proxy-openai`,
      {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
          apikey: process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY!,
        },
        body: JSON.stringify(fullRequest),
      }
    );

    if (!response.ok) {
      const errorText = await response.text();

      if (response.status === 429) {
        throw new OpenAIProxyError(
          'Rate limit exceeded. Please try again in an hour.',
          429
        );
      }

      if (response.status === 401) {
        throw new OpenAIProxyError(
          'Authentication failed. Please log in again.',
          401
        );
      }

      throw new OpenAIProxyError(
        errorText || 'AI service unavailable',
        response.status
      );
    }

    const data = await response.json();
    return data as OpenAIResponse;
  } catch (error) {
    if (error instanceof OpenAIProxyError) {
      throw error;
    }

    throw new OpenAIProxyError(`Failed to connect to AI service: ${error}`);
  }
}

/**
 * Helper function to create a system message
 */
export function createSystemMessage(content: string): OpenAIMessage {
  return { role: 'system', content };
}

/**
 * Helper function to create a user message
 */
export function createUserMessage(content: string): OpenAIMessage {
  return { role: 'user', content };
}

/**
 * Helper function to create an assistant message
 */
export function createAssistantMessage(content: string): OpenAIMessage {
  return { role: 'assistant', content };
}

/**
 * Helper function to create a user message with image (for vision API)
 */
export function createUserVisionMessage(
  textContent: string,
  imageUrl: string,
  detail: 'low' | 'high' | 'auto' = 'high'
): OpenAIMessage {
  return {
    role: 'user',
    content: [
      {
        type: 'text',
        text: textContent,
      },
      {
        type: 'image_url',
        image_url: {
          url: imageUrl,
          detail: detail,
        },
      },
    ],
  };
}
