import { supabase } from './supabase';

export interface IngredientItem {
  text: string;
  mealId?: string;
  mealName?: string;
}

export interface ConsolidatedIngredient {
  ingredient_name: string;
  total_quantity: number;
  unit: string;
  display_text: string;
  category: string;
  emoji: string;
  instacart_unit?: {
    code: string;
    name: string;
    original_unit: string;
  };
  originalItems?: IngredientItem[]; // Track which meals contributed
}

/**
 * Consolidates multiple ingredient strings by combining quantities of the same ingredient
 * @param ingredients Array of ingredient strings from different meals
 * @returns Array of consolidated ingredients with combined quantities
 */
export async function consolidateIngredients(
  ingredients: IngredientItem[]
): Promise<ConsolidatedIngredient[]> {
  if (ingredients.length === 0) return [];

  try {
    // Call the database function to consolidate
    const { data, error } = await supabase.rpc('consolidate_ingredients', {
      p_ingredients: ingredients.map((i) => ({ text: i.text })),
    });

    if (error) {
      console.error('Error consolidating ingredients:', error);
      // Fallback: return unconsolidated list
      return ingredients.map((i) => ({
        ingredient_name: i.text,
        total_quantity: 1,
        unit: '',
        display_text: i.text,
        category: 'uncategorized',
        emoji: '',
        originalItems: [i],
      }));
    }

    // Map the results and track which meals contributed
    const consolidated = new Map<string, ConsolidatedIngredient>();

    // First, build the consolidated map from the database results
    if (data) {
      data.forEach((item: any) => {
        consolidated.set(item.ingredient_name, {
          ...item,
          originalItems: [],
        });
      });
    }

    // Then, track which meals contributed to each ingredient
    for (const ingredient of ingredients) {
      // Parse to find which consolidated ingredient this belongs to
      const { data: parsed } = await supabase.rpc(
        'parse_ingredient_with_quantity',
        {
          p_ingredient_text: ingredient.text,
        }
      );

      if (parsed && parsed.length > 0) {
        const key = parsed[0].ingredient_name.toLowerCase().trim();
        const consolidatedItem = consolidated.get(key);
        if (consolidatedItem) {
          consolidatedItem.originalItems!.push(ingredient);
        }
      }
    }

    return Array.from(consolidated.values());
  } catch (error) {
    console.error('Error in consolidateIngredients:', error);
    // Fallback: return unconsolidated list
    return ingredients.map((i) => ({
      ingredient_name: i.text,
      total_quantity: 1,
      unit: '',
      display_text: i.text,
      category: 'uncategorized',
      emoji: '',
      originalItems: [i],
    }));
  }
}

/**
 * Formats a quantity for display (e.g., converts 1.0 to "1", 0.5 to "1/2")
 */
export function formatQuantity(quantity: number): string {
  // Handle whole numbers
  if (quantity % 1 === 0) {
    return quantity.toString();
  }

  // Common fractions
  const fractions: { [key: number]: string } = {
    0.25: '1/4',
    0.333: '1/3',
    0.5: '1/2',
    0.666: '2/3',
    0.667: '2/3',
    0.75: '3/4',
  };

  // Check for common fractions
  for (const [decimal, fraction] of Object.entries(fractions)) {
    if (Math.abs(quantity - parseFloat(decimal)) < 0.01) {
      return fraction;
    }
  }

  // Check for mixed numbers
  const whole = Math.floor(quantity);
  const decimal = quantity - whole;

  if (whole > 0) {
    for (const [dec, frac] of Object.entries(fractions)) {
      if (Math.abs(decimal - parseFloat(dec)) < 0.01) {
        return `${whole} ${frac}`;
      }
    }
  }

  // Default to decimal with 1 decimal place
  return quantity.toFixed(1).replace(/\.0$/, '');
}

/**
 * Groups consolidated ingredients by category
 */
export function groupByCategory(
  ingredients: ConsolidatedIngredient[]
): Map<string, ConsolidatedIngredient[]> {
  const grouped = new Map<string, ConsolidatedIngredient[]>();

  ingredients.forEach((ingredient) => {
    const category = ingredient.category || 'Other';
    if (!grouped.has(category)) {
      grouped.set(category, []);
    }
    grouped.get(category)!.push(ingredient);
  });

  // Sort categories in a logical order
  const sortedMap = new Map<string, ConsolidatedIngredient[]>();
  const categoryOrder = [
    'Produce',
    'Meat & Seafood',
    'Dairy',
    'Bakery',
    'Pantry',
    'Frozen',
    'Beverages',
    'Other',
  ];

  categoryOrder.forEach((category) => {
    if (grouped.has(category)) {
      sortedMap.set(category, grouped.get(category)!);
    }
  });

  // Add any remaining categories
  grouped.forEach((items, category) => {
    if (!sortedMap.has(category)) {
      sortedMap.set(category, items);
    }
  });

  return sortedMap;
}
