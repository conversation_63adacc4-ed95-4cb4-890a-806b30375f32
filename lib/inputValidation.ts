/**
 * Comprehensive input validation and sanitization utilities
 * Prevents XSS, injection attacks, and ensures data integrity
 */

// Email validation with comprehensive checks
export function validateEmail(email: string): {
  isValid: boolean;
  error?: string;
} {
  if (!email || email.trim().length === 0) {
    return { isValid: false, error: 'Email is required' };
  }

  // Remove whitespace
  email = email.trim();

  // Check length
  if (email.length > 254) {
    return { isValid: false, error: 'Email is too long' };
  }

  // RFC 5322 compliant email regex
  const emailRegex =
    /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;

  if (!emailRegex.test(email)) {
    return { isValid: false, error: 'Please enter a valid email address' };
  }

  // Check for common malicious patterns
  const maliciousPatterns = [
    /<script/i,
    /javascript:/i,
    /data:/i,
    /vbscript:/i,
    /onload=/i,
    /onerror=/i,
  ];

  for (const pattern of maliciousPatterns) {
    if (pattern.test(email)) {
      return { isValid: false, error: 'Invalid email format' };
    }
  }

  return { isValid: true };
}

// Name validation for user display names
export function validateName(name: string): {
  isValid: boolean;
  error?: string;
} {
  if (!name || name.trim().length === 0) {
    return { isValid: false, error: 'Name is required' };
  }

  // Remove leading/trailing whitespace
  name = name.trim();

  // Check length
  if (name.length < 2) {
    return { isValid: false, error: 'Name must be at least 2 characters' };
  }

  if (name.length > 50) {
    return { isValid: false, error: 'Name must not exceed 50 characters' };
  }

  // Allow letters, spaces, hyphens, apostrophes, and dots
  const nameRegex = /^[a-zA-Z\s\-'.]+$/;

  if (!nameRegex.test(name)) {
    return {
      isValid: false,
      error:
        'Name can only contain letters, spaces, hyphens, apostrophes, and dots',
    };
  }

  // Check for excessive consecutive spaces or special characters
  if (/\s{2,}/.test(name) || /[-'.]{2,}/.test(name)) {
    return {
      isValid: false,
      error: 'Name contains invalid character patterns',
    };
  }

  return { isValid: true };
}

// Text input sanitization for general text fields
export function sanitizeText(input: string, maxLength: number = 1000): string {
  if (!input) return '';

  // Remove potential XSS patterns
  let sanitized = input
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove script tags
    .replace(/javascript:/gi, '') // Remove javascript: URLs
    .replace(/data:text\/html/gi, '') // Remove data URLs
    .replace(/vbscript:/gi, '') // Remove vbscript
    .replace(/on\w+\s*=/gi, '') // Remove event handlers
    .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, ''); // Remove iframes

  // Trim whitespace and limit length
  sanitized = sanitized.trim().substring(0, maxLength);

  return sanitized;
}

// Validate and sanitize search queries
export function validateSearchQuery(query: string): {
  isValid: boolean;
  sanitizedQuery?: string;
  error?: string;
} {
  if (!query || query.trim().length === 0) {
    return { isValid: false, error: 'Search query cannot be empty' };
  }

  // Remove excessive whitespace and limit length
  const sanitized = sanitizeText(query, 200);

  if (sanitized.length < 2) {
    return {
      isValid: false,
      error: 'Search query must be at least 2 characters',
    };
  }

  // Check for SQL injection patterns
  const sqlPatterns = [
    /('|('')|;|--|\|\|)/i,
    /(union|select|insert|update|delete|drop|create|alter|exec|execute)/i,
    /(\bor\b|\band\b).*[=<>]/i,
  ];

  for (const pattern of sqlPatterns) {
    if (pattern.test(sanitized)) {
      return {
        isValid: false,
        error: 'Search query contains invalid characters',
      };
    }
  }

  return { isValid: true, sanitizedQuery: sanitized };
}

// Validate numeric inputs (serving sizes, quantities, etc.)
export function validateNumber(
  value: string | number,
  min: number = 0,
  max: number = Number.MAX_SAFE_INTEGER,
  allowDecimals: boolean = false
): { isValid: boolean; number?: number; error?: string } {
  let numValue: number;

  if (typeof value === 'string') {
    // Remove any non-numeric characters except decimal point
    const cleaned = value.replace(/[^0-9.-]/g, '');
    numValue = parseFloat(cleaned);
  } else {
    numValue = value;
  }

  if (isNaN(numValue)) {
    return { isValid: false, error: 'Please enter a valid number' };
  }

  if (!allowDecimals && numValue % 1 !== 0) {
    return { isValid: false, error: 'Please enter a whole number' };
  }

  if (numValue < min) {
    return { isValid: false, error: `Number must be at least ${min}` };
  }

  if (numValue > max) {
    return { isValid: false, error: `Number must not exceed ${max}` };
  }

  return { isValid: true, number: numValue };
}

// Validate URLs for safety
export function validateUrl(url: string): { isValid: boolean; error?: string } {
  if (!url || url.trim().length === 0) {
    return { isValid: false, error: 'URL is required' };
  }

  try {
    const urlObj = new URL(url);

    // Only allow https and http protocols
    if (!['https:', 'http:'].includes(urlObj.protocol)) {
      return { isValid: false, error: 'Only HTTP and HTTPS URLs are allowed' };
    }

    // Check for suspicious patterns
    const suspiciousPatterns = [/javascript:/i, /data:/i, /file:/i, /ftp:/i];

    for (const pattern of suspiciousPatterns) {
      if (pattern.test(url)) {
        return { isValid: false, error: 'URL contains invalid protocol' };
      }
    }

    return { isValid: true };
  } catch {
    return { isValid: false, error: 'Please enter a valid URL' };
  }
}

// Validate ingredient text (for AI-generated recipes)
export function validateIngredientText(text: string): {
  isValid: boolean;
  sanitizedText?: string;
  error?: string;
} {
  if (!text || text.trim().length === 0) {
    return { isValid: false, error: 'Ingredient text cannot be empty' };
  }

  const sanitized = sanitizeText(text, 500);

  if (sanitized.length < 3) {
    return {
      isValid: false,
      error: 'Ingredient description must be at least 3 characters',
    };
  }

  // Check for reasonable ingredient format (allow letters, numbers, common punctuation)
  const ingredientRegex = /^[a-zA-Z0-9\s\-.,()\/&%]+$/;

  if (!ingredientRegex.test(sanitized)) {
    return { isValid: false, error: 'Ingredient contains invalid characters' };
  }

  return { isValid: true, sanitizedText: sanitized };
}

// Generic text validation with customizable rules
export function validateGenericText(
  text: string,
  options: {
    required?: boolean;
    minLength?: number;
    maxLength?: number;
    allowHtml?: boolean;
    fieldName?: string;
  } = {}
): { isValid: boolean; sanitizedText?: string; error?: string } {
  const {
    required = true,
    minLength = 0,
    maxLength = 1000,
    allowHtml = false,
    fieldName = 'Text',
  } = options;

  if (!text || text.trim().length === 0) {
    if (required) {
      return { isValid: false, error: `${fieldName} is required` };
    }
    return { isValid: true, sanitizedText: '' };
  }

  let sanitized = allowHtml ? text.trim() : sanitizeText(text, maxLength);

  if (sanitized.length < minLength) {
    return {
      isValid: false,
      error: `${fieldName} must be at least ${minLength} characters`,
    };
  }

  if (sanitized.length > maxLength) {
    return {
      isValid: false,
      error: `${fieldName} must not exceed ${maxLength} characters`,
    };
  }

  return { isValid: true, sanitizedText: sanitized };
}
