// Meal category icon mappings for course type, cuisine type, and prep method
// Using Ionicons icon names for consistency with the app

export interface CategoryIcon {
  name: string;
  color: string;
  label: string;
}

// Course type icons with themed colors
export const getCourseIcon = (course: string | null): CategoryIcon => {
  const courseMap: Record<string, CategoryIcon> = {
    'Main Course': {
      name: 'restaurant',
      color: '#FF6B35', // Orange-red for main dishes
      label: 'Main Course',
    },
    Appetizer: {
      name: 'wine',
      color: '#4ECDC4', // Teal for appetizers
      label: 'Appetizer',
    },
    'Side Dish': {
      name: 'leaf',
      color: '#45B7D1', // Blue for sides
      label: 'Side Dish',
    },
    Dessert: {
      name: 'ice-cream',
      color: '#F7DC6F', // Yellow for desserts
      label: 'Dessert',
    },
    Breakfast: {
      name: 'sunny',
      color: '#F39C12', // Orange for breakfast
      label: 'Breakfast',
    },
    Snack: {
      name: 'fast-food',
      color: '#E74C3C', // Red for snacks
      label: 'Snack',
    },
  };

  return (
    courseMap[course || ''] || {
      name: 'restaurant-outline',
      color: '#95A5A6',
      label: 'Other',
    }
  );
};

// Cuisine type icons with regional colors
export const getCuisineIcon = (cuisine: string | null): CategoryIcon => {
  const cuisineMap: Record<string, CategoryIcon> = {
    American: {
      name: 'flag',
      color: '#3498DB', // Blue
      label: 'American',
    },
    Italian: {
      name: 'pizza',
      color: '#E74C3C', // Red
      label: 'Italian',
    },
    Asian: {
      name: 'fish',
      color: '#F39C12', // Orange
      label: 'Asian',
    },
    Mexican: {
      name: 'flame',
      color: '#E67E22', // Dark orange
      label: 'Mexican',
    },
    French: {
      name: 'wine',
      color: '#9B59B6', // Purple
      label: 'French',
    },
    Mediterranean: {
      name: 'leaf-outline',
      color: '#27AE60', // Green
      label: 'Mediterranean',
    },
    Indian: {
      name: 'flower',
      color: '#F1C40F', // Gold
      label: 'Indian',
    },
  };

  return (
    cuisineMap[cuisine || ''] || {
      name: 'globe-outline',
      color: '#95A5A6',
      label: cuisine || 'World',
    }
  );
};

// Prep method icons with cooking-themed colors
export const getPrepMethodIcon = (prepMethod: string | null): CategoryIcon => {
  const prepMethodMap: Record<string, CategoryIcon> = {
    Baked: {
      name: 'thermometer',
      color: '#E67E22', // Orange
      label: 'Baked',
    },
    Grilled: {
      name: 'flame',
      color: '#E74C3C', // Red
      label: 'Grilled',
    },
    Fried: {
      name: 'water',
      color: '#F39C12', // Gold
      label: 'Fried',
    },
    Sautéed: {
      name: 'flash',
      color: '#3498DB', // Blue
      label: 'Sautéed',
    },
    'Stir-Fried': {
      name: 'flash-outline',
      color: '#9B59B6', // Purple
      label: 'Stir-Fried',
    },
    Steamed: {
      name: 'cloud',
      color: '#1ABC9C', // Turquoise
      label: 'Steamed',
    },
    'No-Cook': {
      name: 'snow',
      color: '#34495E', // Dark blue-gray
      label: 'No-Cook',
    },
    'Slow Cooker': {
      name: 'hourglass',
      color: '#8E44AD', // Dark purple
      label: 'Slow Cook',
    },
    'Instant Pot': {
      name: 'time',
      color: '#E74C3C', // Red
      label: 'Instant Pot',
    },
    'Air Fryer': {
      name: 'radio-outline',
      color: '#16A085', // Teal
      label: 'Air Fryer',
    },
  };

  return (
    prepMethodMap[prepMethod || ''] || {
      name: 'restaurant-outline',
      color: '#95A5A6',
      label: prepMethod || 'Other',
    }
  );
};

// Helper function to get all category icons for a meal
export const getMealCategoryIcons = (meal: {
  course?: string | null | undefined;
  cuisine_type?: string | null | undefined;
  prep_method?: string | null | undefined;
}) => {
  return {
    course: getCourseIcon(meal.course || null),
    cuisine: getCuisineIcon(meal.cuisine_type || null),
    prepMethod: getPrepMethodIcon(meal.prep_method || null),
  };
};
