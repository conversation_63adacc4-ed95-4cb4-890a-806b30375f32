// Utility function to get appropriate emoji for ingredient
export const getIngredientEmoji = (ingredient: string) => {
  const lower = ingredient.toLowerCase();

  // Meat & Seafood
  if (
    lower.includes('chicken') ||
    lower.includes('beef') ||
    lower.includes('pork') ||
    lower.includes('meat') ||
    lower.includes('turkey') ||
    lower.includes('lamb')
  )
    return '🍖';
  if (
    lower.includes('fish') ||
    lower.includes('salmon') ||
    lower.includes('tuna') ||
    lower.includes('seafood')
  )
    return '🐟';

  // Vegetables & Produce
  if (lower.includes('oil') || lower.includes('olive oil')) return '🫒';
  if (lower.includes('pepper') || lower.includes('bell pepper')) return '🌶️';
  if (lower.includes('onion') || lower.includes('green onion')) return '🧅';
  if (lower.includes('garlic')) return '🧄';
  if (lower.includes('ginger')) return '🫚';
  if (lower.includes('tomato')) return '🍅';
  if (lower.includes('carrot')) return '🥕';
  if (lower.includes('potato')) return '🥔';
  if (
    lower.includes('spinach') ||
    lower.includes('lettuce') ||
    lower.includes('salad') ||
    lower.includes('greens')
  )
    return '🥬';
  if (lower.includes('avocado')) return '🥑';
  if (lower.includes('cucumber')) return '🥒';
  if (lower.includes('broccoli')) return '🥦';
  if (lower.includes('mushroom')) return '🍄';
  if (lower.includes('corn')) return '🌽';

  // Herbs & Spices
  if (lower.includes('salt') || lower.includes('pepper')) return '🧂';
  if (
    lower.includes('herb') ||
    lower.includes('basil') ||
    lower.includes('parsley') ||
    lower.includes('cilantro') ||
    lower.includes('oregano') ||
    lower.includes('thyme')
  )
    return '🌿';

  // Condiments & Sauces
  if (lower.includes('honey')) return '🍯';
  if (lower.includes('soy sauce') || lower.includes('sauce')) return '🥢';
  if (lower.includes('vinegar')) return '🍶';

  // Dairy & Eggs
  if (lower.includes('egg')) return '🥚';
  if (lower.includes('cheese')) return '🧀';
  if (lower.includes('milk') || lower.includes('cream')) return '🥛';
  if (lower.includes('butter')) return '🧈';
  if (lower.includes('yogurt')) return '🥛';

  // Grains & Pantry
  if (lower.includes('rice')) return '🍚';
  if (lower.includes('pasta') || lower.includes('noodle')) return '🍝';
  if (lower.includes('bread')) return '🍞';
  if (lower.includes('flour')) return '🌾';
  if (lower.includes('oats') || lower.includes('cereal')) return '🥣';
  if (lower.includes('quinoa')) return '🌾';

  // Fruits
  if (lower.includes('lemon') || lower.includes('lime')) return '🍋';
  if (lower.includes('apple')) return '🍎';
  if (lower.includes('banana')) return '🍌';
  if (lower.includes('orange')) return '🍊';
  if (
    lower.includes('berry') ||
    lower.includes('strawberry') ||
    lower.includes('blueberry')
  )
    return '🍓';

  // Sweeteners
  if (lower.includes('sugar')) return '🍬';
  if (lower.includes('vanilla')) return '🍦';

  // Canned/Jarred items
  if (
    lower.includes('canned') ||
    lower.includes('can') ||
    lower.includes('jar')
  )
    return '🥫';

  return '🥗'; // default fallback
};
