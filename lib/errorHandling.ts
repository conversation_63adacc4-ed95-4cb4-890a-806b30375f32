import { CustomAlert } from '@/components/CustomAlert';

export interface AppError {
  message: string;
  code?: string;
  details?: any;
  isUserError?: boolean;
}

export class UserError extends Error {
  code?: string;
  details?: any;

  constructor(message: string, code?: string, details?: any) {
    super(message);
    this.name = 'UserError';
    this.code = code;
    this.details = details;
  }
}

export class NetworkError extends Error {
  code?: string;
  details?: any;

  constructor(message: string = 'Network connection error', code?: string, details?: any) {
    super(message);
    this.name = 'NetworkError';
    this.code = code;
    this.details = details;
  }
}

export class ValidationError extends Error {
  fields?: Record<string, string>;

  constructor(message: string, fields?: Record<string, string>) {
    super(message);
    this.name = 'ValidationError';
    this.fields = fields;
  }
}

/**
 * Standard error handler for async operations
 */
export async function handleAsync<T>(
  promise: Promise<T>,
  errorMessage?: string
): Promise<[T | null, AppError | null]> {
  try {
    const data = await promise;
    return [data, null];
  } catch (error) {
    const appError = normalizeError(error, errorMessage);
    return [null, appError];
  }
}

/**
 * Normalize various error types into AppError
 */
export function normalizeError(error: any, defaultMessage?: string): AppError {
  // Handle Supabase errors
  if (error?.message && error?.code) {
    return {
      message: error.message,
      code: error.code,
      details: error.details,
      isUserError: true,
    };
  }

  // Handle custom errors
  if (error instanceof UserError) {
    return {
      message: error.message,
      code: error.code,
      details: error.details,
      isUserError: true,
    };
  }

  if (error instanceof NetworkError) {
    return {
      message: 'Network connection issue. Please check your internet connection.',
      code: 'NETWORK_ERROR',
      details: error.details,
      isUserError: true,
    };
  }

  if (error instanceof ValidationError) {
    return {
      message: error.message,
      code: 'VALIDATION_ERROR',
      details: error.fields,
      isUserError: true,
    };
  }

  // Handle generic errors
  if (error instanceof Error) {
    return {
      message: defaultMessage || error.message || 'An unexpected error occurred',
      code: 'UNKNOWN_ERROR',
      details: error,
      isUserError: false,
    };
  }

  // Handle non-Error objects
  return {
    message: defaultMessage || 'An unexpected error occurred',
    code: 'UNKNOWN_ERROR',
    details: error,
    isUserError: false,
  };
}

/**
 * Display error to user using consistent UI
 */
export function displayError(
  error: AppError | Error | string,
  setShowAlert: (show: boolean) => void,
  setAlertConfig: (config: { title: string; message: string }) => void
) {
  let title = 'Error';
  let message = 'An unexpected error occurred';

  if (typeof error === 'string') {
    message = error;
  } else if ('isUserError' in error && error.isUserError) {
    // User-friendly error
    title = error.code === 'VALIDATION_ERROR' ? 'Validation Error' : 'Error';
    message = error.message;
  } else if (error instanceof Error) {
    // Generic error
    message = error.message;
  } else if ('message' in error) {
    message = error.message;
  }

  setAlertConfig({ title, message });
  setShowAlert(true);
}

/**
 * Log error for debugging (in development only)
 */
export function logError(error: any, context?: string) {
  if (__DEV__) {
    console.error(`[${context || 'Error'}]:`, error);
  }
}

/**
 * Common error messages
 */
export const ErrorMessages = {
  NETWORK: 'Network connection error. Please check your internet connection.',
  AUTH_REQUIRED: 'Please sign in to continue.',
  PERMISSION_DENIED: 'You do not have permission to perform this action.',
  NOT_FOUND: 'The requested resource was not found.',
  VALIDATION_FAILED: 'Please check your input and try again.',
  GENERIC: 'An unexpected error occurred. Please try again.',
  SAVE_FAILED: 'Failed to save changes. Please try again.',
  LOAD_FAILED: 'Failed to load data. Please try again.',
  DELETE_FAILED: 'Failed to delete. Please try again.',
} as const;

/**
 * Retry logic for network operations
 */
export async function retryWithBackoff<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  initialDelay: number = 1000
): Promise<T> {
  let lastError: any;
  
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      
      // Don't retry on non-network errors
      if (!(error instanceof NetworkError) && !isNetworkError(error)) {
        throw error;
      }
      
      // Wait before retrying
      if (i < maxRetries - 1) {
        const delay = initialDelay * Math.pow(2, i);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }
  
  throw lastError;
}

/**
 * Check if error is network-related
 */
function isNetworkError(error: any): boolean {
  const networkErrorPatterns = [
    'network',
    'fetch',
    'timeout',
    'connection',
    'ECONNREFUSED',
    'ETIMEDOUT',
  ];
  
  const errorString = error?.message?.toLowerCase() || '';
  return networkErrorPatterns.some(pattern => errorString.includes(pattern));
}