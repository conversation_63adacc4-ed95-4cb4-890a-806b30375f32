import { supabase } from './supabase';
import type { Database } from './database.types';

type Meal = Database['public']['Tables']['meals']['Row'];

// Cache for ingredient parsing
const ingredientCache = new Map<
  string,
  { parsed_name: string; category: string; emoji: string }
>();

/**
 * Get parsed ingredient with caching
 */
export async function getParsedIngredient(originalText: string) {
  // Check local cache first
  if (ingredientCache.has(originalText)) {
    return ingredientCache.get(originalText)!;
  }

  // Check database cache
  const { data, error } = await supabase
    .from('ingredient_cache')
    .select('parsed_name, category, emoji')
    .eq('original_text', originalText)
    .single();

  if (data && !error) {
    ingredientCache.set(originalText, data);
    return data;
  }

  // If not in cache, parse and store
  const { data: parsed } = await supabase
    .rpc('get_parsed_ingredient', { p_original_text: originalText })
    .single();

  if (parsed) {
    // Store in cache for next time
    await supabase
      .from('ingredient_cache')
      .insert({
        original_text: originalText,
        parsed_name: parsed.parsed_name,
        category: parsed.category || null,
        emoji: parsed.emoji || null,
      })
      .select()
      .single();

    ingredientCache.set(originalText, parsed);
    return parsed;
  }

  return { parsed_name: originalText, category: 'uncategorized', emoji: '🥘' };
}

/**
 * Batch fetch meals efficiently
 */
export async function getMealsBatch(mealIds: string[]): Promise<Meal[]> {
  if (mealIds.length === 0) return [];

  const { data, error } = await supabase.rpc('get_meals_batch', {
    p_meal_ids: mealIds,
  });

  if (error) {
    console.error('Error fetching meals batch:', error);
    // Fallback to regular query
    const { data: fallbackData } = await supabase
      .from('meals')
      .select('*')
      .in('id', mealIds);

    return fallbackData || [];
  }

  return (data as unknown as Meal[]) || [];
}

/**
 * Get active meal plan with optimized query
 */
export async function getActiveMealPlan(userId: string, weekStartDate: string) {
  const { data, error } = await supabase
    .from('weekly_meal_plans')
    .select('*')
    .eq('user_id', userId)
    .eq('week_start_date', weekStartDate)
    .eq('is_active', true)
    .is('archived_at', null)
    .single();

  return { data, error };
}

/**
 * Get user preferences with full data
 */
export async function getUserPreferencesOptimized(userId: string) {
  const { data, error } = await supabase
    .from('user_preferences')
    .select(
      `
      *,
      dietary_restrictions,
      allergies,
      cuisine_preferences,
      cooking_skill_level,
      household_size
    `
    )
    .eq('user_id', userId)
    .single();

  return { data, error };
}

/**
 * Batch process ingredients for shopping list
 */
export async function batchProcessIngredients(ingredients: string[]) {
  const results = await Promise.all(
    ingredients.map(async (ingredient) => {
      const parsed = await getParsedIngredient(ingredient);
      return {
        original: ingredient,
        ...parsed,
      };
    })
  );

  return results;
}
