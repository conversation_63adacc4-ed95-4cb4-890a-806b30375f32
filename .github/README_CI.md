# CI/CD Pipeline Documentation

## Overview

This repository uses GitHub Actions for continuous integration and deployment. The pipeline ensures code quality, runs tests, and automates the build process.

## Workflows

### 1. CI/CD Pipeline (`ci.yml`)

**Triggers:**
- Push to `main`, `master`, or `develop` branches
- Pull requests to these branches

**Jobs:**

#### Lint and Type Check
- Runs ESLint for code quality
- Checks TypeScript types
- Validates code formatting with Prettier

#### Run Tests
- Executes Jest test suite
- Generates coverage reports
- Uploads coverage to Codecov (requires `CODECOV_TOKEN` secret)

#### Security Scan
- Runs npm audit for dependency vulnerabilities
- Performs Trivy security scan
- Uploads results to GitHub Security tab

#### Build Expo App
- Only runs on push to main/master branches
- Validates Expo configuration
- Creates preview build
- Stores build artifacts for 7 days

#### Deploy Preview
- Creates preview builds for pull requests
- Comments on PR with build status

#### Quality Gates Check
- Ensures all quality checks pass before merge

### 2. Dependency Update (`dependency-update.yml`)

**Triggers:**
- Weekly on Mondays at 9am UTC
- Manual trigger via workflow dispatch

**Actions:**
- Updates npm dependencies
- Runs security fixes
- Creates PR with changes
- Runs tests to ensure compatibility

## Required Secrets

Add these secrets in GitHub repository settings:

- `EXPO_TOKEN` - For Expo builds and deployments
- `CODECOV_TOKEN` - For coverage reporting (optional)
- `GITHUB_TOKEN` - Automatically provided by GitHub

## Local Testing

Before pushing, run these commands locally:

```bash
# Lint check
npm run lint

# Type check
npm run typecheck

# Format check
npm run format:check

# Run tests
npm test

# Run all checks
npm run lint && npm run typecheck && npm run format:check && npm test
```

## Branch Protection

Recommended branch protection rules for `main`/`master`:

1. Require pull request reviews before merging
2. Require status checks to pass:
   - `lint-and-typecheck`
   - `test`
   - `security-scan`
3. Require branches to be up to date before merging
4. Include administrators in restrictions

## Monitoring

- Check Actions tab for workflow runs
- Review Security tab for vulnerability alerts
- Monitor Codecov for coverage trends
- Review automated dependency PRs weekly

## Troubleshooting

### Common Issues

1. **Build failures**: Check Node version compatibility
2. **Test failures**: Ensure all dependencies are installed
3. **Security scan failures**: Review and update vulnerable dependencies
4. **Expo build errors**: Verify Expo configuration and credentials

### Debug Mode

To enable verbose logging in workflows:

```yaml
env:
  ACTIONS_RUNNER_DEBUG: true
  ACTIONS_STEP_DEBUG: true
```

## Best Practices

1. Always run tests locally before pushing
2. Keep dependencies up to date
3. Address security vulnerabilities promptly
4. Write meaningful commit messages
5. Create descriptive PR titles and descriptions

## Future Enhancements

- [ ] Add E2E testing when React 19 compatibility is resolved
- [ ] Implement automated releases
- [ ] Add performance benchmarking
- [ ] Configure deployment to app stores