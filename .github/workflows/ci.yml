name: CI/CD Pipeline

on:
  push:
    branches: [main, master, develop]
  pull_request:
    branches: [main, master, develop]
  workflow_dispatch:

jobs:
  lint-and-typecheck:
    name: <PERSON><PERSON> and <PERSON> Check
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: Install dependencies
        run: npm install --legacy-peer-deps
        
      - name: Run ESLint
        run: npm run lint
        
      - name: Run TypeScript check
        run: npm run typecheck
        
      - name: Check formatting
        run: npm run format:check

  test:
    name: Run Tests
    runs-on: ubuntu-latest
    needs: lint-and-typecheck
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: Install dependencies
        run: npm install --legacy-peer-deps
        
      - name: Run tests with coverage
        run: npm run test:ci
        
      - name: Upload coverage reports
        uses: codecov/codecov-action@v4
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          fail_ci_if_error: false
          
  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Run npm audit
        run: npm audit --production
        continue-on-error: true
        
      - name: Run security scan
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'
          
      # Code scanning upload skipped for private repos
      # - name: Upload Trivy scan results
      #   uses: github/codeql-action/upload-sarif@v3
      #   if: always()
      #   with:
      #     sarif_file: 'trivy-results.sarif'

  build-expo:
    name: Build Expo App
    runs-on: ubuntu-latest
    needs: [lint-and-typecheck, test]
    if: github.event_name == 'push' && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/master')
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: Setup Expo
        uses: expo/expo-github-action@v8
        with:
          eas-version: latest
          token: ${{ secrets.EXPO_TOKEN }}
          
      - name: Install dependencies
        run: npm install --legacy-peer-deps
        
      - name: Run Expo Doctor
        run: npx expo-doctor
        continue-on-error: true
        
      - name: Build preview
        run: npx expo export --platform all
        
      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: expo-build
          path: dist/
          retention-days: 7

  deploy-preview:
    name: Deploy Preview
    runs-on: ubuntu-latest
    needs: build-expo
    if: github.event_name == 'pull_request'
    
    steps:
      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: expo-build
          path: dist/
          
      - name: Comment PR with preview link
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: '🚀 Preview build ready! Check artifacts for the build.'
            })

  quality-gates:
    name: Quality Gates Check
    runs-on: ubuntu-latest
    needs: [lint-and-typecheck, test, security-scan]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Check quality gates
        run: |
          echo "✅ Linting passed"
          echo "✅ Type checking passed"
          echo "✅ Tests passed"
          echo "✅ Security scan completed"
          echo "🎉 All quality gates passed!"