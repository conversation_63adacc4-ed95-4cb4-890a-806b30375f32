name: Dependency Update

on:
  schedule:
    # Run every Monday at 9am UTC
    - cron: '0 9 * * 1'
  workflow_dispatch:

jobs:
  update-dependencies:
    name: Update Dependencies
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: Update npm dependencies
        run: |
          npm update
          npm audit fix --force || true
          
      - name: Run tests after update
        run: |
          npm ci
          npm test
        continue-on-error: true
        
      - name: Create Pull Request
        uses: peter-evans/create-pull-request@v6
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          commit-message: 'chore: update dependencies'
          title: 'Automated Dependency Updates'
          body: |
            ## Automated Dependency Updates
            
            This PR contains automated dependency updates performed by the CI/CD pipeline.
            
            ### Changes
            - Updated npm dependencies to latest compatible versions
            - Ran security fixes where applicable
            
            ### Checklist
            - [ ] Tests are passing
            - [ ] No breaking changes detected
            - [ ] Security vulnerabilities addressed
            
            Please review and merge if all checks pass.
          branch: automated-dependency-updates
          delete-branch: true