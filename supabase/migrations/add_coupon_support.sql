-- Migration to add coupon code support to affiliate system
-- This replaces the referral link system with Stripe coupon code tracking

-- Add coupon_code column to affiliates table
ALTER TABLE affiliates 
ADD COLUMN coupon_code VARCHAR(50) UNIQUE;

-- Add stripe_promotion_code_id column to affiliate_referrals table to track Stripe promotion codes
ALTER TABLE affiliate_referrals 
ADD COLUMN stripe_promotion_code_id VARCHAR(255),
ADD COLUMN stripe_coupon_id VARCHAR(255),
ADD COLUMN discount_amount DECIMAL(10,2) DEFAULT 0;

-- Update the referral_link column to be nullable since we're using coupon codes now
ALTER TABLE affiliates 
ALTER COLUMN referral_link DROP NOT NULL;

-- Create index for faster coupon code lookups
CREATE INDEX idx_affiliates_coupon_code ON affiliates(coupon_code);
CREATE INDEX idx_affiliate_referrals_stripe_promotion_code ON affiliate_referrals(stripe_promotion_code_id);

-- Add check constraint to ensure either referral_link OR coupon_code is present
ALTER TABLE affiliates 
ADD CONSTRAINT chk_affiliate_tracking_method 
CHECK (referral_link IS NOT NULL OR coupon_code IS NOT NULL);

-- Update the comment on the table to reflect the new functionality
COMMENT ON TABLE affiliates IS 'Affiliate accounts that can earn commissions through referral links or coupon codes';
COMMENT ON COLUMN affiliates.coupon_code IS 'Stripe coupon code for this affiliate (e.g., ROB20)';
COMMENT ON COLUMN affiliate_referrals.stripe_promotion_code_id IS 'Stripe promotion code ID from checkout session';
COMMENT ON COLUMN affiliate_referrals.stripe_coupon_id IS 'Stripe coupon ID from checkout session';
COMMENT ON COLUMN affiliate_referrals.discount_amount IS 'Amount of discount applied by the coupon';