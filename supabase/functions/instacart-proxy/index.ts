// Supabase Edge Function to proxy Instacart API calls securely
// Keeps Instacart API key server-side and adds authentication/rate limiting

import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers':
    'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
};

interface InstacartOrderRequest {
  items: Array<{
    quantity: number;
    item: {
      name: string;
      size?: string;
      image_url?: string;
    };
  }>;
  affiliateData?: {
    affiliateCode: string;
    userId: string;
  };
}

serve(async (req) => {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Verify the request is authenticated
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      return new Response('Unauthorized', {
        status: 401,
        headers: corsHeaders,
      });
    }

    // Initialize Supabase client to verify the user
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseKey = Deno.env.get('SUPABASE_ANON_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseKey, {
      auth: { persistSession: false },
    });

    // Get user from JWT token
    const jwt = authHeader.replace('Bearer ', '');
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser(jwt);

    if (authError || !user) {
      return new Response('Unauthorized', {
        status: 401,
        headers: corsHeaders,
      });
    }

    // Rate limiting: Check user's Instacart orders for the current day
    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();
    const { data: recentOrders } = await supabase
      .from('instacart_orders') // We'll need to create this table
      .select('id')
      .eq('user_id', user.id)
      .gte('created_at', oneDayAgo);

    // Limit to 5 Instacart orders per day per user
    if (recentOrders && recentOrders.length >= 5) {
      return new Response(
        'Daily order limit exceeded. Please try again tomorrow.',
        {
          status: 429,
          headers: corsHeaders,
        }
      );
    }

    // Get Instacart API credentials from environment (server-side only)
    const instacartApiKey = Deno.env.get('INSTACART_API_KEY');
    const instacartApiUrl =
      Deno.env.get('INSTACART_API_URL') ||
      'https://connect.dev.instacart.tools';
    const instacartPartnerId =
      Deno.env.get('INSTACART_PARTNER_ID') || '6099105';

    if (!instacartApiKey) {
      return new Response('Instacart API key not configured', {
        status: 500,
        headers: corsHeaders,
      });
    }

    const url = new URL(req.url);
    // Handle both possible URL patterns: /functions/v1/proxy-instacart and /proxy-instacart
    let endpoint = url.pathname.replace('/functions/v1/proxy-instacart', '');
    if (endpoint === url.pathname) {
      // If no replacement happened, try the shorter pattern
      endpoint = url.pathname.replace('/proxy-instacart', '');
    }

    // Debug logging removed - endpoint routing working correctly

    // Handle different Instacart API endpoints
    if (req.method === 'POST' && endpoint === '/create-order') {
      const body: InstacartOrderRequest = await req.json();

      // Validate request structure
      if (!body.items || !Array.isArray(body.items)) {
        return new Response('Invalid request format', {
          status: 400,
          headers: corsHeaders,
        });
      }

      // Limit number of items per order (increased for meal planning app)
      if (body.items.length > 100) {
        return new Response('Too many items in order', {
          status: 400,
          headers: corsHeaders,
        });
      }

      // Prepare Instacart API request (match working desktop implementation exactly)
      const instacartRequest = {
        title: 'MenuMaker Shopping List',
        link_type: 'shopping_list',
        expires_in: 7,
        line_items: body.items
          .map((item) => {
            // Format display text like desktop version
            const displayText = item.item.size
              ? `${item.quantity} ${item.item.size} ${item.item.name}`.trim()
              : item.quantity > 1
                ? `${item.quantity} ${item.item.name}`.trim()
                : item.item.name.trim();

            // Clean up the ingredient name for better search results (from desktop version)
            let searchName = item.item.name
              // Remove specific brand names
              .replace(/Lawry's|Neufchâtel|Low-Moisture Part Skim Block/gi, '')
              // Remove common descriptors that might limit results
              .replace(
                /(shredded|diced|dried|minced|chopped|sliced|ground|fresh|whole)/gi,
                ''
              )
              // Remove parenthetical notes that might be too specific
              .replace(/\([^)]*\)/g, '')
              // Clean up extra spaces and punctuation
              .replace(/[,;]/g, '')
              .replace(/\s+/g, ' ')
              .trim();

            // If it's a cheese product, add "cheese" if not present
            if (
              /mozzarella|cheddar|parmesan|queso|cotija|mexican.*blend/i.test(
                searchName
              ) &&
              !/cheese/i.test(searchName)
            ) {
              searchName = `${searchName} cheese`;
            }

            // Special case optimizations for common ingredients
            if (searchName.toLowerCase().includes('bell pepper')) {
              searchName = 'bell pepper';
            }
            if (searchName.toLowerCase().includes('olive oil')) {
              searchName = 'olive oil';
            }
            if (searchName.toLowerCase().includes('chicken broth')) {
              searchName = 'chicken broth';
            }
            if (searchName.toLowerCase().includes('jasmine rice')) {
              searchName = 'jasmine rice';
            }

            return {
              name: searchName || item.item.name.trim(), // Fallback to original if cleaning went too far
              display_text: displayText,
              line_item_measurements: [
                {
                  quantity: item.quantity,
                  unit: item.item.size || 'each',
                },
              ],
            };
          })
          .filter((item) => {
            if (!item.name || item.name.length === 0) return false;

            // Filter out water only - assume people have water at home
            const itemNameLower = item.name.toLowerCase().trim();
            return itemNameLower !== 'water';
          }),
        landing_page_configuration: {
          partner_linkback_url: 'https://menumaker.app/dashboard',
          enable_pantry_items: true,
        },
      };

      // Make request to Instacart API (use correct endpoint from desktop version)
      const instacartUrl = `${instacartApiUrl}/idp/v1/products/products_link`;
      // Debug logging removed - API working correctly

      // Add timeout to prevent hanging
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

      const instacartResponse = await fetch(instacartUrl, {
        method: 'POST',
        headers: {
          Accept: 'application/json',
          Authorization: `Bearer ${instacartApiKey}`,
          'Content-Type': 'application/json',
          'X-Partner-ID': instacartPartnerId,
        },
        body: JSON.stringify(instacartRequest),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!instacartResponse.ok) {
        const errorText = await instacartResponse.text();
        console.error('Instacart API error:', {
          status: instacartResponse.status,
          statusText: instacartResponse.statusText,
          error: errorText,
        });
        return new Response('Shopping service temporarily unavailable', {
          status: 503,
          headers: corsHeaders,
        });
      }

      const instacartData = await instacartResponse.json();

      // Log the order for monitoring and affiliate tracking
      await supabase.from('instacart_orders').insert({
        user_id: user.id,
        instacart_order_id: `instacart_recipe_${Date.now()}`,
        items_count: body.items.length, // Use original items count
        affiliate_code: body.affiliateData?.affiliateCode || null,
        created_at: new Date().toISOString(),
      });

      // Transform the response to match our expected format
      const transformedResponse = {
        id: `instacart_recipe_${Date.now()}`, // Generate a unique ID
        url: instacartData.products_link_url || instacartData.url, // Map to expected field name
        partner_user_id: user.id,
        items: body.items, // Return original items format
      };

      // Return the transformed response
      return new Response(JSON.stringify(transformedResponse), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    } else if (req.method === 'GET' && endpoint === '/retailers') {
      // Proxy retailer information requests
      const instacartResponse = await fetch(`${instacartApiUrl}/v2/retailers`, {
        headers: {
          Authorization: `Bearer ${instacartApiKey}`,
        },
      });

      if (!instacartResponse.ok) {
        return new Response('Retailer service unavailable', {
          status: 503,
          headers: corsHeaders,
        });
      }

      const retailersData = await instacartResponse.json();
      return new Response(JSON.stringify(retailersData), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    } else {
      return new Response('Endpoint not found', {
        status: 404,
        headers: corsHeaders,
      });
    }
  } catch (error) {
    console.error('Instacart proxy error:', error);
    return new Response('Internal server error', {
      status: 500,
      headers: corsHeaders,
    });
  }
});
