-- Stripe Connect functions for affiliate payout system
-- These functions handle Connect account creation, onboarding, and payout processing

-- Function to create Stripe Connect account for affiliate
CREATE OR REPLACE FUNCTION create_stripe_connect_account(
  affiliate_user_id UUID,
  email_param VARCHAR(255),
  business_type_param VARCHAR(50) DEFAULT 'individual'
) RETURNS JSON AS $$
DECLARE
  affiliate_record RECORD;
  result JSON;
BEGIN
  -- Get affiliate record
  SELECT * INTO affiliate_record 
  FROM affiliates 
  WHERE user_id = affiliate_user_id 
  AND status = 'active';
  
  IF NOT FOUND THEN
    RETURN json_build_object(
      'success', false,
      'error', 'Active affiliate not found for user'
    );
  END IF;
  
  -- Check if already has Connect account
  IF affiliate_record.stripe_connect_account_id IS NOT NULL THEN
    RETURN json_build_object(
      'success', false,
      'error', 'Affiliate already has Stripe Connect account',
      'account_id', affiliate_record.stripe_connect_account_id
    );
  END IF;
  
  -- This function prepares the data - actual Stripe API call happens in Edge Function
  result := json_build_object(
    'success', true,
    'affiliate_id', affiliate_record.id,
    'email', email_param,
    'business_type', business_type_param,
    'message', 'Ready to create Stripe Connect account'
  );
  
  RETURN result;
  
EXCEPTION WHEN OTHERS THEN
  RETURN json_build_object(
    'success', false,
    'error', 'Database error: ' || SQLERRM
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update affiliate with Stripe Connect account details
CREATE OR REPLACE FUNCTION update_affiliate_connect_account(
  affiliate_id_param UUID,
  stripe_connect_account_id_param VARCHAR(255),
  connect_status_param VARCHAR(50),
  details_submitted_param BOOLEAN DEFAULT FALSE,
  charges_enabled_param BOOLEAN DEFAULT FALSE,
  payouts_enabled_param BOOLEAN DEFAULT FALSE,
  onboard_url_param TEXT DEFAULT NULL
) RETURNS JSON AS $$
DECLARE
  result JSON;
BEGIN
  -- Update affiliate with Connect account details
  UPDATE affiliates 
  SET 
    stripe_connect_account_id = stripe_connect_account_id_param,
    stripe_connect_status = connect_status_param,
    stripe_connect_details_submitted = details_submitted_param,
    stripe_connect_charges_enabled = charges_enabled_param,
    stripe_connect_payouts_enabled = payouts_enabled_param,
    stripe_connect_onboard_url = onboard_url_param,
    stripe_connect_created_at = CASE 
      WHEN stripe_connect_created_at IS NULL THEN NOW() 
      ELSE stripe_connect_created_at 
    END,
    updated_at = NOW()
  WHERE id = affiliate_id_param;
  
  IF NOT FOUND THEN
    RETURN json_build_object(
      'success', false,
      'error', 'Affiliate not found'
    );
  END IF;
  
  result := json_build_object(
    'success', true,
    'affiliate_id', affiliate_id_param,
    'connect_account_id', stripe_connect_account_id_param,
    'status', connect_status_param,
    'message', 'Stripe Connect account updated successfully'
  );
  
  RETURN result;
  
EXCEPTION WHEN OTHERS THEN
  RETURN json_build_object(
    'success', false,
    'error', 'Database error: ' || SQLERRM
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get affiliates ready for payout
CREATE OR REPLACE FUNCTION get_affiliates_ready_for_payout(
  minimum_amount_param DECIMAL(10,2) DEFAULT 25.00
) RETURNS TABLE(
  affiliate_id UUID,
  user_id UUID,
  stripe_connect_account_id VARCHAR(255),
  coupon_code VARCHAR(50),
  total_commission_earned DECIMAL(10,2),
  unpaid_amount DECIMAL(10,2),
  minimum_payout_threshold DECIMAL(10,2),
  last_payout_date TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  WITH unpaid_commissions AS (
    SELECT 
      a.id as affiliate_id,
      a.total_commission_earned - COALESCE(SUM(ap.amount), 0) as unpaid_amount,
      MAX(ap.paid_at) as last_payout_date
    FROM affiliates a
    LEFT JOIN affiliate_payouts ap ON a.id = ap.affiliate_id AND ap.status = 'paid'
    GROUP BY a.id, a.total_commission_earned
  )
  SELECT 
    a.id,
    a.user_id,
    a.stripe_connect_account_id,
    a.coupon_code,
    a.total_commission_earned,
    uc.unpaid_amount,
    a.minimum_payout_threshold,
    uc.last_payout_date
  FROM affiliates a
  JOIN unpaid_commissions uc ON a.id = uc.affiliate_id
  WHERE a.stripe_connect_status = 'active'
    AND a.stripe_connect_payouts_enabled = TRUE
    AND uc.unpaid_amount >= COALESCE(a.minimum_payout_threshold, minimum_amount_param)
    AND a.status = 'active';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to create payout record
CREATE OR REPLACE FUNCTION create_affiliate_payout(
  affiliate_id_param UUID,
  amount_param DECIMAL(10,2),
  period_start_param TIMESTAMP WITH TIME ZONE,
  period_end_param TIMESTAMP WITH TIME ZONE,
  referral_ids_param UUID[] DEFAULT ARRAY[]::UUID[]
) RETURNS JSON AS $$
DECLARE
  new_payout_id UUID;
  result JSON;
BEGIN
  -- Create payout record
  INSERT INTO affiliate_payouts (
    affiliate_id,
    amount,
    period_start,
    period_end,
    referral_ids,
    status,
    payout_method
  ) VALUES (
    affiliate_id_param,
    amount_param,
    period_start_param,
    period_end_param,
    referral_ids_param,
    'pending',
    'stripe_connect'
  ) RETURNING id INTO new_payout_id;
  
  result := json_build_object(
    'success', true,
    'payout_id', new_payout_id,
    'affiliate_id', affiliate_id_param,
    'amount', amount_param,
    'status', 'pending',
    'message', 'Payout record created successfully'
  );
  
  RETURN result;
  
EXCEPTION WHEN OTHERS THEN
  RETURN json_build_object(
    'success', false,
    'error', 'Database error: ' || SQLERRM
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update payout status after Stripe processing
CREATE OR REPLACE FUNCTION update_payout_status(
  payout_id_param UUID,
  status_param VARCHAR(50),
  stripe_connect_transfer_id_param VARCHAR(255) DEFAULT NULL,
  stripe_connect_payout_id_param VARCHAR(255) DEFAULT NULL,
  failure_reason_param TEXT DEFAULT NULL
) RETURNS JSON AS $$
DECLARE
  result JSON;
BEGIN
  -- Update payout record
  UPDATE affiliate_payouts 
  SET 
    status = status_param,
    stripe_connect_transfer_id = stripe_connect_transfer_id_param,
    stripe_connect_payout_id = stripe_connect_payout_id_param,
    failure_reason = failure_reason_param,
    paid_at = CASE WHEN status_param = 'paid' THEN NOW() ELSE paid_at END,
    retry_count = CASE WHEN status_param = 'failed' THEN retry_count + 1 ELSE retry_count END
  WHERE id = payout_id_param;
  
  IF NOT FOUND THEN
    RETURN json_build_object(
      'success', false,
      'error', 'Payout record not found'
    );
  END IF;
  
  result := json_build_object(
    'success', true,
    'payout_id', payout_id_param,
    'status', status_param,
    'message', 'Payout status updated successfully'
  );
  
  RETURN result;
  
EXCEPTION WHEN OTHERS THEN
  RETURN json_build_object(
    'success', false,
    'error', 'Database error: ' || SQLERRM
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;