// Supabase Edge Function to proxy OpenAI API calls securely
// This keeps the OpenAI API key server-side only

import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers':
    'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
};

serve(async (req) => {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Verify the request is authenticated
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      return new Response('Unauthorized', {
        status: 401,
        headers: corsHeaders,
      });
    }

    // Initialize Supabase client to verify the user
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseKey = Deno.env.get('SUPABASE_ANON_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseKey, {
      auth: { persistSession: false },
    });

    // Get user from JWT token
    const jwt = authHeader.replace('Bearer ', '');
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser(jwt);

    if (authError || !user) {
      return new Response('Unauthorized', {
        status: 401,
        headers: corsHeaders,
      });
    }

    // Rate limiting: Check user's AI usage for the current hour
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000).toISOString();
    const { data: recentUsage } = await supabase
      .from('ai_generated_meals')
      .select('id')
      .eq('user_id', user.id)
      .gte('generated_at', oneHourAgo);

    // Limit to 10 AI requests per hour per user
    if (recentUsage && recentUsage.length >= 10) {
      return new Response('Rate limit exceeded. Please try again later.', {
        status: 429,
        headers: corsHeaders,
      });
    }

    // Get request body
    const body = await req.json();

    // Validate request structure
    if (!body.messages || !Array.isArray(body.messages)) {
      return new Response('Invalid request format', {
        status: 400,
        headers: corsHeaders,
      });
    }

    // Get OpenAI API key from environment (server-side only)
    const openaiApiKey = Deno.env.get('OPENAI_API_KEY');
    if (!openaiApiKey) {
      return new Response('OpenAI API key not configured', {
        status: 500,
        headers: corsHeaders,
      });
    }

    // Prepare OpenAI request with safety constraints
    const openaiRequest = {
      model: body.model || 'gpt-4o-mini', // Allow model specification, default to gpt-4o-mini
      messages: body.messages.slice(-10), // Limit conversation history to last 10 messages
      max_tokens: Math.min(body.max_tokens || 1500, 1500), // Limit response length to control costs
      temperature: body.temperature || 0.7,
      // Add safety constraints
      presence_penalty: 0.1,
      frequency_penalty: 0.1,
      // Support tools/function calling if provided
      ...(body.tools && { tools: body.tools }),
      ...(body.tool_choice && { tool_choice: body.tool_choice }),
    };

    // Make request to OpenAI
    const openaiResponse = await fetch(
      'https://api.openai.com/v1/chat/completions',
      {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${openaiApiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(openaiRequest),
      }
    );

    if (!openaiResponse.ok) {
      const errorText = await openaiResponse.text();
      console.error('OpenAI API error:', errorText);
      return new Response('AI service temporarily unavailable', {
        status: 503,
        headers: corsHeaders,
      });
    }

    const openaiData = await openaiResponse.json();

    // Log usage for monitoring (without storing the actual content for privacy)
    await supabase.from('ai_api_usage').insert({
      user_id: user.id,
      model: openaiRequest.model,
      tokens_used: openaiData.usage?.total_tokens || 0,
      cost_estimate: (openaiData.usage?.total_tokens || 0) * 0.00003, // Rough estimate
      created_at: new Date().toISOString(),
    });

    // Return the OpenAI response
    return new Response(JSON.stringify(openaiData), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('OpenAI proxy error:', error);
    return new Response('Internal server error', {
      status: 500,
      headers: corsHeaders,
    });
  }
});
