-- SQL functions for coupon-based affiliate tracking
-- These functions handle attribution when customers use affiliate coupon codes

-- Function to process affiliate referral based on coupon code usage
CREATE OR REPLACE FUNCTION process_affiliate_referral_coupon(
  coupon_code_param VARCHAR(50),
  stripe_subscription_id_param VARCHAR(255),
  stripe_customer_id_param VARCHAR(255),
  stripe_promotion_code_id_param VARCHAR(255),
  stripe_coupon_id_param VARCHAR(255),
  subscription_amount_param DECIMAL(10,2),
  discount_amount_param DECIMAL(10,2),
  commission_amount_param DECIMAL(10,2) DEFAULT 1.00
) RETURNS JSON AS $$
DECLARE
  affiliate_record RECORD;
  new_referral_id UUID;
  result JSON;
BEGIN
  -- Find the affiliate by coupon code
  SELECT * INTO affiliate_record 
  FROM affiliates 
  WHERE coupon_code = coupon_code_param 
  AND status = 'active';
  
  IF NOT FOUND THEN
    RETURN json_build_object(
      'success', false,
      'error', 'Affiliate not found for coupon code: ' || coupon_code_param
    );
  END IF;
  
  -- Check if this subscription is already tracked
  IF EXISTS (
    SELECT 1 FROM affiliate_referrals 
    WHERE stripe_subscription_id = stripe_subscription_id_param
  ) THEN
    RETURN json_build_object(
      'success', false,
      'error', 'Subscription already has affiliate attribution'
    );
  END IF;
  
  -- Create the referral record
  INSERT INTO affiliate_referrals (
    affiliate_id,
    stripe_subscription_id,
    stripe_customer_id,
    stripe_promotion_code_id,
    stripe_coupon_id,
    commission_amount,
    subscription_amount,
    discount_amount,
    status,
    conversion_date
  ) VALUES (
    affiliate_record.id,
    stripe_subscription_id_param,
    stripe_customer_id_param,
    stripe_promotion_code_id_param,
    stripe_coupon_id_param,
    commission_amount_param,
    subscription_amount_param,
    discount_amount_param,
    'confirmed',
    NOW()
  ) RETURNING id INTO new_referral_id;
  
  -- Update affiliate totals
  UPDATE affiliates 
  SET 
    total_referrals = total_referrals + 1,
    total_commission_earned = total_commission_earned + commission_amount_param,
    updated_at = NOW()
  WHERE id = affiliate_record.id;
  
  -- Return success result
  result := json_build_object(
    'success', true,
    'affiliate_id', affiliate_record.id,
    'referral_id', new_referral_id,
    'commission_amount', commission_amount_param,
    'discount_amount', discount_amount_param,
    'message', 'Affiliate referral processed successfully'
  );
  
  RETURN result;
  
EXCEPTION WHEN OTHERS THEN
  RETURN json_build_object(
    'success', false,
    'error', 'Database error: ' || SQLERRM
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to process recurring commission for coupon-based referrals
CREATE OR REPLACE FUNCTION process_recurring_commission_coupon(
  stripe_subscription_id_param VARCHAR(255),
  billing_period_start_param TIMESTAMP WITH TIME ZONE,
  billing_period_end_param TIMESTAMP WITH TIME ZONE
) RETURNS JSON AS $$
DECLARE
  referral_record RECORD;
  result JSON;
BEGIN
  -- Find the referral by subscription ID
  SELECT ar.*, a.commission_rate 
  INTO referral_record
  FROM affiliate_referrals ar
  JOIN affiliates a ON ar.affiliate_id = a.id
  WHERE ar.stripe_subscription_id = stripe_subscription_id_param
  AND ar.status = 'confirmed';
  
  IF NOT FOUND THEN
    RETURN json_build_object(
      'success', false,
      'error', 'No active referral found for subscription: ' || stripe_subscription_id_param
    );
  END IF;
  
  -- Update affiliate commission totals for recurring payment
  UPDATE affiliates 
  SET 
    total_commission_earned = total_commission_earned + referral_record.commission_rate,
    updated_at = NOW()
  WHERE id = referral_record.affiliate_id;
  
  -- Log the recurring commission (you could create a separate table for this)
  -- For now, we'll just return the result
  
  result := json_build_object(
    'success', true,
    'affiliate_id', referral_record.affiliate_id,
    'commission_amount', referral_record.commission_rate,
    'billing_period_start', billing_period_start_param,
    'billing_period_end', billing_period_end_param,
    'message', 'Recurring commission processed successfully'
  );
  
  RETURN result;
  
EXCEPTION WHEN OTHERS THEN
  RETURN json_build_object(
    'success', false,
    'error', 'Database error: ' || SQLERRM
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to create affiliate with coupon code
CREATE OR REPLACE FUNCTION create_affiliate_with_coupon(
  user_email VARCHAR(255),
  coupon_code_param VARCHAR(50),
  commission_rate_param DECIMAL(10,2) DEFAULT 1.00
) RETURNS JSON AS $$
DECLARE
  user_record RECORD;
  new_affiliate_id UUID;
  result JSON;
BEGIN
  -- Find the user by email
  SELECT * INTO user_record 
  FROM auth.users 
  WHERE email = user_email;
  
  IF NOT FOUND THEN
    RETURN json_build_object(
      'success', false,
      'error', 'User not found with email: ' || user_email
    );
  END IF;
  
  -- Check if coupon code is already taken
  IF EXISTS (SELECT 1 FROM affiliates WHERE coupon_code = coupon_code_param) THEN
    RETURN json_build_object(
      'success', false,
      'error', 'Coupon code already exists: ' || coupon_code_param
    );
  END IF;
  
  -- Check if user is already an affiliate
  IF EXISTS (SELECT 1 FROM affiliates WHERE user_id = user_record.id) THEN
    RETURN json_build_object(
      'success', false,
      'error', 'User is already an affiliate'
    );
  END IF;
  
  -- Create the affiliate record
  INSERT INTO affiliates (
    user_id,
    affiliate_code,
    coupon_code,
    commission_rate,
    status,
    total_referrals,
    total_commission_earned
  ) VALUES (
    user_record.id,
    coupon_code_param, -- Use coupon code as affiliate code for consistency
    coupon_code_param,
    commission_rate_param,
    'active',
    0,
    0
  ) RETURNING id INTO new_affiliate_id;
  
  result := json_build_object(
    'success', true,
    'affiliate_id', new_affiliate_id,
    'user_id', user_record.id,
    'coupon_code', coupon_code_param,
    'commission_rate', commission_rate_param,
    'message', 'Affiliate created successfully with coupon code'
  );
  
  RETURN result;
  
EXCEPTION WHEN OTHERS THEN
  RETURN json_build_object(
    'success', false,
    'error', 'Database error: ' || SQLERRM
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get affiliate stats by coupon code
CREATE OR REPLACE FUNCTION get_affiliate_stats_by_coupon(coupon_code_param VARCHAR(50))
RETURNS TABLE(
  affiliate_id UUID,
  user_id UUID,
  coupon_code VARCHAR(50),
  total_referrals INTEGER,
  total_commission_earned DECIMAL(10,2),
  active_referrals INTEGER,
  recent_referrals JSON
) AS $$
BEGIN
  RETURN QUERY
  WITH recent_refs AS (
    SELECT ar.affiliate_id, 
           json_agg(
             json_build_object(
               'id', ar.id,
               'conversion_date', ar.conversion_date,
               'commission_amount', ar.commission_amount,
               'discount_amount', ar.discount_amount,
               'status', ar.status
             ) ORDER BY ar.conversion_date DESC
           ) FILTER (WHERE ar.conversion_date >= NOW() - INTERVAL '30 days') as recent_referrals
    FROM affiliate_referrals ar
    GROUP BY ar.affiliate_id
  )
  SELECT 
    a.id,
    a.user_id,
    a.coupon_code,
    a.total_referrals,
    a.total_commission_earned,
    (SELECT COUNT(*)::INTEGER FROM affiliate_referrals WHERE affiliate_id = a.id AND status = 'confirmed'),
    COALESCE(rr.recent_referrals, '[]'::json)
  FROM affiliates a
  LEFT JOIN recent_refs rr ON a.id = rr.affiliate_id
  WHERE a.coupon_code = coupon_code_param;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;