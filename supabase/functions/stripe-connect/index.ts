// Supabase Edge Function for Stripe Connect operations
// Handles Connect account creation, onboarding, and payout processing
// Deploy with: supabase functions deploy stripe-connect

import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers':
    'authorization, x-client-info, apikey, content-type',
};

interface StripeAccount {
  id: string;
  object: string;
  business_type: string;
  charges_enabled: boolean;
  details_submitted: boolean;
  payouts_enabled: boolean;
  requirements: {
    currently_due: string[];
    eventually_due: string[];
    past_due: string[];
    pending_verification: string[];
  };
}

interface StripeAccountLink {
  object: string;
  created: number;
  expires_at: number;
  url: string;
}

serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Initialize Supabase client
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    // Parse request
    const { action, ...params } = await req.json();

    console.log('Stripe Connect action:', action, params);

    let result;

    switch (action) {
      case 'create_account':
        result = await createConnectAccount(params, supabase);
        break;

      case 'create_account_link':
        result = await createAccountLink(params, supabase);
        break;

      case 'get_account':
        result = await getAccount(params, supabase);
        break;

      case 'process_payout':
        result = await processPayout(params, supabase);
        break;

      case 'get_payout_status':
        result = await getPayoutStatus(params, supabase);
        break;

      default:
        result = { success: false, error: `Unknown action: ${action}` };
    }

    return new Response(JSON.stringify(result), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: result.success ? 200 : 400,
    });
  } catch (error) {
    console.error('Stripe Connect error:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500,
    });
  }
});

async function createConnectAccount(params: any, supabase: any) {
  const {
    affiliate_id,
    email,
    business_type = 'individual',
    country = 'US',
  } = params;

  if (!affiliate_id || !email) {
    return {
      success: false,
      error: 'Missing required parameters: affiliate_id, email',
    };
  }

  try {
    // Create Stripe Connect account
    const stripeResponse = await fetch('https://api.stripe.com/v1/accounts', {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${Deno.env.get('STRIPE_SECRET_KEY')}`,
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        type: 'express',
        country: country,
        email: email,
        business_type: business_type,
        'capabilities[card_payments][requested]': 'true',
        'capabilities[transfers][requested]': 'true',
      }),
    });

    if (!stripeResponse.ok) {
      const error = await stripeResponse.text();
      console.error('Stripe API error:', error);
      return {
        success: false,
        error: 'Failed to create Stripe Connect account',
      };
    }

    const account: StripeAccount = await stripeResponse.json();

    console.log('Created Stripe Connect account:', account.id);

    // Update affiliate in database
    const { data, error } = await supabase.rpc(
      'update_affiliate_connect_account',
      {
        affiliate_id_param: affiliate_id,
        stripe_connect_account_id_param: account.id,
        connect_status_param: account.details_submitted ? 'active' : 'pending',
        details_submitted_param: account.details_submitted,
        charges_enabled_param: account.charges_enabled,
        payouts_enabled_param: account.payouts_enabled,
      }
    );

    if (error) {
      console.error('Database error:', error);
      return { success: false, error: 'Failed to update database' };
    }

    return {
      success: true,
      account_id: account.id,
      status: account.details_submitted ? 'active' : 'pending',
      details_submitted: account.details_submitted,
      charges_enabled: account.charges_enabled,
      payouts_enabled: account.payouts_enabled,
      message: 'Stripe Connect account created successfully',
    };
  } catch (error) {
    console.error('Error creating Connect account:', error);
    return { success: false, error: error.message };
  }
}

async function createAccountLink(params: any, supabase: any) {
  const { account_id, refresh_url, return_url } = params;

  if (!account_id || !refresh_url || !return_url) {
    return {
      success: false,
      error: 'Missing required parameters: account_id, refresh_url, return_url',
    };
  }

  try {
    // Create account link for onboarding
    const stripeResponse = await fetch(
      'https://api.stripe.com/v1/account_links',
      {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${Deno.env.get('STRIPE_SECRET_KEY')}`,
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          account: account_id,
          refresh_url: refresh_url,
          return_url: return_url,
          type: 'account_onboarding',
        }),
      }
    );

    if (!stripeResponse.ok) {
      const error = await stripeResponse.text();
      console.error('Stripe account link error:', error);
      return { success: false, error: 'Failed to create account link' };
    }

    const accountLink: StripeAccountLink = await stripeResponse.json();

    console.log('Created account link for:', account_id);

    return {
      success: true,
      url: accountLink.url,
      expires_at: accountLink.expires_at,
      message: 'Account link created successfully',
    };
  } catch (error) {
    console.error('Error creating account link:', error);
    return { success: false, error: error.message };
  }
}

async function getAccount(params: any, supabase: any) {
  const { account_id } = params;

  if (!account_id) {
    return { success: false, error: 'Missing required parameter: account_id' };
  }

  try {
    // Get account status from Stripe
    const stripeResponse = await fetch(
      `https://api.stripe.com/v1/accounts/${account_id}`,
      {
        headers: {
          Authorization: `Bearer ${Deno.env.get('STRIPE_SECRET_KEY')}`,
        },
      }
    );

    if (!stripeResponse.ok) {
      const error = await stripeResponse.text();
      console.error('Stripe get account error:', error);
      return { success: false, error: 'Failed to get account' };
    }

    const account: StripeAccount = await stripeResponse.json();

    return {
      success: true,
      account_id: account.id,
      business_type: account.business_type,
      charges_enabled: account.charges_enabled,
      details_submitted: account.details_submitted,
      payouts_enabled: account.payouts_enabled,
      requirements: account.requirements,
      message: 'Account retrieved successfully',
    };
  } catch (error) {
    console.error('Error getting account:', error);
    return { success: false, error: error.message };
  }
}

async function processPayout(params: any, supabase: any) {
  const {
    payout_id,
    destination_account_id,
    amount,
    currency = 'usd',
  } = params;

  if (!payout_id || !destination_account_id || !amount) {
    return {
      success: false,
      error:
        'Missing required parameters: payout_id, destination_account_id, amount',
    };
  }

  try {
    // Create transfer to Connect account
    const stripeResponse = await fetch('https://api.stripe.com/v1/transfers', {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${Deno.env.get('STRIPE_SECRET_KEY')}`,
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        amount: Math.round(amount * 100).toString(), // Convert to cents
        currency: currency,
        destination: destination_account_id,
        'metadata[payout_id]': payout_id,
      }),
    });

    if (!stripeResponse.ok) {
      const error = await stripeResponse.text();
      console.error('Stripe transfer error:', error);

      // Update payout as failed
      await supabase.rpc('update_payout_status', {
        payout_id_param: payout_id,
        status_param: 'failed',
        failure_reason_param: error,
      });

      return { success: false, error: 'Failed to process transfer' };
    }

    const transfer = await stripeResponse.json();

    console.log('Created transfer:', transfer.id);

    // Update payout as paid
    const { data, error } = await supabase.rpc('update_payout_status', {
      payout_id_param: payout_id,
      status_param: 'paid',
      stripe_connect_transfer_id_param: transfer.id,
    });

    if (error) {
      console.error('Database error updating payout:', error);
      return {
        success: false,
        error: 'Transfer successful but failed to update database',
      };
    }

    return {
      success: true,
      transfer_id: transfer.id,
      amount: amount,
      currency: currency,
      destination: destination_account_id,
      message: 'Payout processed successfully',
    };
  } catch (error) {
    console.error('Error processing payout:', error);

    // Update payout as failed
    await supabase.rpc('update_payout_status', {
      payout_id_param: payout_id,
      status_param: 'failed',
      failure_reason_param: error.message,
    });

    return { success: false, error: error.message };
  }
}

async function getPayoutStatus(params: any, supabase: any) {
  const { transfer_id } = params;

  if (!transfer_id) {
    return { success: false, error: 'Missing required parameter: transfer_id' };
  }

  try {
    // Get transfer status from Stripe
    const stripeResponse = await fetch(
      `https://api.stripe.com/v1/transfers/${transfer_id}`,
      {
        headers: {
          Authorization: `Bearer ${Deno.env.get('STRIPE_SECRET_KEY')}`,
        },
      }
    );

    if (!stripeResponse.ok) {
      const error = await stripeResponse.text();
      console.error('Stripe get transfer error:', error);
      return { success: false, error: 'Failed to get transfer status' };
    }

    const transfer = await stripeResponse.json();

    return {
      success: true,
      transfer_id: transfer.id,
      amount: transfer.amount / 100, // Convert from cents
      currency: transfer.currency,
      created: transfer.created,
      destination: transfer.destination,
      status: transfer.status || 'pending',
      message: 'Transfer status retrieved successfully',
    };
  } catch (error) {
    console.error('Error getting transfer status:', error);
    return { success: false, error: error.message };
  }
}
