// Supabase Edge Function to handle Stripe webhooks for affiliate attribution
// Deploy with: supabase functions deploy stripe-webhook

import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

// Constant-time comparison to prevent timing attacks
function constantTimeCompare(a: string, b: string): boolean {
  if (a.length !== b.length) {
    return false;
  }

  let result = 0;
  for (let i = 0; i < a.length; i++) {
    result |= a.charCodeAt(i) ^ b.charCodeAt(i);
  }

  return result === 0;
}

// Stripe signature verification for Deno
async function verifyStripeSignature(
  payload: string,
  signature: string,
  secret: string
): Promise<boolean> {
  const elements = signature.split(',');
  let timestamp = '';
  const signatures: string[] = [];

  for (const element of elements) {
    const [key, value] = element.split('=');
    if (key === 't') {
      timestamp = value;
    } else if (key === 'v1') {
      signatures.push(value);
    }
  }

  if (!timestamp || signatures.length === 0) {
    return false;
  }

  // Check timestamp tolerance (5 minutes)
  const timestampTolerance = 300;
  const currentTime = Math.floor(Date.now() / 1000);
  if (currentTime - parseInt(timestamp) > timestampTolerance) {
    return false;
  }

  // Create expected signature
  const signedPayload = timestamp + '.' + payload;
  const key = await crypto.subtle.importKey(
    'raw',
    new TextEncoder().encode(secret),
    { name: 'HMAC', hash: 'SHA-256' },
    false,
    ['sign']
  );

  const expectedSignature = await crypto.subtle.sign(
    'HMAC',
    key,
    new TextEncoder().encode(signedPayload)
  );

  const expectedHex = Array.from(new Uint8Array(expectedSignature))
    .map((b) => b.toString(16).padStart(2, '0'))
    .join('');

  // Verify signature using constant-time comparison to prevent timing attacks
  return signatures.some((sig) => constantTimeCompare(sig, expectedHex));
}

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers':
    'authorization, x-client-info, apikey, content-type, stripe-signature',
};

serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Get webhook signature
    const signature = req.headers.get('stripe-signature');
    if (!signature) {
      return new Response('No signature provided', { status: 400 });
    }

    // Get webhook body
    const body = await req.text();

    // Verify signature
    const webhookSecret = Deno.env.get('STRIPE_SIGNING_SECRET');
    if (!webhookSecret) {
      console.error('STRIPE_SIGNING_SECRET not configured');
      return new Response('Webhook secret not configured', { status: 500 });
    }

    const isValid = await verifyStripeSignature(body, signature, webhookSecret);
    if (!isValid) {
      console.error('Invalid signature');
      return new Response('Invalid signature', { status: 400 });
    }

    console.log('Signature verified successfully');

    // Initialize Supabase client
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    // Parse webhook event
    const event = JSON.parse(body);

    console.log('Processing Stripe webhook:', event.type);

    let result;

    switch (event.type) {
      case 'checkout.session.completed':
        result = await handleCheckoutCompleted(event.data.object, supabase);
        break;

      case 'invoice.payment_succeeded':
        result = await handleRecurringPayment(event.data.object, supabase);
        break;

      case 'customer.subscription.deleted':
        result = await handleSubscriptionCancelled(event.data.object, supabase);
        break;

      default:
        console.log(`Unhandled event type: ${event.type}`);
        result = { success: true, message: 'Event type not handled' };
    }

    return new Response(JSON.stringify(result), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: result.success ? 200 : 500,
    });
  } catch (error) {
    console.error('Webhook error:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500,
    });
  }
});

async function handleCheckoutCompleted(session: any, supabase: any) {
  console.log('Processing checkout completion:', session.id);

  // Check for coupon/promotion code usage
  let couponCode = null;
  let stripePromotionCodeId = null;
  let stripeCouponId = null;
  let discountAmount = 0;

  // Extract discount information from total_details
  if (
    session.total_details?.breakdown?.discounts &&
    session.total_details.breakdown.discounts.length > 0
  ) {
    const discount = session.total_details.breakdown.discounts[0]; // Take first discount
    if (discount.discount) {
      stripeCouponId = discount.discount.coupon?.id;
      stripePromotionCodeId = discount.discount.promotion_code;
      couponCode =
        discount.discount.coupon?.name || discount.discount.coupon?.id;
      discountAmount = discount.amount / 100; // Convert from cents

      console.log('Discount found:', {
        couponCode,
        stripeCouponId,
        stripePromotionCodeId,
        discountAmount,
      });
    }
  }

  // Fallback: check for client_reference_id (old system)
  if (!couponCode && session.client_reference_id) {
    couponCode = session.client_reference_id;
    console.log(
      'Using client_reference_id as coupon code for backwards compatibility:',
      couponCode
    );
  }

  if (!couponCode) {
    console.log(
      'No coupon code or client_reference_id found, skipping affiliate attribution'
    );
    return { success: true, message: 'No affiliate attribution' };
  }

  console.log('Processing affiliate referral for coupon code:', couponCode);

  try {
    const { data, error } = await supabase.rpc(
      'process_affiliate_referral_coupon',
      {
        coupon_code_param: couponCode,
        stripe_subscription_id_param: session.subscription,
        stripe_customer_id_param: session.customer,
        stripe_promotion_code_id_param: stripePromotionCodeId,
        stripe_coupon_id_param: stripeCouponId,
        subscription_amount_param: session.amount_total / 100,
        discount_amount_param: discountAmount,
        commission_amount_param: 1.0,
      }
    );

    if (error) {
      console.error('Error processing affiliate referral:', error);
      return { success: false, error: error.message };
    }

    console.log('Affiliate referral processed:', data);
    return data;
  } catch (error) {
    console.error('Exception processing affiliate referral:', error);
    return { success: false, error: error.message };
  }
}

async function handleRecurringPayment(invoice: any, supabase: any) {
  console.log('Processing recurring payment:', invoice.id);

  // Skip if this is the first invoice
  if (invoice.billing_reason === 'subscription_create') {
    console.log('Skipping initial subscription invoice');
    return { success: true, message: 'Initial invoice skipped' };
  }

  const subscriptionId = invoice.subscription;
  const periodStart = new Date(invoice.period_start * 1000);
  const periodEnd = new Date(invoice.period_end * 1000);

  try {
    const { data, error } = await supabase.rpc(
      'process_recurring_commission_coupon',
      {
        stripe_subscription_id_param: subscriptionId,
        billing_period_start_param: periodStart.toISOString(),
        billing_period_end_param: periodEnd.toISOString(),
      }
    );

    if (error) {
      console.error('Error processing recurring commission:', error);
      return { success: false, error: error.message };
    }

    console.log('Recurring commission processed:', data);
    return data;
  } catch (error) {
    console.error('Exception processing recurring commission:', error);
    return { success: false, error: error.message };
  }
}

async function handleSubscriptionCancelled(subscription: any, supabase: any) {
  console.log('Processing subscription cancellation:', subscription.id);

  try {
    const { data, error } = await supabase
      .from('affiliate_referrals')
      .update({ status: 'cancelled' })
      .eq('stripe_subscription_id', subscription.id)
      .eq('status', 'confirmed');

    if (error) {
      console.error('Error updating cancelled referrals:', error);
      return { success: false, error: error.message };
    }

    console.log('Referrals marked as cancelled:', data);
    return { success: true, message: 'Referrals cancelled' };
  } catch (error) {
    console.error('Exception handling cancellation:', error);
    return { success: false, error: error.message };
  }
}
