# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

node_modules/
.expo/
dist/
npm-debug.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision
*.orig.*
web-build/

# macOS
.DS_Store

# Temporary files created by Metro to check the health of the file watcher
.metro-health-check*

# Environment files
.env*
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.secure

# TypeScript
*.tsbuildinfo

# IDE
.vscode/
.idea/

# Security sensitive files
SECURITY_DEPLOYMENT.md
.mcp.json
*.pem
*.env.backup
secrets/
keys/

# Next.js build files
menumaker_admin/.next/
menumaker_admin/out/
.next/
out/
# @generated expo-cli sync-2b81b286409207a5da26e14c78851eb30d8ccbdb
# The following patterns were generated by expo-cli

expo-env.d.ts
# @end expo-clidatabase_backups/
