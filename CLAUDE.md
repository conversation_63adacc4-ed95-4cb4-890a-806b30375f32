# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a React Native/Expo mobile application called "menumaker_mobile_v2" - a convenience-focused meal planning app that automatically generates weekly meal plans, creates dynamic shopping lists, and integrates with Walmart for seamless grocery shopping. The project uses Expo SDK 53 with TypeScript and is configured with Supabase for backend services.

## Essential Commands

```bash
# Development
npm start          # Start Expo development server
npm run ios        # Run on iOS simulator
npm run android    # Run on Android device/emulator
npm run web        # Run in web browser

# Code Quality
npm run lint       # Run ESLint
npm run typecheck  # Check TypeScript types
npm run format:check # Check Prettier formatting
npm run format:fix # Fix Prettier formatting
```

## Architecture

The app uses Expo Router for file-based navigation:

- `/app/_layout.tsx` - Root layout with Stack navigation and dark theme
- `/app/index.tsx` - Entry point (currently bypassing auth for development)
- `/app/(auth)/` - Authentication screens (unified login/register/forgot-password)
- `/app/(onboarding)/` - 4-screen onboarding flow for user preferences
- `/app/(tabs)/` - Main app with bottom tab navigation
  - `meal-plan.tsx` - Weekly meal overview with featured meal and meal cards
  - `shopping-list.tsx` - Dynamic shopping list with Walmart integration
  - `ai-chat.tsx` - AI Chef conversational interface (floating yellow button)
  - `favorites.tsx` - Saved meals collection
  - `profile.tsx` - User settings and preferences
- `/app/meal-detail.tsx` - Full-screen modal for recipe details with ingredients and instructions
- `/app/ai-meal-detail.tsx` - Dedicated detail page for AI-generated recipes

Key architectural decisions:

- TypeScript with strict mode enabled
- Path alias `@/*` configured for clean imports
- Portrait-only mobile application
- Dark theme throughout with consistent color scheme (#212121 background, #FFE598 accent)
- Supabase integration via MCP server (configured in `.mcp.json`)
- Custom navigation animations and transitions

## Supabase Integration

The project is fully connected to Supabase with:

- **Authentication**: Complete auth flow with email/password signup/login
- **Database**: Clean schema with 8 main tables:
  - `meals` (5437 recipes) - Main recipe database
  - `user_preferences` - User dietary restrictions, household size, etc.
  - `weekly_meal_plans` - Auto-generated weekly meal selections with AI-generated meals support
  - `user_favorites` - User's favorited meals
  - `ai_generated_meals` - AI-created recipes for analysis and user storage
  - `affiliates` - Affiliate partner accounts with commission tracking
  - `affiliate_referrals` - Individual referral records linked to Stripe subscriptions
  - `affiliate_payouts` - Commission payment history
- **TypeScript Types**: Generated types in `lib/database.types.ts`
- **Auth Context**: `contexts/AuthContext.tsx` provides global auth state
- **RLS Policies**: Row Level Security enabled for data isolation

When implementing features:

- Use the `supabase` client from `lib/supabase.ts`
- Import types from `lib/database.types.ts`
- Use `useAuth()` hook for authentication state
- Never commit the `.env` file with credentials

## Recent Fixes & Features (June 2025)

### Meal Plan Persistence Fixed

- **Issue**: Meal plans were regenerating on every login due to concurrent execution and improper week calculation
- **Solution**:
  - Added loading guard with `useRef` to prevent race conditions
  - Fixed week calculation logic to use Friday->Sunday meal plan cycles
  - Improved database queries to check for existing plans by `user_id + week_start_date`
  - Added comprehensive error handling with proper loading state resets

### Smart Meal Filtering Enhanced

- **Issue**: Meal swapping was too restrictive, only finding 19 meals from 5000+ database
- **Solution**:
  - Removed artificial query limits, now processes full database (1000 meal chunks)
  - Added tiered filtering: smart filtering first, then critical-only filtering as fallback
  - Smart filtering considers all user preferences (allergens, dietary restrictions, cooking time, skill level, etc.)
  - Critical filtering only checks safety requirements (allergens + dietary restrictions)
  - Results: 871 compatible meals from 1000, returning top 100 suggestions per swap

### Favorites Page Auto-Refresh

- **Issue**: Favorited meals weren't appearing immediately in favorites tab
- **Solution**: Added `useFocusEffect` to reload favorites when tab comes into focus

### Affiliate Program System (June 2025)

- **Features**: Complete affiliate system with $1.00 monthly commission per active subscription
- **Implementation**:
  - Database schema with 3 tables for affiliate tracking and commission management
  - Profile UI for affiliates to view stats and copy referral links
  - Stripe payment link integration with `client_reference_id` for attribution
  - Supabase Edge Function webhook handler for automatic commission processing
  - Admin scripts for creating affiliate accounts
- **Webhook Integration**:
  - Endpoint: `https://uxcynasomzhnrzxdhydj.supabase.co/functions/v1/stripe-webhook`
  - Handles: `checkout.session.completed`, `invoice.payment_succeeded`, `customer.subscription.deleted`
  - Ready for production deployment

### AI-Generated Meals System (June 2025)

- **Features**: Complete AI recipe generation and management system integrated with meal planning
- **Implementation**:
  - AI Chat interface generates custom recipes based on user preferences and dietary restrictions
  - Dedicated `ai_generated_meals` database table for recipe storage and admin analysis
  - AI meals appear in both shopping list and dedicated meal plan section
  - Custom AI meal detail page (`/ai-meal-detail`) with ingredient emojis and "Add to Cart" functionality
- **Database Schema**:
  - `ai_generated_meals` table: Stores AI recipes with ingredients (array), instructions, times, user context
  - `weekly_meal_plans.ai_generated_meals` field: JSONB array linking AI recipes to weekly plans
- **Workflow**: AI Chat → Generate Recipe → Add to Cart → Shopping List + Meal Plan → Detail View
- **Key Fix**: AI recipes now use actual database IDs instead of temporary IDs like `generated_${timestamp}`, preventing filtering issues in meal plan loading
- **User Experience**:
  - AI recipes clearly distinguished with sparkle icons and #FFE598 accent color
  - Seamless integration with existing meal plan and shopping workflows
  - Users can generate, save, and manage custom AI recipes alongside database meals

### Global Meal Plan Generation (June 2025)

- **Issue Fixed**: Meal plans were regenerating at different times based on user's local timezone
- **Solution**: All meal plan generation now uses UTC time via `getWeekStartDate()` utility
- **Behavior**: All users globally get new meal plans on Friday at midnight UTC
- **Implementation**:
  - Created `lib/weekCalculation.ts` with shared UTC-based week calculation
  - Updated `meal-plan.tsx`, `ai-chat.tsx`, and `meal-detail.tsx` to use this utility
  - Week cycle: Friday 12:00 AM UTC starts new plans for ALL users regardless of timezone

### Key Functions

- `loadWeeklyMeals()` in `meal-plan.tsx` - Handles meal plan persistence and generation
- `getMealSwapSuggestions()` in `lib/mealFiltering.ts` - Smart meal filtering for swaps
- `getFilteredMealsForUser()` in `lib/mealFiltering.ts` - Core filtering logic
- `handleAddGeneratedRecipeToCart()` in `ai-chat.tsx` - Adds AI recipes to meal plans
- `saveGeneratedRecipeToDatabase()` in `ai-chat.tsx` - Stores AI recipes for admin analysis
- `process_affiliate_referral()` - SQL function for processing new referrals from Stripe webhooks
- `process_recurring_commission()` - SQL function for monthly commission tracking
- `getWeekStartDate()` in `lib/weekCalculation.ts` - **UTC-based week calculation for global consistency**
- Logging added throughout for debugging meal plan and filtering issues

## Development Guidelines

When developing features:

1. Follow the existing file-based routing pattern in `/app/`
2. Use TypeScript types for all components and functions
3. Import using the `@/*` path alias for better maintainability
4. The app is mobile-first (portrait only) - design accordingly
5. Use comprehensive logging for debugging complex async operations
6. Implement loading guards with `useRef` for concurrent execution prevention
7. **Always run code quality checks after implementing features:**
   - `npm run lint` - Check for linting errors
   - `npm run typecheck` - Verify TypeScript types
   - `npm run format:check` - Ensure consistent formatting
