{"name": "menumaker_mobile_v2", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "lint": "expo lint", "typecheck": "tsc --noEmit", "format:check": "prettier --check .", "format:fix": "prettier --write .", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-picker/picker": "^2.11.1", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "@stripe/stripe-react-native": "0.45.0", "@supabase/supabase-js": "^2.50.0", "crypto-js": "^4.2.0", "expo": "53.0.20", "expo-blur": "~14.1.5", "expo-clipboard": "~7.1.5", "expo-constants": "~17.1.6", "expo-dev-client": "~5.2.4", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.4.0", "expo-image-picker": "^16.1.4", "expo-linking": "~7.1.7", "expo-router": "~5.1.4", "expo-secure-store": "^14.2.3", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.10", "expo-web-browser": "~14.2.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-pager-view": "6.7.1", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-web": "~0.20.0", "react-native-webview": "^13.13.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^13.2.1", "@types/crypto-js": "^4.2.2", "@types/jest": "^30.0.0", "@types/react": "~19.0.10", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "prettier": "^3.6.2", "react-test-renderer": "^19.1.1", "typescript": "~5.8.3"}, "private": true}