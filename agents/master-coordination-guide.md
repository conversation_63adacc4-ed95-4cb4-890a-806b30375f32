# Master Coordination Guide - 4-Agent Code Review System

## Quick Start Commands

### Terminal Setup

Open 4 terminal windows and run these commands:

**Terminal 1:**

```bash
claude
```

Then paste: `I am Agent 01 - Security & Infrastructure Lead. Please acknowledge and begin the comprehensive Expo app review following my instructions.`

**Terminal 2:**

```bash
claude
```

Then paste: `I am Agent 02 - Code Quality & Architecture Lead. Please acknowledge and begin the comprehensive Expo app review following my instructions.`

**Terminal 3:**

```bash
claude
```

Then paste: `I am Agent 03 - Testing & Quality Assurance Lead. Please acknowledge and begin the comprehensive Expo app review following my instructions.`

**Terminal 4:**

```bash
claude
```

Then paste: `I am Agent 04 - Bug Hunter & Performance Lead. Please acknowledge and begin the comprehensive Expo app review following my instructions.`

## Agent Roles & Responsibilities

### Agent 01 - Security & Infrastructure Lead

- **Primary Focus**: Security vulnerabilities, Supabase security, authentication
- **Key Deliverables**: Security assessment, infrastructure review, vulnerability report
- **Collaboration Role**: Cross-agent coordinator, security guidance provider
- **Status File**: `agent_01_status.md`

### Agent 02 - Code Quality & Architecture Lead

- **Primary Focus**: Code quality, TypeScript usage, architecture assessment
- **Key Deliverables**: Quality metrics, architecture review, technical standards
- **Collaboration Role**: Technical standards coordinator, performance architecture
- **Status File**: `agent_02_status.md`

### Agent 03 - Testing & Quality Assurance Lead

- **Primary Focus**: Test implementation, functional validation, QA processes
- **Key Deliverables**: Test suite, coverage reports, quality validation
- **Collaboration Role**: Quality validation coordinator, test strategy lead
- **Status File**: `agent_03_status.md`

### Agent 04 - Bug Hunter & Performance Lead

- **Primary Focus**: Bug discovery, performance analysis, edge case testing
- **Key Deliverables**: Bug reports, performance metrics, optimization roadmap
- **Collaboration Role**: Issue detection specialist, cross-agent validator
- **Status File**: `agent_04_status.md`

## Coordination Timeline (8-Hour Review)

### Hour 1-2: Setup & Initial Analysis

- **All Agents**: Environment setup, initial codebase analysis
- **Agent 01**: Supabase security review, dependency audit
- **Agent 02**: Project structure analysis, TypeScript review
- **Agent 03**: Test strategy development, framework setup
- **Agent 04**: Performance baseline, profiling setup

### Hour 3-5: Deep Dive Analysis

- **Agent 01**: Authentication security, API security testing
- **Agent 02**: Code quality analysis, architecture assessment
- **Agent 03**: Test implementation, functional validation
- **Agent 04**: Systematic bug hunting, edge case testing

### Hour 6-7: Cross-Validation & Integration

- **Agent 01**: Infrastructure security finalization
- **Agent 02**: Performance architecture review
- **Agent 03**: Test execution and coverage analysis
- **Agent 04**: Performance deep dive, cross-agent validation

### Hour 8: Consolidation & Reporting

- **All Agents**: Final reporting, issue prioritization, production readiness assessment

## Collaboration Protocol

### Status File Updates

Each agent maintains their status file with updates every 2 hours:

- Current progress percentage
- Key findings and issues
- Recommendations for other agents
- Blockers or assistance needed

### Cross-Agent Communication Files

- `security_consult_[agent_number].md` - Security consultations
- `code_quality_consult_[agent_number].md` - Code quality questions
- `test_consult_[agent_number].md` - Testing guidance requests
- `performance_consult_[agent_number].md` - Performance analysis requests

### Priority Escalation System

**P0 (Critical)**: Immediate cross-agent notification

- App crashes, security vulnerabilities, data loss
- Create `CRITICAL_ALERT_[issue_type].md`

**P1 (High)**: Same-day notification via status files

- Major functionality issues, significant performance problems

**P2/P3 (Medium/Low)**: Regular reporting cycle

## Issue Tracking Matrix

### Severity Levels

| Level | Description                     | Examples                           | Response Time  |
| ----- | ------------------------------- | ---------------------------------- | -------------- |
| P0    | Critical - Production Blocking  | Crashes, security holes, data loss | Immediate      |
| P1    | High - Should Fix Before Launch | Major bugs, poor performance       | 24 hours       |
| P2    | Medium - Post-Launch Acceptable | Minor bugs, UI issues              | 1 week         |
| P3    | Low - Enhancement               | Cosmetic issues, optimizations     | Future release |

### Category Classification

- **Security**: Authentication, data protection, API security
- **Functionality**: Core features, user flows, integrations
- **Performance**: Speed, memory, network, battery
- **Quality**: Code maintainability, architecture, testing
- **UX**: Usability, accessibility, cross-platform consistency

## Final Deliverables Checklist

### Individual Agent Reports

- [ ] Agent 01: Security assessment report
- [ ] Agent 02: Code quality and architecture report
- [ ] Agent 03: Testing strategy and validation report
- [ ] Agent 04: Bug discovery and performance analysis report

### Consolidated Outputs

- [ ] Executive summary with production readiness recommendation
- [ ] Prioritized issue list with severity ratings
- [ ] Performance benchmark report
- [ ] Security compliance assessment
- [ ] Technical debt and improvement roadmap

## Production Readiness Criteria

### Must-Fix Before Launch (P0 Items)

- [ ] Zero critical security vulnerabilities
- [ ] Zero production-blocking bugs
- [ ] Performance meets minimum thresholds
- [ ] Authentication and data protection verified
- [ ] Core user journeys fully functional

### Quality Gates

- [ ] > 80% test coverage for critical paths
- [ ] Security assessment: PASS
- [ ] Performance benchmarks: ACCEPTABLE
- [ ] Code quality score: >85/100
- [ ] All P0 and P1 issues resolved

## Post-Review Action Plan Template

### Immediate Actions (Pre-Launch)

1. **Critical Issues**: [List P0 items with owners and deadlines]
2. **Security Fixes**: [Security vulnerabilities to address]
3. **Performance Optimization**: [Critical performance issues]
4. **Testing Gaps**: [Missing test coverage to implement]

### Short-Term Improvements (Next Sprint)

1. **Code Quality**: [Refactoring and improvement tasks]
2. **Performance**: [Optimization opportunities]
3. **Testing**: [Additional test implementation]
4. **Documentation**: [Documentation gaps to fill]

### Long-Term Roadmap (Next Quarter)

1. **Architecture Evolution**: [Structural improvements]
2. **Technical Debt**: [Debt reduction plan]
3. **Scalability**: [Growth preparation tasks]
4. **Process Improvements**: [Development workflow enhancements]

## Success Metrics

### Overall Project Health Score

```
Security Score: [X]/100 (Weight: 25%)
Code Quality Score: [X]/100 (Weight: 25%)
Test Coverage Score: [X]/100 (Weight: 20%)
Performance Score: [X]/100 (Weight: 20%)
Documentation Score: [X]/100 (Weight: 10%)

Overall Health Score: [X]/100
```

### Production Readiness Decision

- **GO**: >85 overall score, zero P0 issues
- **CONDITIONAL GO**: >75 overall score, clear remediation plan
- **NO-GO**: <75 overall score or unresolved P0 issues

## Troubleshooting Common Issues

### Agent Coordination Problems

- Ensure all agents are updating status files regularly
- Check for blocked agents and redistribute work if needed
- Verify cross-agent communication files are being monitored

### Conflicting Findings

- Schedule cross-agent validation sessions
- Document disagreements and seek additional testing
- Escalate architectural decisions to senior technical leadership

### Timeline Slippage

- Prioritize P0 and P1 issues first
- Consider extending timeline for thorough P2/P3 analysis
- Focus on production readiness over perfection

This coordination guide ensures all 4 agents work effectively together to provide you with a comprehensive, actionable assessment of your Expo app's readiness for app store publication.
